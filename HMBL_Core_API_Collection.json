{"info": {"_postman_id": "hmbl-core-api-collection", "name": "HMBL Core - Healthcare Management API", "description": "Complete Postman collection for HMBL Core healthcare management system including Member Diagnoses, Medications, Problems, Programs, and Associated Persons with WHO ICD API and RxNav API integration.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "version": "6.0.0"}, "variable": [{"key": "baseUrl", "value": "http://localhost:8080", "type": "string"}, {"key": "memberID", "value": "123e4567-e89b-12d3-a456-426614174000", "type": "string"}, {"key": "diagnosisID", "value": "123e4567-e89b-12d3-a456-426614174001", "type": "string"}, {"key": "medicationID", "value": "123e4567-e89b-12d3-a456-426614174002", "type": "string"}, {"key": "problemID", "value": "123e4567-e89b-12d3-a456-426614174003", "type": "string"}, {"key": "programID", "value": "123e4567-e89b-12d3-a456-426614174004", "type": "string"}, {"key": "reviewPeriodID", "value": "123e4567-e89b-12d3-a456-426614174005", "type": "string"}, {"key": "programTaskID", "value": "123e4567-e89b-12d3-a456-426614174006", "type": "string"}, {"key": "authToken", "value": "your-jwt-token-here", "type": "string"}, {"key": "whoEntityId", "value": "142601234", "type": "string"}, {"key": "rxcui", "value": "617314", "type": "string"}, {"key": "medicationName", "value": "lisinopril", "type": "string"}, {"key": "associatedPersonID", "value": "123e4567-e89b-12d3-a456-426614174007", "type": "string"}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{authToken}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Set common headers", "pm.request.headers.add({", "    key: 'Content-Type',", "    value: 'application/json'", "});"]}}], "item": [{"name": "WHO ICD API", "description": "WHO International Classification of Diseases API endpoints", "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/whoicd/health", "host": ["{{baseUrl}}"], "path": ["whoicd", "health"]}, "description": "Check WHO ICD API health status"}}, {"name": "Search Diagnoses", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"query\": \"diabetes\",\n  \"language\": \"en\"\n}"}, "url": {"raw": "{{baseUrl}}/whoicd/search", "host": ["{{baseUrl}}"], "path": ["whoicd", "search"]}, "description": "Search for diagnoses using WHO ICD API"}}]}, {"name": "RxNav API", "description": "RxNav API endpoints for medication information", "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/rxnav/health", "host": ["{{baseUrl}}"], "path": ["rxnav", "health"]}, "description": "Check RxNav API health status"}}, {"name": "Search Medications", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"{{<PERSON><PERSON><PERSON>}}\"\n}"}, "url": {"raw": "{{baseUrl}}/rxnav/search", "host": ["{{baseUrl}}"], "path": ["rxnav", "search"]}, "description": "Search for medications using RxNav API"}}]}, {"name": "Member Diagnoses", "description": "Endpoints for managing member diagnoses", "item": [{"name": "Get Member Diagnoses", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/diagnoses", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "diagnoses"]}, "description": "Retrieve all diagnoses for a specific member"}}, {"name": "Create Diagnosis", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"icdCode\": \"E11.9\",\n  \"description\": \"Type 2 diabetes mellitus without complications\",\n  \"diagnosisDate\": \"2025-01-15T00:00:00Z\",\n  \"status\": \"active\",\n  \"severity\": \"moderate\",\n  \"notes\": \"Patient diagnosed with Type 2 diabetes, requires ongoing monitoring\"\n}"}, "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/diagnoses", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "diagnoses"]}, "description": "Create a new diagnosis for a member"}}, {"name": "Update Diagnosis", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"icdCode\": \"E11.9\",\n  \"description\": \"Type 2 diabetes mellitus without complications\",\n  \"diagnosisDate\": \"2025-01-15T00:00:00Z\",\n  \"status\": \"resolved\",\n  \"severity\": \"mild\",\n  \"notes\": \"<PERSON><PERSON>'s diabetes is now well-controlled with medication\"\n}"}, "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/diagnoses/{{diagnosisID}}", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "diagnoses", "{{diagnosisID}}"]}, "description": "Update an existing diagnosis"}}, {"name": "Delete Diagnosis", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/diagnoses/{{diagnosisID}}", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "diagnoses", "{{diagnosisID}}"]}, "description": "Delete a diagnosis"}}]}, {"name": "Member Medications", "description": "Endpoints for managing member medications", "item": [{"name": "Get Member Medications", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/medications", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "medications"]}, "description": "Retrieve all medications for a specific member"}}, {"name": "Create Medication", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"rxcui\": \"{{rxcui}}\",\n  \"name\": \"Lisinopril\",\n  \"dosage\": \"10mg\",\n  \"frequency\": \"once daily\",\n  \"startDate\": \"2025-01-15T00:00:00Z\",\n  \"endDate\": null,\n  \"prescribedBy\": \"<PERSON><PERSON> <PERSON>\",\n  \"status\": \"active\",\n  \"notes\": \"For blood pressure management\"\n}"}, "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/medications", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "medications"]}, "description": "Create a new medication for a member"}}, {"name": "Update Medication", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"rxcui\": \"{{rxcui}}\",\n  \"name\": \"Lisinopril\",\n  \"dosage\": \"20mg\",\n  \"frequency\": \"once daily\",\n  \"startDate\": \"2025-01-15T00:00:00Z\",\n  \"endDate\": null,\n  \"prescribedBy\": \"<PERSON><PERSON> <PERSON>\",\n  \"status\": \"active\",\n  \"notes\": \"<PERSON><PERSON>ge increased for better blood pressure control\"\n}"}, "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/medications/{{medicationID}}", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "medications", "{{medicationID}}"]}, "description": "Update an existing medication"}}, {"name": "Delete Medication", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/medications/{{medicationID}}", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "medications", "{{medicationID}}"]}, "description": "Delete a medication"}}]}, {"name": "Member Problems", "description": "Endpoints for managing member healthcare problems", "item": [{"name": "Get Member Problems", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/problems", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "problems"]}, "description": "Retrieve all problems for a specific member"}}, {"name": "Create Problem", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"clinicalNote\": \"Patient experiencing chronic lower back pain affecting daily activities\",\n  \"status\": \"active\",\n  \"dateIdentified\": \"2025-01-15T00:00:00Z\",\n  \"source\": \"care team\",\n  \"confirmedBy\": \"Dr. <PERSON>, MD\"\n}"}, "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/problems", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "problems"]}, "description": "Create a new problem for a member"}}, {"name": "Update Problem", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"clinicalNote\": \"<PERSON><PERSON>'s chronic lower back pain has improved with physical therapy\",\n  \"status\": \"resolved\",\n  \"dateIdentified\": \"2025-01-15T00:00:00Z\",\n  \"source\": \"care team\",\n  \"confirmedBy\": \"Dr. <PERSON>, MD\"\n}"}, "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/problems/{{problemID}}", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "problems", "{{problemID}}"]}, "description": "Update an existing problem"}}, {"name": "Delete Problem", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/problems/{{problemID}}", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "problems", "{{problemID}}"]}, "description": "Delete a problem"}}]}, {"name": "Member Programs", "description": "Endpoints for managing member healthcare programs with LTSS support", "item": [{"name": "Get Member Programs", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/programs", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "programs"]}, "description": "Retrieve all programs for a specific member"}}, {"name": "Create Program", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"programType\": \"ltss\",\n  \"displayName\": \"Long-Term Services and Supports Program\",\n  \"startDate\": \"2025-01-01T00:00:00Z\",\n  \"endDate\": \"2025-12-31T23:59:59Z\",\n  \"status\": \"active\",\n  \"reviewFrequencyDays\": 90,\n  \"assignedBy\": \"<PERSON><PERSON> <PERSON>\",\n  \"assignedTo\": \"<PERSON>, <PERSON><PERSON>\",\n  \"reviewPeriods\": []\n}"}, "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/programs", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "programs"]}, "description": "Create a new program for a member"}}, {"name": "Update Program", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"programType\": \"ltss\",\n  \"displayName\": \"Updated LTSS Program with Enhanced Mobility Focus\",\n  \"startDate\": \"2025-01-01T00:00:00Z\",\n  \"endDate\": \"2025-12-31T23:59:59Z\",\n  \"status\": \"active\",\n  \"reviewFrequencyDays\": 60,\n  \"assignedBy\": \"<PERSON><PERSON> <PERSON>\",\n  \"assignedTo\": \"<PERSON>, R<PERSON>\"\n}"}, "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/programs/{{programID}}", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "programs", "{{programID}}"]}, "description": "Update an existing program"}}, {"name": "Delete Program", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/programs/{{programID}}", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "programs", "{{programID}}"]}, "description": "Delete a program and all associated data"}}]}, {"name": "Program Tasks", "description": "Endpoints for managing tasks within program review periods", "item": [{"name": "Get Program Tasks", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/programs/{{programID}}/tasks", "host": ["{{baseUrl}}"], "path": ["api", "programs", "{{programID}}", "tasks"]}, "description": "Retrieve all tasks for a specific program"}}, {"name": "Create Program Task", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"taskType\": \"assessment\",\n  \"title\": \"Initial Assessment\",\n  \"assessmentKey\": \"ltss_initial_assessment\",\n  \"reviewKey\": null,\n  \"assignedTo\": \"<PERSON>, R<PERSON>\",\n  \"completedAt\": null,\n  \"dueDate\": \"2025-02-01T00:00:00Z\",\n  \"status\": \"pending\",\n  \"reviewPeriodId\": \"{{reviewPeriodID}}\"\n}"}, "url": {"raw": "{{baseUrl}}/api/programs/{{programID}}/tasks", "host": ["{{baseUrl}}"], "path": ["api", "programs", "{{programID}}", "tasks"]}, "description": "Create a new task within a program"}}, {"name": "Update Program Task", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"taskType\": \"assessment\",\n  \"title\": \"Initial Assessment - Completed\",\n  \"assessmentKey\": \"ltss_initial_assessment\",\n  \"reviewKey\": null,\n  \"assignedTo\": \"<PERSON>, R<PERSON>\",\n  \"completedAt\": \"2025-01-30T14:30:00Z\",\n  \"dueDate\": \"2025-02-01T00:00:00Z\",\n  \"status\": \"completed\",\n  \"reviewPeriodId\": \"{{reviewPeriodID}}\"\n}"}, "url": {"raw": "{{baseUrl}}/api/programs/{{programID}}/tasks/{{programTaskID}}", "host": ["{{baseUrl}}"], "path": ["api", "programs", "{{programID}}", "tasks", "{{programTaskID}}"]}, "description": "Update an existing program task"}}, {"name": "Delete Program Task", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/programs/{{programID}}/tasks/{{programTaskID}}", "host": ["{{baseUrl}}"], "path": ["api", "programs", "{{programID}}", "tasks", "{{programTaskID}}"]}, "description": "Delete a program task"}}]}, {"name": "Review Periods", "description": "Endpoints for managing review periods within programs with outcome tracking", "item": [{"name": "Get Review Periods", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/programs/{{programID}}/periods", "host": ["{{baseUrl}}"], "path": ["api", "programs", "{{programID}}", "periods"]}, "description": "Retrieve all review periods for a specific program"}}, {"name": "Create Review Period", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"periodNumber\": 1,\n  \"startDate\": \"2025-01-01T00:00:00Z\",\n  \"endDate\": \"2025-03-31T23:59:59Z\",\n  \"status\": \"active\",\n  \"outcomeStatus\": null,\n  \"outcomeDescription\": null,\n  \"tasks\": [\n    {\n      \"taskType\": \"assessment\",\n      \"title\": \"Initial Functional Assessment\",\n      \"assessmentKey\": \"ltss_functional_assessment\",\n      \"reviewKey\": null,\n      \"assignedTo\": \"<PERSON>, R<PERSON>\",\n      \"completedAt\": null,\n      \"dueDate\": \"2025-01-15T00:00:00Z\",\n      \"status\": \"pending\"\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/api/programs/{{programID}}/periods", "host": ["{{baseUrl}}"], "path": ["api", "programs", "{{programID}}", "periods"]}, "description": "Create a new review period for a program"}}, {"name": "Update Review Period", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"periodNumber\": 1,\n  \"startDate\": \"2025-01-01T00:00:00Z\",\n  \"endDate\": \"2025-03-31T23:59:59Z\",\n  \"status\": \"complete\",\n  \"outcomeStatus\": \"successful\",\n  \"outcomeDescription\": \"Member successfully completed all assessments and showed significant improvement in functional independence.\"\n}"}, "url": {"raw": "{{baseUrl}}/api/programs/{{programID}}/periods/{{reviewPeriodID}}", "host": ["{{baseUrl}}"], "path": ["api", "programs", "{{programID}}", "periods", "{{reviewPeriodID}}"]}, "description": "Update a review period, including marking as complete with outcome tracking"}}, {"name": "Delete Review Period", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/programs/{{programID}}/periods/{{reviewPeriodID}}", "host": ["{{baseUrl}}"], "path": ["api", "programs", "{{programID}}", "periods", "{{reviewPeriodID}}"]}, "description": "Delete a review period and all associated tasks"}}]}, {"name": "Associated Persons", "description": "Endpoints for managing member associated persons (emergency contacts, caregivers, etc.)", "item": [{"name": "Get Member Associated Persons", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/associated-persons", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "associated-persons"]}, "description": "Retrieve all associated persons for a specific member"}}, {"name": "Get Specific Associated Person", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/associated-persons/{{associatedPersonID}}", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "associated-persons", "{{associatedPersonID}}"]}, "description": "Retrieve a specific associated person by ID"}}, {"name": "Create Associated Person", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"fullName\": \"<PERSON>\",\n  \"relationship\": \"<PERSON>\",\n  \"role\": \"emergency_contact\",\n  \"phone\": \"******-123-4567\",\n  \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/associated-persons", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "associated-persons"]}, "description": "Create a new associated person for a member. Role options: emergency_contact, informal_caregiver, formal_caregiver, legal_guardian, navigator, family_member, friend, healthcare_provider, social_worker, other"}}, {"name": "Update Associated Person", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"fullName\": \"<PERSON>\",\n  \"relationship\": \"<PERSON>\",\n  \"role\": \"emergency_contact\",\n  \"phone\": \"******-123-4567\",\n  \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/associated-persons/{{associatedPersonID}}", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "associated-persons", "{{associatedPersonID}}"]}, "description": "Update an existing associated person"}}, {"name": "Delete Associated Person", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/associated-persons/{{associatedPersonID}}", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "associated-persons", "{{associatedPersonID}}"]}, "description": "Delete an associated person"}}, {"name": "Create Caregiver Example", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"fullName\": \"<PERSON>\",\n  \"relationship\": \"Home Health Aide\",\n  \"role\": \"formal_caregiver\",\n  \"phone\": \"+1-************\",\n  \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/associated-persons", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "associated-persons"]}, "description": "Example: Create a formal caregiver associated person"}}, {"name": "Create Legal Guardian Example", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"fullName\": \"<PERSON>\",\n  \"relationship\": \"Legal Guardian\",\n  \"role\": \"legal_guardian\",\n  \"phone\": \"+1-************\",\n  \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/associated-persons", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "associated-persons"]}, "description": "Example: Create a legal guardian associated person"}}, {"name": "Create Healthcare Provider Example", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"fullName\": \"<PERSON>. <PERSON>\",\n  \"relationship\": \"Primary Care Physician\",\n  \"role\": \"healthcare_provider\",\n  \"phone\": \"+1-************\",\n  \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/associated-persons", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "associated-persons"]}, "description": "Example: Create a healthcare provider associated person"}}]}, {"name": "Program Timeline", "description": "Endpoints for fetching timeline items related to program activities", "item": [{"name": "Get Program Timeline", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/programs/{{programID}}/timeline", "host": ["{{baseUrl}}"], "path": ["api", "programs", "{{programID}}", "timeline"]}, "description": "Retrieve all timeline items for a specific program, including program, review period, and task activities"}}]}]}