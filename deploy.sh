#!/bin/bash
#Set Variables
containerName="donahealth-stg"
ecrURI="628326973807.dkr.ecr.us-east-1.amazonaws.com/${containerName}"
YourAccessKey="********************"
YourSecretKey="jyx8OAGsqJaD80IQf+SM1XkH50B8AYuPsdCfxI1v"
YourRegion="us-east-1"

echo "Step 1. Setup AWS Config"

aws configure set aws_access_key_id "********************" --profile default
aws configure set aws_secret_access_key "jyx8OAGsqJaD80IQf+SM1XkH50B8AYuPsdCfxI1v" --profile default
aws configure set region "us-east-1" --profile default

echo "Step 2. AWS ECR Login"
aws ecr get-login-password --region "us-east-1" | docker login --username AWS --password-stdin $ecrURI


echo "Step 3. Build Docker Image"
 docker build \
--build-arg AWS_RDS_HOST="vapor-database-stg.c4thw8cgiooh.us-east-1.rds.amazonaws.com" \
--build-arg AWS_RDS_PORT=5432 \
--build-arg AWS_RDS_USER="kingpin" \
--build-arg AWS_RDS_PASS="qVgbwqmQESgX2pP85k_y^P8=*" \
--build-arg AWS_RDS_DB="donahealthstaging" \
-t "${containerName}:latest" -f Dockerfile .

echo "Step 4. Set tag on Docker Image"
docker tag "${containerName}:latest" "${ecrURI}:latest"

echo "Step 5. Push Docker Image to AWS ECR"
# docker push "vapor-app-image:latest"
docker push "${ecrURI}:latest"
