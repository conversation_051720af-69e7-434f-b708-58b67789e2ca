//
//  ProgramTimelineService.swift
//  
//
//  Created by Augment Agent on 2025-06-27.
//

import Foundation
import Fluent
import Vapor

/// Service for creating timeline entries for Program-related operations
struct ProgramTimelineService {
    
    /// Creates a timeline entry for Program operations
    static func createProgramTimeline(
        operation: TimelineOperation,
        program: Program,
        on db: Database,
        creatorID: UUID,
        memberID: UUID? = nil
    ) async throws {
        let programID = try program.requireID()

        let timelineItem = TimelineItem(
            carepackageID: "program-\(programID.uuidString)",
            status: operation.status,
            desc: operation.description(for: "Program", details: "\(program.displayName) - \(program.programType)"),
            title: operation.title(for: "Program"),
            memberId: memberID ?? program.$member.id,
            visible: true,
            meta: MetaData(data: [
                "ref_id": programID.uuidString,
                "entity_type": "Program",
                "operation": operation.rawValue,
                "program_type": program.programType
            ])
        )

        timelineItem.$program.id = programID
        timelineItem.$creator.id = creatorID
        try await timelineItem.save(on: db)
    }
    
    /// Creates a timeline entry for ReviewPeriod operations
    static func createReviewPeriodTimeline(
        operation: TimelineOperation,
        reviewPeriod: ReviewPeriod,
        on db: Database,
        creatorID: UUID,
        memberID: UUID? = nil
    ) async throws {
        let reviewPeriodID = try reviewPeriod.requireID()
        let programID = reviewPeriod.$program.id

        let timelineItem = TimelineItem(
            carepackageID: "program-\(programID.uuidString)",
            status: operation.status,
            desc: operation.description(for: "Review Period", details: "Period \(reviewPeriod.periodNumber) - \(reviewPeriod.status)"),
            title: operation.title(for: "Review Period"),
            memberId: memberID,
            visible: true,
            meta: MetaData(data: [
                "ref_id": reviewPeriodID.uuidString,
                "entity_type": "ReviewPeriod",
                "operation": operation.rawValue,
                "program_id": programID.uuidString,
                "period_number": String(reviewPeriod.periodNumber)
            ])
        )

        timelineItem.$program.id = programID
        timelineItem.$creator.id = creatorID
        try await timelineItem.save(on: db)
    }
    
    /// Creates a timeline entry for ProgramTask operations
    static func createProgramTaskTimeline(
        operation: TimelineOperation,
        task: ProgramTask,
        on db: Database,
        creatorID: UUID,
        memberID: UUID? = nil
    ) async throws {
        let taskID = try task.requireID()
        let reviewPeriodID = task.$reviewPeriod.id
        
        // Get the program ID through the review period
        guard let reviewPeriod = try await ReviewPeriod.find(reviewPeriodID, on: db) else {
            throw Abort(.badRequest, reason: "Review period not found for task")
        }
        let programID = reviewPeriod.$program.id

        let timelineItem = TimelineItem(
            carepackageID: "program-\(programID.uuidString)",
            status: operation.status,
            desc: operation.description(for: "Program Task", details: "\(task.title) - \(task.taskType)"),
            title: operation.title(for: "Program Task"),
            memberId: memberID,
            visible: true,
            meta: MetaData(data: [
                "ref_id": taskID.uuidString,
                "entity_type": "ProgramTask",
                "operation": operation.rawValue,
                "program_id": programID.uuidString,
                "review_period_id": reviewPeriodID.uuidString,
                "task_type": task.taskType
            ])
        )

        timelineItem.$program.id = programID
        timelineItem.$creator.id = creatorID
        try await timelineItem.save(on: db)
    }
}
