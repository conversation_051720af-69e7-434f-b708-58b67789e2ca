//
//  SMSReminderMonitoring.swift
//  
//
//  Created by <PERSON> on 7/25/25.
//

import Foundation
import Vapor
import SotoCore
import SotoCloudWatch

// MARK: - SMS Reminder Monitoring Service

public class SMSReminderMonitoring {
    private let cloudWatch: CloudWatch?
    private let logger: Logger
    private let namespace: String
    private var metrics: SMSReminderMetrics
    
    init(logger: Logger, client: AWSClient? = nil) {
        self.logger = logger
        self.cloudWatch = client != nil ? CloudWatch(client: client!, region: .useast1) : nil
        self.namespace = Environment.get("SMS_REMINDER_CLOUDWATCH_NAMESPACE") ?? "HMBL/SMSReminders"
        self.metrics = SMSReminderMetrics()
    }
    
    // MARK: - Metrics Recording
    
    func recordReminderScheduled(entityType: SMSReminderPayload.ReminderType) {
        metrics.recordScheduled()
        
        // Send to CloudWatch
        sendMetricToCloudWatch(
            metricName: "RemindersScheduled",
            value: 1,
            unit: .count,
            dimensions: [
                CloudWatch.Dimension(name: "EntityType", value: entityType.rawValue)
            ]
        )
        
        logger.info("SMS reminder scheduled", metadata: [
            "entity_type": .string(entityType.rawValue),
            "total_scheduled": .string(String(metrics.scheduledCount))
        ])
    }
    
    func recordReminderCancelled(entityType: SMSReminderPayload.ReminderType) {
        metrics.recordCancelled()
        
        // Send to CloudWatch
        sendMetricToCloudWatch(
            metricName: "RemindersCancelled",
            value: 1,
            unit: .count,
            dimensions: [
                CloudWatch.Dimension(name: "EntityType", value: entityType.rawValue)
            ]
        )
        
        logger.info("SMS reminder cancelled", metadata: [
            "entity_type": .string(entityType.rawValue),
            "total_cancelled": .string(String(metrics.cancelledCount))
        ])
    }
    
    func recordReminderError(entityType: SMSReminderPayload.ReminderType, error: Error) {
        metrics.recordError()
        
        // Send to CloudWatch
        sendMetricToCloudWatch(
            metricName: "ReminderErrors",
            value: 1,
            unit: .count,
            dimensions: [
                CloudWatch.Dimension(name: "EntityType", value: entityType.rawValue),
                CloudWatch.Dimension(name: "ErrorType", value: String(describing: type(of: error)))
            ]
        )
        
        logger.error("SMS reminder error", metadata: [
            "entity_type": .string(entityType.rawValue),
            "error": .string(error.localizedDescription),
            "total_errors": .string(String(metrics.errorCount))
        ])
    }
    
    func recordSuccessRate() {
        let successRate = metrics.successRate
        
        // Send to CloudWatch
        sendMetricToCloudWatch(
            metricName: "SuccessRate",
            value: successRate,
            unit: .percent
        )
        
        logger.info("SMS reminder success rate", metadata: [
            "success_rate": .string(String(format: "%.2f%%", successRate)),
            "scheduled": .string(String(metrics.scheduledCount)),
            "errors": .string(String(metrics.errorCount))
        ])
    }
    
    // MARK: - Health Checks
    
    func performHealthCheck(on eventLoop: EventLoop) -> EventLoopFuture<HealthCheckResult> {
        let promise = eventLoop.makePromise(of: HealthCheckResult.self)
        
        Task {
            var result = HealthCheckResult()
            
            // Check AWS connectivity
            result.awsConnectivity = await checkAWSConnectivity()
            
            // Check Lambda function
            result.lambdaFunction = await checkLambdaFunction()
            
            // Check error rate
            result.errorRate = checkErrorRate()
            
            // Check configuration
            result.configuration = checkConfiguration()
            
            // Overall health
            result.overallHealth = result.awsConnectivity && 
                                 result.lambdaFunction && 
                                 result.errorRate && 
                                 result.configuration
            
            promise.succeed(result)
        }
        
        return promise.futureResult
    }
    
    // MARK: - Alerting
    
    func checkAndSendAlerts() {
        // Check error rate threshold
        if metrics.errorCount > 0 && metrics.scheduledCount > 0 {
            let errorRate = Double(metrics.errorCount) / Double(metrics.scheduledCount) * 100
            
            if errorRate > 10.0 { // Alert if error rate > 10%
                sendAlert(
                    severity: .high,
                    message: "High SMS reminder error rate: \(String(format: "%.2f", errorRate))%",
                    details: [
                        "scheduled": String(metrics.scheduledCount),
                        "errors": String(metrics.errorCount),
                        "time_window": "1 hour"
                    ]
                )
            }
        }
        
        // Check if no reminders scheduled in the last hour (potential issue)
        let timeSinceReset = Date().timeIntervalSince(metrics.lastResetTime)
        if timeSinceReset > 3600 && metrics.scheduledCount == 0 { // 1 hour
            sendAlert(
                severity: .medium,
                message: "No SMS reminders scheduled in the last hour",
                details: [
                    "time_since_last_reminder": String(Int(timeSinceReset / 60)) + " minutes"
                ]
            )
        }
    }
    
    // MARK: - Private Methods
    
    private func sendMetricToCloudWatch(
        metricName: String,
        value: Double,
        unit: CloudWatch.StandardUnit,
        dimensions: [CloudWatch.Dimension] = []
    ) {
        guard let cloudWatch = cloudWatch else { return }
        
        let metricData = CloudWatch.MetricDatum(
            dimensions: dimensions.isEmpty ? nil : dimensions,
            metricName: metricName,
            timestamp: Date(),
            unit: unit,
            value: value
        )
        
        let putMetricDataInput = CloudWatch.PutMetricDataInput(
            metricData: [metricData],
            namespace: namespace
        )
        
        Task {
            do {
                _ = try await cloudWatch.putMetricData(putMetricDataInput)
            } catch {
                logger.error("Failed to send metric to CloudWatch: \(error)")
            }
        }
    }
    
    private func checkAWSConnectivity() async -> Bool {
        guard let cloudWatch = cloudWatch else { return false }
        
        do {
            _ = try await cloudWatch.listMetrics(CloudWatch.ListMetricsInput(namespace: namespace))
            return true
        } catch {
            logger.error("AWS connectivity check failed: \(error)")
            return false
        }
    }
    
    private func checkLambdaFunction() async -> Bool {
        // This would require Lambda client to check function status
        // For now, we'll check if the ARN is configured
        let lambdaArn = Environment.get("SMS_REMINDER_LAMBDA_ARN") ?? ""
        return !lambdaArn.isEmpty && lambdaArn.contains("lambda")
    }
    
    private func checkErrorRate() -> Bool {
        guard metrics.scheduledCount > 0 else { return true }
        
        let errorRate = Double(metrics.errorCount) / Double(metrics.scheduledCount) * 100
        return errorRate < 5.0 // Consider healthy if error rate < 5%
    }
    
    private func checkConfiguration() -> Bool {
        let requiredEnvVars = [
            "SMS_REMINDER_LAMBDA_ARN",
            "SMS_REMINDER_EXECUTION_ROLE_ARN",
            "SMS_REMINDER_SCHEDULE_GROUP",
            "TWILIO_ACCOUNT_SID",
            "TWILIO_TOKEN",
            "TWILIO_PHONE_NUMBER"
        ]
        
        return requiredEnvVars.allSatisfy { envVar in
            let value = Environment.get(envVar) ?? ""
            return !value.isEmpty
        }
    }
    
    private func sendAlert(severity: AlertSeverity, message: String, details: [String: String] = [:]) {
        logger.critical("SMS Reminder Alert", metadata: [
            "severity": .string(severity.rawValue),
            "message": .string(message),
            "details": .string(details.description)
        ])
        
        // In a production environment, you would send this to:
        // - SNS topic for email/SMS alerts
        // - Slack webhook
        // - PagerDuty
        // - etc.
    }
    
    // MARK: - Metrics Reset
    
    func resetMetrics() {
        // Log final metrics before reset
        logger.info("Resetting SMS reminder metrics", metadata: [
            "scheduled": .string(String(metrics.scheduledCount)),
            "cancelled": .string(String(metrics.cancelledCount)),
            "errors": .string(String(metrics.errorCount)),
            "success_rate": .string(String(format: "%.2f%%", metrics.successRate))
        ])
        
        metrics.reset()
    }
}

// MARK: - Supporting Types

struct HealthCheckResult {
    var awsConnectivity: Bool = false
    var lambdaFunction: Bool = false
    var errorRate: Bool = false
    var configuration: Bool = false
    var overallHealth: Bool = false
    
    var description: String {
        return """
        SMS Reminder System Health Check:
        - AWS Connectivity: \(awsConnectivity ? "✅" : "❌")
        - Lambda Function: \(lambdaFunction ? "✅" : "❌")
        - Error Rate: \(errorRate ? "✅" : "❌")
        - Configuration: \(configuration ? "✅" : "❌")
        - Overall Health: \(overallHealth ? "✅ HEALTHY" : "❌ UNHEALTHY")
        """
    }
}

enum AlertSeverity: String {
    case low = "LOW"
    case medium = "MEDIUM"
    case high = "HIGH"
    case critical = "CRITICAL"
}

// MARK: - Application Extension

public extension Application {
    var smsReminderMonitoring: SMSReminderMonitoring {
        return SMSReminderMonitoring(logger: self.logger, client: self.aws.client)
    }
}

public extension Request {
    var smsReminderMonitoring: SMSReminderMonitoring {
        return SMSReminderMonitoring(logger: self.logger, client: self.application.aws.client)
    }
}
