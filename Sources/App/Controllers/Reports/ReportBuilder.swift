//
//  ReportBuilder.swift
//
//
//  Created by <PERSON> on 1/18/24.
//

import Foundation
import Vapor
import Fluent
import Leaf
import SwiftCSV


struct ReportBuilder {
    
//    func memberStartAndLatest(req: Request, id: String) throws -> EventLoopFuture<String> {
//        let org = "f7f9780d-4fb1-44e7-9965-93a6f584d6ee"
//        return Member.query(on: req.db)
//            .join(parent: \.$org)
//            .filter(Organization.self, \.$id == UUID(uuidString: org)!)
//            .with(\.$address)
//            .all().flatMap { members in
//                let string = members.map({CSVCreator().memberRowWithAddress(member: $0)}).joined()
//                return try! saveCSV(req: req, headers: CSVCreator().memberHeadersWithAddress(), fileName: "members_gis", rows: string).transform(to: string)
//            }
//    }
    
    
    func buildGenesisGISData(req: Request, id: String) throws -> EventLoopFuture<String> {
        let org = "f7f9780d-4fb1-44e7-9965-93a6f584d6ee"
        return Member.query(on: req.db)
            .join(parent: \.$org)
            .filter(Organization.self, \.$id == UUID(uuidString: org)!)
            .with(\.$address)
            .all().flatMap { members in
                let string = members.map({CSVCreator().memberRowWithAddress(member: $0)}).joined()
                return try! saveCSV(req: req, headers: MemberWithAddress.headers(), fileName: "members_gis", rows: string).transform(to: string)
            }
    }
    
    func buildGenesis(req: Request, id: String) throws -> EventLoopFuture<String> {
        let org = "f7f9780d-4fb1-44e7-9965-93a6f584d6ee"
        let endDateString = "2024-03-28"
        let startDateString = "2024-02-29"
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        
        guard let start = dateFormatter.date(from: startDateString),
              let end = dateFormatter.date(from: endDateString) else {
            throw Abort(.badRequest, reason: "Invalid date format")
        }
        
        return try! carepackageIds(req: req, start: start, end: end, org: org).flatMap { ids in
            return try! carepackageItems(req: req, start: start, end:end, carepackageIds: ids).flatMap({ items in
                let string = items.map({CSVCreator().packageItemRow(item:$0)}).joined()
                return try! saveCSV(req: req, headers: CSVCreator().carepackageItemHeaders(), fileName: "refferals", rows: string).flatMap({ status in
                    return Survey.query(on: req.db)
                        .filter(\.$orgID == org)
                        .filter(\.$status == "complete")
                        .filter(\.$createdAt >= start)
                        .filter(\.$createdAt <= end)
                        .group(.or) { group in
                            group.filter(\.$key == "rapid_hrp").filter(\.$key == "full_hrp")
                        }.all()
                        .flatMap({ assessments in
                            let string = assessments.map({CSVCreator().surveyRow(survey: $0)}).joined()
                            return try! saveCSV(req: req, headers: CSVCreator().surveyHeaders(), fileName: "assessments", rows: string).flatMap({ status in
                                return Household.query(on: req.db)
                                    .filter(\.$householdScore != nil)
                                    .with(\.$address)
                                    .filter(\.$createdAt >= start)
                                    .filter(\.$createdAt <= end)
                                    .all().flatMap { households in
                                        let string = households.map({CSVCreator().householdRow(household: $0)}).joined()
                                        return try! saveCSV(req: req, headers: CSVCreator().householdHeaders(), fileName: "households", rows: string).flatMap({ status in
                                            return Survey.query(on: req.db)
                                                .filter(\.$orgID == org)
                                                .filter(\.$status == "complete")
                                                .filter(\.$key == "healthy_home_checklist")
                                            
                                                .filter(\.$createdAt >= start)
                                                .filter(\.$createdAt <= end)
                                                .all().flatMap { homeSurveys in
                                                    let string = homeSurveys.map({CSVCreator().surveyRow(survey: $0)}).joined()
                                                    return try! saveCSV(req: req, headers: CSVCreator().surveyHeaders(), fileName: "health_homes_survey", rows: string).flatMap({ status in
                                                        return Member.query(on: req.db)
                                                            .join(parent: \.$org)
                                                            .filter(Organization.self, \.$id == UUID(uuidString: org)!)
                                                            .filter(\.$createdAt >= start)
                                                            .filter(\.$createdAt <= end)
                                                            .all().flatMap { members in
                                                                let string = members.map({CSVCreator().memberRow(member: $0)}).joined()
                                                                return try! saveCSV(req: req, headers: CSVCreator().memberHeaders(), fileName: "members", rows: string).transform(to: string)
                                                            }
                                                    })
                                                }
                                        })
                                    }
                            })
                        })
                })
            })
        }
        
        //
        //
        //        var json:[String: Any] = [:]
        //        //        5b7ed772-68bf-48c9-b956-d1b5cd1470d9
        //        return try! self.orgLookup(req: req, id: "5b7ed772-68bf-48c9-b956-d1b5cd1470d9").flatMap { org in
        //            let orgId = "5b7ed772-68bf-48c9-b956-d1b5cd1470d9"
        //            json["name"] = org.title
        //
        //            var networks:[String: Any] = [:]
        //            for network in org.networks {
        //                networks[network.name] = network.services.count
        //                //                print("\(network.name) as \(network.services.count) services")
        //            }
        //            json["networks"] = networks
        //
        //
        //            let referrals = org.packages.compactMap({$0.items}).reduce([], +).filter({$0.status == "booked"})
        //
        //            json["referrals"] = referrals.count
        //
        //            let inactive = org.members.filter({$0.status == "inactive"})
        //            //            print("referrals: \(referrals.count)")
        //            //            print("Participant retention: \(inactive.count)")
        //            json["retention"] = inactive.count
        //
        //            return Survey.query(on: req.db)
        //                .filter(\.$orgID == orgId)
        //                .filter(\.$status == "complete")
        //                .group(.or) { group in
        //                    group.filter(\.$key == "rapid_hrp").filter(\.$key == "full_hrp")
        //                }.all().flatMap({ assessments in
        //                    print("total Assessments: \(assessments.count)")
        //                    json["total assessments"] = assessments.count
        //                    let baselines = assessments.filter({$0.isBaseline() && $0.firstSurvey == 1})
        //
        //                    json["rapid baselines"] = baselines.count
        //
        //                    let totalHRP = assessments.filter({$0.isFullHRP()})
        //                    json["totalHRP"] = totalHRP.count
        //                    let baselineHrp = assessments.filter({$0.isFullHRP() && $0.firstSurvey == 1})
        //                    json["baselineHrp"] = baselineHrp.count
        //                    let latestHrp = assessments.filter({$0.isFullHRP() && $0.lastSurvey == 1})
        //                    json["latestHrp"] = latestHrp.count
        //                    print("total baselines Rapid: \(baselines.count)")
        //                    print("total baselines HRP: \(baselineHrp.count)")
        //                    print("total Latest HRP: \(latestHrp.count)")
        //                    print("total Total HRP: \(totalHRP.count)")
        //
        //                    return Household.query(on: req.db)
        //                        .filter(\.$householdScore != nil)
        //                        .with(\.$address)
        //                        .all().flatMap { households in
        //                            let totalHRP = households.filter({Double($0.householdScore ?? "0") ?? 0 <= 60})
        //                            json["household<60"] = totalHRP.count
        //
        //                            let safetyHomes = households.filter({Double($0.householdScore ?? "0") ?? 0 >= 60})
        //                            json["household>60"] = safetyHomes.count
        //                            print("% households in each safety category: \(safetyHomes.count)")
        //
        //
        //
        //                            return Household.query(on: req.db)
        //                                .with(\.$address).all().flatMap { households in
        //                                    let groupedByZipCode = Dictionary(grouping: households) { parent in
        //                                        return parent.address.map { $0.zip }
        //                                    }
        //                                    print("Household zips: \(groupedByZipCode.count)")
        //                                    json["Householdbyzips"] = groupedByZipCode.count
        //                                    return Survey.query(on: req.db)
        //                                        .filter(\.$orgID == orgId)
        //                                        .filter(\.$status == "complete")
        //                                        .filter(\.$key == "healthy_home_checklist")
        //                                        .all().flatMap { homeSurveys in
        //                                            print("Health Homes Scores: \(homeSurveys.count)")
        //                                            json["Health Homes Scores"] = homeSurveys.count
        //                                            let response = ClientResponse(status: .ok, headers: [:], body: ByteBuffer(data: try! JSONSerialization.data(withJSONObject: json)))
        //
        //                                            return req.eventLoop.future(response)
        //                                        }
        //                                }
        //                        }
        //                })
        //        }
        
    }
    
    func saveCSV(req: Request, headers: String, fileName: String, rows: String) throws -> EventLoopFuture<HTTPStatus> {
        let fileNameWithTime = "\(fileName)_\(Int(Date().timeIntervalSince1970)).csv"
        let path = self.rootDirectory(app: req.application).appendingPathComponent(fileNameWithTime)
        let csvText = "\(headers)\(rows)"
        return req.fileio.writeFile(ByteBuffer(string: csvText), at: path.relativePath).transform(to: HTTPStatus.accepted)
    }
    
    
    fileprivate func rootDirectory(app:Application) -> URL {
        return URL(fileURLWithPath: app.directory.workingDirectory +  "Sources/App/csv/", isDirectory: true)
    }
    
    
    fileprivate func carepackageItems(req: Request, start: Date, end:Date, carepackageIds:[UUID]) throws -> EventLoopFuture<[CarePackageItem]> {
        return CarePackageItem.query(on: req.db)
            .with(\.$network)
            .filter(\.$createdAt >= start)
            .filter(\.$createdAt <= end)
            .join(parent: \.$carepackage)
            .filter(CarePackage.self, \.$id ~~ carepackageIds)
            .all()
    }
    
    fileprivate func carepackageIds(req: Request, start: Date, end:Date, org: String) throws -> EventLoopFuture<[UUID]> {
        return CarePackage.query(on: req.db)
            .join(parent: \.$org)
            .filter(Organization.self, \.$id == UUID(uuidString: org)!)
            .all().map { users in
                return users.map { $0.id! }
            }
    }
    
    fileprivate func orgLookup(req: Request, id: String) throws -> EventLoopFuture<Organization> {
        guard let networkID = UUID(id) else { throw NetworkError.error(type: .organization) }
        return Organization.query(on: req.db).filter(\.$id == networkID)
            .with(\.$users)
            .with(\.$attachments)
            .with(\.$address)
            .with(\.$phones)
            .with(\.$members)
            .with(\.$teams)
            .with(\.$packages, { package in
                package.with(\.$items, { item in
                    item.with(\.$items)
                    item.with(\.$appointments)
                })
            })
            .with(\.$networks, { network in
                network.with(\.$services)
            })
            .with(\.$services)
            .with(\.$households)
            .with(\.$chats).first().flatMapThrowing { org in
                guard let foundModel = org else { throw NetworkError.error(type: .organization) }
                return foundModel
            }
    }
    
}
