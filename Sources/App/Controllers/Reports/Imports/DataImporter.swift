//
//  File.swift
//  
//
//  Created by <PERSON> on 2/21/24.
//

import Foundation
import Vapor
import Fluent
import SwiftCSV

struct CamullusDataImporter {
    
    static let orgID = "09659eaf-82f2-4e29-8b1e-4b6bdc505d9b"
    
    static func importData(req: Request) throws -> EventLoopFuture<Void> {
        let filePath = req.application.directory.workingDirectory + "Public/data/camillus/member.csv"
        let csvFile: CSV = try CSV<Named>(url: URL(fileURLWithPath: filePath))
        return csvFile.rows.sequencedFlatMapEach(on: req.eventLoop) { json in
            print(json)
            let groupId = json["Group ID"] ?? UUID().uuidString.replacingOccurrences(of: "-", with: "")
            let firstName = json["First Name"] ?? ""
            let lastName = json["Last Name"] ?? ""
            let fullName = "\(firstName)\(lastN<PERSON>)".lowercased()
            let email = "\(fullName)@example.com".replacingOccurrences(of: " ", with: "").lowercased()
            let dob = json["Date of Birth"] ?? "1971-01-01"
            let gender = json["gender"]
            
            let street = json["Address 1"] ?? ""
            let street2 = json["Address 2"] ?? ""
            let bed = json["Bed"] ?? ""
            let city = json["City"] ?? ""
            let state = json["State"] ?? ""
            let zipcode = json["Zip"] ?? ""
            let refId = json["ID"]
            
            let address = Address(street: street, street2: "\(street2) \(bed)", city: city, state: state, zip: zipcode, country: "US", county: "US", kind: "main")
            let householdaddress = Address(street: street, street2: "\(street2) \(bed)", city: city, state: state, zip: zipcode, country: "US", county: "US", kind: "main")
            
            return try! OrgsController.find(req: req, id: CamullusDataImporter.orgID).flatMap { orgs in
                
                return Member.query(on: req.db).filter(\.$email == email)
                    .with(\.$address)
                    .with(\.$households)
                    .first()
                    .flatMap { foundMember in
                    if let foundMember {
                        return try! CamullusDataImporter.household(req: req, groupId: groupId, createdMember: foundMember, address: householdaddress, org: orgs)
                    } else {
                        let member = Member(email: email,
                                            firstName: firstName,
                                            lastName: lastName,
                                            type: "adult",
                                            roles: ["clients"],
                                            dob: dob,
                                            gender: gender,
                                            status: "active",
                                            refId: refId)
                        return try! CamullusDataImporter.memberCreate(req: req, member: member, address: address, groupId: groupId, org: orgs, householdAddress: householdaddress)
                    }
                }
            }
        }
    }
    
    static func memberCreate(req: Request, member: Member, address: Address, groupId: String, org: Organization, householdAddress: Address)throws -> EventLoopFuture<Void> {
        return org.$members.create(member, on: req.db).transform(to: member).flatMap { createMember in
            return createMember.$address.create([address], on: req.db).transform(to: createMember).flatMap { createdMember in
                return try! CamullusDataImporter.household(req: req, groupId: groupId, createdMember: createdMember, address: householdAddress, org: org)
            }
        }
    }
    
    static func household(req: Request, groupId: String, createdMember: Member, address: Address, org: Organization) throws -> EventLoopFuture<Void> {
        return Household.query(on: req.db).filter(\.$title == groupId)
            .with(\.$address)
            .with(\.$members)
            .first().flatMap { household in
            if let household {
                return household.$members.attach([createdMember], on: req.db)
            } else {
                //create the household
                let house = Household(title: groupId, type: "household", kind: "household")
                return org.$households.create(house, on: req.db).transform(to: house)
                    .flatMap { household in
                        return Household.query(on: req.db).filter(\.$id == household.id!)
                            .with(\.$address)
                            .with(\.$members)
                            .first().flatMap { household in
                                if let household {
                                    return household.$address.create(address, on: req.db).transform(to: household).flatMap { createdHouse in
                                        return createdHouse.$members.attach([createdMember], on: req.db)
                                    }
                                } else {
                                    return req.eventLoop.future()
                                }
                            }
                }
                
            }
        }
    }
}


struct DataImporter {
    
    enum NetworkStatus: String {
        case accepting
        case notaccepting
    }
    
    static let orgID = "fc312a94-9382-4909-8e08-7c8076ff9f68"
    
    static func importData(req: Request) throws -> EventLoopFuture<Void> {
//        return req.eventLoop.future()
        do {
            let filePath = req.application.directory.workingDirectory + "Public/data/import/networks.csv"
            let csvFile: CSV = try CSV<Named>(url: URL(fileURLWithPath: filePath))
            return csvFile.rows.sequencedFlatMapEach(on: req.eventLoop) { item in
                return try! importNetworks(req: req, item: item)
//                return try! importMembers(req: req, item: item)
            }
        } catch let parseError as CSVParseError {
            print(parseError.localizedDescription)
            return req.eventLoop.future()
        } catch {
            return req.eventLoop.future()
        }
    }
        
    static func importMembers(req: Request, item:[String:String]) throws -> EventLoopFuture<Void> {
        let memberType = "adult" //all the same
        let active = "active"
        let inactive = "inactive"
        let eng = "en-us"
        let spanish = "es"
        let gender = "female"
        
        var language = item["Language"] ?? ""
        language = language.lowercased() ==  "spanish" ? spanish : eng
        
        var status = item["Active"] ?? ""
        status = status.lowercased() ==  "no" ? inactive : active
                        
        
        let input = MembersCreateInput(orgID: orgID,
                           email: item["Email"] ?? "",
                           firstName: item["First Name"] ?? "",
                           lastName: item["Last Name"] ?? "",
                           type: memberType,
                           roles: ["client"],
                           dob: item["DOB"] ?? "",
                           gender: gender,
                           ethnicity: item["Ethnicity"]?.lowercased() ?? "",
                           lang: language,
                           referredBy: item["Referred by"] ?? "",
                           status: status,
                            phone: item["Phone Number"] ?? "",
                           refId: item["Member ID"] ?? "")
        
        return try! createMember(req: req, memberInput: input, item: item).flatMap { member in
            
            let addressInput = createAddrssIfNeed(item: item)
            let memberId = member.id?.uuidString ?? ""
            
            return try! createHousehold(req: req, input: HouseholdInput(orgID: orgID,
                                                            title: item["Title"] ?? "",
                                                            type: "household",
                                                            kind: "household",
                                                            members: [memberId],
                                                            headOfHouseID: memberId,
                                                                        address: addressInput)).flatMap({ _ in
                return req.eventLoop.future()
            })
        }
    }
    
    static func createMember(req: Request, memberInput: MembersCreateInput, item:[String:String]) throws -> EventLoopFuture<Member> {
        var input = memberInput
        input = input.cleanUp()
        let member = input.member()
        
        return try! OrgsController.find(req: req, id: input.orgID).flatMap { org in
                        
            return org.$members.create(member, on: req.db).transform(to: member).flatMap { orgMember in
                
                return try! MembersController.find(req: req, id: orgMember.id?.uuidString ?? "").flatMap({ foundMember in
                    
                    return try! createAddrssFrom(req: req, item: item, model: foundMember).flatMap({ _ in
                       
                        if let phone = createPhoneIfNeed(item: item) {
                            
                            return foundMember.$phones.create([phone], on: req.db).transform(to: foundMember)
                            
                        } else {
                            return req.eventLoop.future(foundMember)
                        }
                        
                    })
                })
            }
        }
    }
    
    static func createHousehold(req: Request, input: HouseholdInput) throws -> EventLoopFuture<Household> {
        let house = input.minHousehold()
        return try! OrgsController.find(req: req, id: orgID).flatMap { org in
            return org.$households.create(house, on: req.db).transform(to: house).flatMap { household in
                
                return try! HouseholdsController.query(req: req, id: household.id?.uuidString ?? "").flatMap({ householdUpdate in
                    
                    let household = input.returnUpdatedModel(household: householdUpdate)
                    
                    return try! HouseholdsController().updateHouseholdIfNeeded(req: req, input: input, household: household)
                    
                })
            }
        }
    }
    
    static func importNetworks(req: Request, item:[String:String]) throws -> EventLoopFuture<Void> {
        if  let name = item["name"],
                !name.isEmpty {
            let type = item["type"]?.lowercased() ?? "health"
            let contactName = item["contact"]?.lowercased() ?? ""
            
            
            let network = Network(name: name,
                                  types:[type],
                                  status:NetworkStatus.accepting.rawValue,
                                  contact:contactName,
                                  memberBook: false)
//            print("\(network.name) | \(network.types.last ?? "-") | \(network.contact ?? "*")")
            
            return try! OrgsController.find(req: req, id: orgID).flatMap { org in
                
                return org.$networks.create(network, on: req.db).transform(to: network).flatMap { net in
                                                            
                    if let addressInput = createAddrssIfNeed(item: item) {
                        
                        return try! NetworksController.query(req: req, id: net.id?.uuidString ?? "").flatMap({  netItem in
                            
                            return try! AddressService.updateAddressIfNeeded(req: req, input: addressInput, model: netItem).flatMap { _ in
                                
                                if let phone = createPhoneIfNeed(item: item) {
                                    
                                    return netItem.$phones.create([phone], on: req.db)
                                    
                                } else {
                                    return req.eventLoop.future()
                                }
                            }
                        })
                    } else {
                        return req.eventLoop.future()
                    }
                }
            }
        } else {
            return req.eventLoop.future()
        }
    }
    
    static func createPhoneIfNeed(item:[String:String]) -> PhoneNumber? {
        if let phone = item["phone"], !phone.isEmpty {
            return PhoneNumber(label: "main", number: phone)
        } else {
            return nil
        }
    }
    
    static func createAddrssFrom(req:Request, item:[String:String], model: any Model) throws -> EventLoopFuture<Void> {
        let input = createAddrssIfNeed(item: item)
        if let addressInput = input {
            return try! AddressService.updateAddressIfNeeded(req: req, input: addressInput, model: model)
        } else {
            return req.eventLoop.future()
        }
    }
    
    static func createAddrssIfNeed(item:[String:String]) -> AddressInput? {
        if let street1 = item["street1"], !street1.isEmpty,
            let city = item["city"], !city.isEmpty,
            let state = item["state"], !state.isEmpty,
            let zip = item["zip"], !zip.isEmpty {
            let street2 = item["street2"]

            let input = AddressInput(
                street:street1,
                street2:street2,
                city:city,
                state:state,
                zip:zip,
                kind:"main")
             return input
        } else {
            return nil
        }
    }
}




