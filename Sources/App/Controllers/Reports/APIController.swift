//
//  File.swift
//  
//
//  Created by <PERSON> on 5/9/23.
//

import Foundation
import Vapor
import Fluent
import Leaf
import SwiftCSV
import SotoSNS

struct ReportRequest: Content {
    let orgId: String
    let start: String //yyyy-mm-dd
    let end: String //yyyy-mm-dd
}

struct ReportBarChart: Encodable {
    var title: String
    var percentage:Double
    var leftOver:Double
}

struct ReportContext: Encodable {
    var totalMembers: Int
    var totalNavs:Int
    var totalTeams:Int
    var totalCarePacks:Int
    var totalSurveys:Int
    var chartItem:[ReportBarChart]
}

struct EHRContext: Encodable {
    var dashboard: Dashboard
    var layout:String
    var memberURL:String
}

struct APIInput: Content {
    var memberID:String
    var layout:String //full | minimal | graphical
    var kind: String //ehr / reprot
}

//final class CSVInput: Content {
//    var startDate:String
//    var endDate:String
//    var orgID:String?
//    var deIdentified: String?
//}


final class CSVInput: Content {
    var startDate:String
    var endDate:String
    var report:ReportCreator = .none
    var deIdentified: String?
    
    func combineDate() -> String {
        return "\(startDate)-\(endDate)"
    }
}

struct Report:Content {
    var dashboard: Dashboard
}


struct APIController: RouteCollection {
    
    func boot(routes: RoutesBuilder) throws {
        let users = routes.grouped("api")
        users.post(use: create)
        users.post([":userID", "report"], use: lookup)
        users.get(["ehr",":userID"], use: templates)
        users.get(["import"], use: dataImport)
        users.post(["dashboard"], use: dashboard)
        users.post(["dashboard", "export"], use: export)
        users.post(["dashboard", "exportAllData"], use: exportAllData)
        users.post(["pushService"], use: pushTest)
        users.post(["report"], use: createCSV)
        users.get(["genesis"], use: buildGenesis)
        users.get(["genesisGIS"], use: genesisGIS)
                
        users.post(["empowered"], use: empowered)
        users.post(["custom"], use: custom)
        users.post("apiManual", use: apiManual)
        
    }
    
    func apiManual(req: Request) throws -> EventLoopFuture<HTTPStatus> {
        let input = try req.content.decode(ReportInput.self)
       
       let dateFormatter = DateFormatter()
       dateFormatter.dateFormat = "yyyy-MM-dd"
        req.logger.info("Kicking off job by formatting.")
       guard let start = dateFormatter.date(from: input.startDate),
             let end = dateFormatter.date(from: input.endDate) else {
           return req.eventLoop.future(.badRequest)
       }
       
       do {
           let jsonData = try JSONEncoder().encode(input)
           let buffer = ByteBuffer(data: jsonData)
           print("encoding.")
           req.logger.info("job encoding.")
           
           var headers = HTTPHeaders()
           headers.add(name: .contentType, value: "application/json")
           
           let request = Request(application: req.application, headers: headers, collectedBody: buffer, on: req.eventLoop)                      
       
           let report = ExportData(model: ReportQuery(start: start, end: end, org: input.org, input: input),
                                   config: ReportCreator.configuration(type: .genesis))
           
           return try! report.exportData(req: request).flatMap { response in
               print(response)
               return req.eventLoop.future(.accepted)
           }
       }
       catch {
           req.logger.info("Failed to encode")
           return req.eventLoop.makeFailedFuture(error)
       }
    }
    
    func custom(req: Request) throws -> EventLoopFuture<ClientResponse> {
        let input = try req.content.decode(ReportRequest.self)
        return Member.query(on: req.db)
//            .filter(\.$status == "active")
            .group(.or) { orGroup in
                orGroup.filter(\.$unenrolledDate == "")
                orGroup.filter(\.$unenrolledDate == nil)
            }
            .with(\.$phones)
            .with(\.$households, { house in
                house.with(\.$address)
                house.with(\.$teams)
            })
            .filter(\.$org.$id == UUID(input.orgId)!)
            .all().flatMap { members in
                let rows = members.map({CSVCreator().memberRowWithAddressAnTeamAndPhone(member: $0)}).joined()
                let csvText = "\(MemberWithAddressWithTeamWithPhoneNumber.headers())\(rows)"
                let fileName = "custom_members"
                return try! ReportGenerator().generateLocalCSV(fileName: fileName, csvText: csvText, req: req, isEmpty: false).flatMap({ response in
                    return try! exportInsurances(req: req, members: members).transform(to: ClientResponse(status: .accepted))
                })
            }
    }
    
    
    fileprivate func exportInsurances(req: Request, members: [Member]) throws -> EventLoopFuture<[URL]> {
        return InsuranceCard.query(on: req.db)
            .filter(\.$policyholderID ~~ members.compactMap({$0.id?.uuidString.lowercased()}))
            .with(\.$policy, { policy in
                policy.with(\.$plans)
            })
            .all().flatMap { members in
                
                let rows = members.map({ InsuranceCardCSV.row(card: $0, policy: $0.policy!)}).joined()
                let csvText = "\(InsuranceCardCSV.headers())\(rows)"
                let fileName = "insurances"
                return try! ReportGenerator().generateAndCollectCSV(fileName: fileName, csvText: csvText, req: req, isEmpty: false, filePaths: [])
//                return try! ReportGenerator().generateCSV(fileName: fileName, csvText: csvText, req: req, isEmpty: false, config: config)
            }
    }
        
    func empowered(req: Request) throws -> EventLoopFuture<ClientResponse> {
        let input = try req.content.decode(ReportRequest.self)
        
        return Member.query(on: req.db)
            .filter(\.$status == "active")
//            .filter(\.$enrolledOn >= input.start)
            .group(.or) { orGroup in
                orGroup.filter(\.$unenrolledDate == nil)
                orGroup.filter(\.$unenrolledDate == "")
            }
            .with(\.$households, { household in
                household.with(\.$teams)
            })
            .with(\.$address)
            .join(parent: \.$org)
            .filter(Organization.self, \.$id == UUID(input.orgId)!)
            .all().flatMap { members in
                let rows = members.map({CSVCreator().memberRowWithAddress(member: $0)}).joined()
                let csvText = "\(MemberWithAddress.headers())\(rows)"
                let fileName = "members_with_address"
                let config = ReportCreator.configuration(type: .empowered)
                return try! ReportGenerator().generateCSV(fileName: fileName, csvText: csvText, req: req, isEmpty: false, config: config)
                
//                return try! ReportBuilder().saveCSV(req: req, headers: MemberWithAddress.headers(), fileName: "members_with_address", rows: string).flatMap({ value in
//                    return try! notes(req: req, input: input).flatMap({ count in
//                        return try! membersWhoAnsweredMOUD(req: req, input: input).flatMap({ count in
//                            return try! socialPlansByServiceType(req: req, input: input)
//                        })
//                    })
////                    return req.eventLoop.future(members.count)
//                })
            }
    }
    
    func pushTest(req: Request) throws -> EventLoopFuture<HTTPStatus> {
//        Device.query(on: req.db).all().flatMap { devices in
//            devices.sequencedFlatMapEach(on: req.eventLoop) { device in
//                snsClient.createPlatformEndpoint(SNS.CreatePlatformEndpointInput(
//                    platformApplicationArn: .navigatorEndpoint,
//                    token: device.deviceID)).flatMap { response in
//                        device.arn = response.endpointArn ?? ""
//                        return device.save(on: req.db)
//                    }
//            }
//        }
        let snsClient = SNS(client: req.aws.client, region: .useast1, timeout: .seconds(90))
        
        let input = SNS.PublishInput(message: "Hello From Vapor",
                                     targetArn: "arn:aws:sns:us-east-1:628326973807:endpoint/APNS_SANDBOX/navigator-stg/64598601-aed2-34f0-b90a-c3bf453c979a")
        
        return snsClient.publish(input).flatMap { response in
            print(response)
            return req.eventLoop.future(HTTPStatus.ok)
        }
    }
    
    func socialPlansByServiceType(req: Request, input: ReportRequest) throws -> EventLoopFuture<Int> {
        return CarePackage.query(on: req.db)
            .with(\.$items)
            .with(\.$items, { item in
                item.with(\.$network)
            })
            .group(.or) { orGroup in
                orGroup.filter(\.$status == "active")
                orGroup.filter(\.$status == "complete")
            }
            .filter(\.$title  !~ "demo")
            .join(parent: \.$org)
            .filter(Organization.self, \.$id == UUID(input.orgId)!)
            .all().flatMap { socialPlans in
                let items = socialPlans.compactMap({$0}).compactMap({$0.items}).flatMap({$0})
                let networks = items.compactMap({$0.network})
                let string = networks.map({CSVCreator().networkRow(network: $0)}).joined()
                return try! ReportBuilder().saveCSV(req: req, headers: NetworkHeaders.headers(), fileName: "resources", rows: string).flatMap({ value in
                    return req.eventLoop.future(socialPlans.count)
                })
            }
    }
    
    
    func exportAllData(req: Request) throws -> EventLoopFuture<ExportResponse> {
        let input = try req.content.decode(ReportInput.self)
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        guard let start = dateFormatter.date(from: input.startDate),
              let end = dateFormatter.date(from: input.endDate) else {
            throw Abort(.badRequest, reason: "Invalid date format")
        }
        let report = ExportData(model: ReportQuery(start: start, end: end, org: input.org, input: input),config: ReportCreator.configuration(type: .allData(orgId: input.org, reportDate: input.combineDate())))        
        return try report.exportData(req: req)
    }
    
    func export(req: Request) throws -> EventLoopFuture<String> {
        let input = try req.content.decode(ReportInput.self)
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        guard let start = dateFormatter.date(from: input.startDate),
              let end = dateFormatter.date(from: input.endDate) else {
            throw Abort(.badRequest, reason: "Invalid date format")
        }
        let report = WellupReportBuilder(model: ReportQuery(start: start, end: end, org: input.org, input: input))
        return try report.exportData(req: req)
    }
    
    func dashboard(req: Request) throws -> EventLoopFuture<WellupReport> {
        let input = try req.content.decode(ReportInput.self)
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        guard let start = dateFormatter.date(from: input.startDate),
              let end = dateFormatter.date(from: input.endDate) else {
            throw Abort(.badRequest, reason: "Invalid date format")
        }
        let report = WellupReportBuilder(model: ReportQuery(start: start, end: end, org: input.org, input: input))
        return try report.buildDashboard(req: req)
    }
    
    func buildGenesis(req: Request) throws -> EventLoopFuture<String> {
        return try! ReportBuilder().buildGenesis(req: req, id: "")
    }
    
    func genesisGIS(req: Request) throws -> EventLoopFuture<String> {
        return try! ReportBuilder().buildGenesisGISData(req: req, id: "")
    }
    
    func report(req: Request)  throws -> EventLoopFuture<HTTPStatus> {
        return try! self.importData(req: req).flatMap { _ in
            return req.eventLoop.future(HTTPStatus.accepted)
        }
    }
    
    func dataImport(req: Request)  throws -> EventLoopFuture<HTTPStatus> {
        return try! DataImporter.importData(req: req).transform(to: HTTPStatus.accepted)
//        return try! self.importData(req: req).flatMap { _ in
//            return req.eventLoop.future(HTTPStatus.accepted)
//        }
    }    
    
    func createCSV(req: Request) throws -> EventLoopFuture<ClientResponse> {
        let input = try req.content.decode(CSVInput.self)
        return ReportGenerator().runReportSchedule(req: req, input: input)
    }
    
//    fileprivate func mapMembersAndUsers() {
//        if let first = item["roles"], first.lowercased() == "{patient}" {
//            //work on membesr
//            var member = Member(email: "", firstName: "", lastName: "", type: "adult", roles: [], dob: "")
//            if let id = item["id"], !id.isEmpty, let uuid = UUID(uuidString: id) {
//                member.id = uuid
//            }
//
//            if let first = item["first"] {
//                member.firstName = first
//            }
//            if let mdd = item["middle"] {
//                member.middleName = mdd
//            }
//            if let last = item["last"] {
//                member.lastName = last
//            }
//            if let color = item["color"] {
//                member.color = color
//            }
//
//            if let email = item["email"] {
//                member.email = email.isEmpty ? "-" : email
//            }
//
//            if let dob = item["dob"] {
//                member.dob = dob.isEmpty ? "-" : dob
//            }
//
//            if let ethnicity = item["ethnicity"] {
//                member.ethnicity = ethnicity.lowercased()
//            }
//
//            if let lang = item["lang"] {
//                if lang.isEmpty {
//                    member.lang = "en-us"
//                } else if lang.lowercased() == "english" {
//                    member.lang = "en-us"
//                } else if lang.lowercased() == "spanish, castilian" {
//                    member.lang = "es"
//                } else {
//                    member.lang = "en-us"
//                }
//            }
//
//            if let sexualIdentity = item["sexualIdentity"] {
//                member.sexualIdentity = sexualIdentity.lowercased()
//            }
//
//            if let genderIdentity = item["genderIdentity"] {
//                member.genderIdentity = genderIdentity.lowercased()
//            }
//
//            if let gender = item["gender"] {
//                member.gender = gender.lowercased()
//            }
//
//            if let pronouns = item["pronouns"] {
//                member.pronouns = pronouns.lowercased()
//            }
//
//            if let referred_by = item["referred_by"] {
//                member.referredBy = referred_by.lowercased()
//            }
//
//            if let last_at = item["last_at"], let date = DateFormatter.dateFromMultipleFormats(fromString: last_at) {
//
//                member.lastAt = Date.sentTime(date: date)
//            }
//
//            if let score = item["score"] {
//                member.score = score
//            }
//            //add address
//            //add phones
//
//            //fetch org and create on with batch
//            member.status = "active"
//            member.roles = ["client"]
////                    members.append(member)
//
//            return try! OrgsController.find(req: req, id: "f7f9780d-4fb1-44e7-9965-93a6f584d6ee").flatMap { org in
//
//                return org.$members.create(member, on: req.db).transform(to: member).flatMap { mem in
//
//                    if let address = createAddrssIfNeed(item: item) {
//
//                        return mem.$address.create(address, on: req.db)
//                    } else {
//
//                        return req.eventLoop.future()
//                    }
//                }
//            }
//        } else {
//            //work on users
//            var user = User(email: "", firstName: "", lastName: "")
//            if let id = item["id"], !id.isEmpty, let uuid = UUID(uuidString: id) {
//                user.id = uuid
//            }
//
//            if let first = item["first"] {
//                user.firstName = first
//            }
//            if let mdd = item["middle"] {
//                user.middleName = mdd
//            }
//            if let last = item["last"] {
//                user.lastName = last
//            }
//            if let color = item["color"] {
//                user.color = color
//            }
//
//            if let url = item["url"] {
//                user.profile = url
//            }
//
//            user.email = item["email"] ?? "-"
//            user.roles = ["navigator"]
////                    users.append(user)
////               NOTE NEED TO CRATE AUTH FIRST
//            return try! OrgsController.find(req: req, id: "f7f9780d-4fb1-44e7-9965-93a6f584d6ee").flatMap { org in
//                return org.$users.create(user, on: req.db)
//            }
//        }
//    }
    
//    func importMembersAddress() {
//        if let first = item["roles"], first.lowercased() == "{patient}", let id = item["id"], !id.isEmpty {
//            if let street1 = item["line1"], !street1.isEmpty, let city = item["city"], !city.isEmpty, let state = item["state"], !state.isEmpty, let zip = item["zip"], !zip.isEmpty {
//
//                var address = Address(street: street1, city: city, state: state, zip: zip, country: "", county: "", kind: "main", note: item["addressInfo"])
//                if let lat = item["lat"], !lat.isEmpty, let lon = item["lon"], !lon.isEmpty {
//                    address.lat = Double(lat)
//                    address.lon = Double(lon)
//                }
//                print(address)
//                return try! MembersController.find(req: req, id: id).flatMap { member in
//                    return member.$address.create([address], on: req.db)
//                }
//            } else {
//                return req.eventLoop.future()
//            }
//
//        } else {
//            //users
//            return req.eventLoop.future()
//        }
//    }
    
//    func importHousehold() {
//    if let type = item["type"], type.lowercased() == "household" {
//
//        if let id = item["id"], !id.isEmpty, let uuid = UUID(uuidString: id) {
//            var household = Household(id:uuid, title: item["title"] ?? "", type: "household", kind: "household")
//
//            if let last_at = item["last_at"], let date = DateFormatter.dateFromMultipleFormats(fromString: last_at) {
//                household.lastVisit = Date.sentTime(date: date)
//            }
//
//            if let score = item["score"], !score.isEmpty {
//                household.householdScore = score
//            }
//
//            return try! OrgsController.find(req: req, id: "f7f9780d-4fb1-44e7-9965-93a6f584d6ee").flatMap { org in
//                return org.$households.create(household, on: req.db).transform(to: household).flatMap { house in
//
//                    if let address = self.createAddrssIfNeed(item: item) {
//                        return house.$address.create(address, on: req.db)
//
//                    } else {
//                        return req.eventLoop.future()
//
//                    }
//                }
//            }
//
//        } else {
//            return req.eventLoop.future()
//        }
//    } else {
//        let team = Team(id: UUID(uuidString: item["id"] ?? ""), name: item["title"] ?? "")
//        return try! OrgsController.find(req: req, id: "f7f9780d-4fb1-44e7-9965-93a6f584d6ee").flatMap { org in
//            return org.$teams.create([team], on: req.db)
//        }
//    }
//    }
    
//    func importNetworks() {
//        if  let id = item["id"], !id.isEmpty, let uuid = UUID(uuidString: id) {
//            let network = Network(id: uuid, name: item["name"] ?? "", types: [item["service_type"] ?? ""], status: item["status"] ?? "", contact: item["contact"] ?? "")
//            return try! OrgsController.find(req: req, id: "f7f9780d-4fb1-44e7-9965-93a6f584d6ee").flatMap { org in
//                return org.$networks.create(network, on: req.db).transform(to: network).flatMap { net in
//                    if let address = createAddrssIfNeed(item: item) {
//                        return net.$address.create([address], on: req.db).flatMap { _ in
//                            if let phone = createPhoneIfNeed(item: item) {
//                                return net.$phones.create([phone], on: req.db)
//
//                            } else {
//                                return req.eventLoop.future()
//                            }
//                        }
//                    } else {
//                        return req.eventLoop.future()
//                    }
//                }
//            }
//        } else {
//            return req.eventLoop.future()
//        }
//    }
    
//    func importServices() {
//        if let id = item["id"], !id.isEmpty, let uuid = UUID(uuidString: id) {
//            let name = item["name"] ?? ""
//            let type = item["type"] ?? ""
//           let service =  Service(id:uuid, name: name, desc: item["desc"] ?? "", service: type, type: type, status: "accepting")
//            return try! OrgsController.find(req: req, id: "f7f9780d-4fb1-44e7-9965-93a6f584d6ee").flatMap { org in
//                return org.$services.create([service], on: req.db).transform(to: service).flatMap { newServ in
//                    if let rule = item["limits"], !rule.isEmpty {
//                        return try! ServicesController.find(req: req, id: newServ.id?.uuidString ?? "").flatMap({ newService in
//                            let serviceRule = ServiceRule(title: name, msg: rule)
//                            return newService.$rules.create(serviceRule, on: req.db)
//                        })
//
//                    }else {
//                        return req.eventLoop.future()
//                    }
//                }
//            }
//        } else {
//            return req.eventLoop.future()
//        }
//    }
    
    func importNotes() {
//        if let id = item["id"], !id.isEmpty, let uuid = UUID(uuidString: id) {
//            let title = item["takerName"] ?? ""
//            let msg = item["note"] ?? ""
//            let tag = item["category"] ?? ""
//
//            let userID = item["takerID"] ?? ""
//
//            let memberID = item["ownerID"]
//
//            let note = Note(id: uuid, title: "New note from \(title)", type: tag, msg: msg)
//            let allTags = Tag(name: tag, key: tag, color:"0ABF89")
//
//            guard let networkID = UUID(userID) else {   return req.eventLoop.future() }
//
//            return User.find(networkID, on: req.db).flatMap { model in
//                if let user = model {
//                    print(uuid)
//                    return note.create(on: req.db).transform(to: note).flatMap { note in
//                        note.$creator.id = user.id
//                        return note.$tags.create(allTags, on: req.db).flatMap { _ in
//                            if let member = memberID {
//                                return try! MembersController.find(req: req, id: member).flatMap { member in
//                                    note.$member.id = member.id
//                                    return note.update(on: req.db)
//                                }
//                            } else {
//                                return note.update(on: req.db)
//                            }
//                        }
//                    }
//                } else {
//                    return req.eventLoop.future()
//                }
//            }
//        } else {
//            return req.eventLoop.future()
//        }
    }
    
//    func importSurveys() {
//        if let id = item["id"], !id.isEmpty, let uuid = UUID(uuidString: id) {
//            let name         = item["name"]         ?? ""
//            let user_id      = item["user_id"]    ?? ""
//            let status       = item["status"]       ?? ""
//            let key          = item["key"]          ?? ""
//            let score        = item["score"]        ?? ""
//            let firstSur     = item["first_survey"]
//            let lastSur      = item["last_survey"]
//            let started_at   = item["started_at"]   ?? ""
//            let ended_at     = item["ended_at"]
//
//
//            let survey = Survey(id: uuid, name: name, memberID: UUID(uuidString: user_id), status: status, key: key, score: score, startedAt: nil, endedAt: nil, firstSurvey: nil, lastSurvey: nil, orgID: "f7f9780d-4fb1-44e7-9965-93a6f584d6ee")
//            if let date = DateFormatter.dateFromMultipleFormats(fromString: started_at) {
//                survey.startedAt = date
//            }
//
//            if let endTime = ended_at, !endTime.isEmpty, let endDate = DateFormatter.dateFromMultipleFormats(fromString: started_at) {
//                survey.endedAt = endDate
//            }
//
//            if let first = firstSur, !first.isEmpty, let firstInt = Int(first) {
//                survey.firstSurvey = firstInt
//            }
//
//            if let last = lastSur, !last.isEmpty, let lastInt = Int(last) {
//                survey.lastSurvey = lastInt
//            }
//
//            return survey.create(on: req.db)
//        } else {
//
//            return req.eventLoop.future()
//        }
//    }
    
//    func importAnswers() {
//        if let id = item["id"], !id.isEmpty, let uuid = UUID(uuidString: id) {
//
//            let survey_id    = item["survey_id"]    ?? ""
//            let taker_id     = item["taker_id"]     ?? ""
//            let giver_id     = item["giver_id"]     ?? ""
//            let answer       = item["answer"]       ?? ""
//            let question_id  = item["question_id"]  ?? ""
//            let value        = item["value"]        ?? ""
//            let score        = item["score"]        ?? ""
//            let parent_id    = item["parent_id"]    ?? ""
//
//            return Survey.query(on: req.db).filter(\.$id == UUID(survey_id)!).first().flatMap { survey in
//                let ans = Answer(id: uuid, surveyID: survey_id, parentID: parent_id, takerID: taker_id, giverID: giver_id, answer: answer, questionID: question_id, value: value, score: score, orgID:"f7f9780d-4fb1-44e7-9965-93a6f584d6ee", key: survey?.key)
//                return ans.create(on: req.db)
//            }
//        } else {
//
//            return req.eventLoop.future()
//        }
//    }
    
//    func importQuestions() {
//      if let id = item["id"], !id.isEmpty, let uuid = UUID(uuidString: id) {
    
//    let title    = item["title"]    ?? ""
//    let message     = item["message"]     ?? ""
//    let level     = item["level"]     ?? ""
//    let value       = item["value"]       ?? ""
//    let score  = item["score"]  ?? ""
//    let type        = item["type"]        ?? ""
//    let question_id        = item["question_id"]        ?? ""
//    let section_id        = item["section_id"]        ?? ""
//
//    let question = Question(id: uuid, title: title, message: message, level: .level1, value: Int(value) ?? 0, score: Int(score), sectionID: nil, questionID: nil, type: ServiceType(rawValue: type))
//
//    if let lvl = SurveyIndent(rawValue: Int(level) ?? 0) {
//        question.level = lvl
//    }
//
//    if let sectionID = UUID(uuidString: section_id) {
//
//        return Section.query(on: req.db).filter(\.$id == sectionID).first().flatMap { section in
//            if let secID = section?.id {
//                question.$section.id = secID
//            }
//            return question.create(on: req.db)
//        }
//
//    } else if let questionID = UUID(uuidString: question_id) {
//
//        return Question.query(on: req.db).filter(\.$id == questionID).first().flatMap { foundQuestion in
//            if let questID = foundQuestion?.id {
//                question.$question.id = questID
//            }
//            return question.create(on: req.db)
//        }
//
//    } else {
//        print("QuestionID:\(uuid) is missing both section:\(section_id) && Quest:\(question_id)")
//        return question.create(on: req.db)
//    }
//
//} else {
//
//    return req.eventLoop.future()
//}
//    }
    
//    func importections() {
//        if let id = item["id"], !id.isEmpty, let uuid = UUID(uuidString: id) {
//            let title    = item["title"]    ?? ""
//            let complete    = item["complete"]    ?? ""
//            let score    = item["score"]    ?? ""
//            let type    = item["type"]    ?? ""
//            let survey_id    = item["survey_id"]    ?? ""
//            let sec = Section(id:uuid, title: title, score: score, complete: Bool(complete.lowercased()) ?? false, surveyID: nil, type: type)
//            return Survey.query(on: req.db).filter(\.$id == UUID(survey_id)!).first().flatMap { survey in
//                if let sur = survey {
////                            note.$creator.id = user.id
//                    sec.$survey.id = sur.id
//                    return sec.create(on: req.db)
//                } else {
//                    return req.eventLoop.future()
//                }
//            }
//        } else {
//            return req.eventLoop.future()
//        }
//    }
    
    func importAnswers() {
//        if let id = item["id"], !id.isEmpty, let uuid = UUID(uuidString: id) {
//            let name    = item["name"]    ?? ""
//            let url    = item["url"]    ?? ""
////                    let category    = item["category"]    ?? ""
//            let noteID    = item["note_id"]    ?? ""
//
//            let attachment = Attachment(id: uuid, name: name, kind: "attachment", type: "image", url: url)
//            if let noteUUID = UUID(uuidString: noteID) {
//                return Note.query(on: req.db).filter(\.$id == noteUUID).with(\.$member).first().flatMap { note in
//                    if let nte = note, let memberID = nte.member?.id {
//                        attachment.$member.id = memberID
//                        return attachment.create(on: req.db)
//                    } else {
//                        return attachment.create(on: req.db)
//                    }
//                }
//            } else {
//                return attachment.create(on: req.db)
//            }
//        } else {
//            return req.eventLoop.future()
//        }
    }
    
    func importPackages() {
//        if let id = item["id"], !id.isEmpty, let uuid = UUID(uuidString: id) {
//            let title       = item["title"]    ?? ""
//            let user_id     = item["user_id"]    ?? ""
//            let status      = item["status"]    ?? ""
//            let type        = item["type"]    ?? ""
//
//            let package = CarePackage(id:uuid, title: title, status: status, type: type)
//            if let started_at = item["started_at"], let date = DateFormatter.dateFromMultipleFormats(fromString: started_at) {
//                package.startedAt = date
//
//            }
//            
//            if let ended_at = item["ended_at"], let date = DateFormatter.dateFromMultipleFormats(fromString: ended_at) {
//                package.endedAt = date
//            }
//
//            package.creator = "aa61c96e-5e35-49e6-a710-d5ff88f87fb8" //need default user
//            package.reciever = user_id
//
//            return try! OrgsController.find(req: req, id: "f7f9780d-4fb1-44e7-9965-93a6f584d6ee").flatMap { org in
//                return org.$packages.create(package, on: req.db)
//            }
//
//        } else {
//            return req.eventLoop.future()
//        }
    }
    
//    func importServicesPackage() {
//        let packageID       = item["package_id"]    ?? ""
//        let serviceID       = item["service_id"]    ?? ""
//
//        return  Network.query(on: req.db).join(siblings: \.$services).filter(Service.self, \.$id == UUID(uuidString: serviceID)!).with(\.$services).first().flatMap { network in
//            if let net = network,  let networkID = net.id {
//                let type = net.services.last?.type ?? "missing"
//
//                return try! CarePackagesController.createTempItem(req: req, cpID: packageID.lowercased(), networkID: networkID.uuidString.lowercased(), creatorID: "aa61c96e-5e35-49e6-a710-d5ff88f87fb8", type: type).flatMap { cpUpdate in
//                    return req.eventLoop.future()
//                }
//
//            } else {
//                return req.eventLoop.future()
//            }
//        }
//
//
////                query.join(siblings: \.$services)
////                    .filter(Service.self, \.$type == typ)
//    }
    
    func importNAvs() {
//        if let id = item["id"], !id.isEmpty, let uuid = UUID(uuidString: id) {
//            let userSignup = UserSignup(orgID: "f7f9780d-4fb1-44e7-9965-93a6f584d6ee", username: item["email"] ?? "-", password: "Welcome2023!", firstName: item["first"] ?? "-", lastName: item["last"] ?? "-", roles: ["navigator"])
//            let user = try! AuthUser.create(from: userSignup)
//            return try! AuthController.createTempSignup(req: req, userSignup: userSignup, user: user).flatMap { _ in
//                return req.eventLoop.future()
//            }
//        } else {
//            return req.eventLoop.future()
//        }
    }
    
    func importData(req: Request) throws -> EventLoopFuture<Void> {
        return req.eventLoop.future()
//        do {
////            let filePath = req.application.directory.workingDirectory + "Public/data/user.csv"
////            let filePath = req.application.directory.workingDirectory + "Public/data/teams.csv"
////            let filePath = req.application.directory.workingDirectory + "Public/data/network.csv"
////            let filePath = req.application.directory.workingDirectory + "Public/data/service.csv"
////            let filePath = req.application.directory.workingDirectory + "Public/data/navs.csv"
////            let filePath = req.application.directory.workingDirectory + "Public/data/notes.csv"
////            let filePath = req.application.directory.workingDirectory + "Public/data/surveys.csv"
////              let filePath = req.application.directory.workingDirectory + "Public/data/sections.csv"
////            let filePath = req.application.directory.workingDirectory + "Public/data/questions.csv"
//            let filePath = req.application.directory.workingDirectory + "Public/data/answers.csv"
////            let filePath = req.application.directory.workingDirectory + "Public/data/attachments.csv"
////            let filePath = req.application.directory.workingDirectory + "Public/data/packages.csv"
////            let filePath = req.application.directory.workingDirectory + "Public/data/package_services.csv"
//            
//            let csvFile: CSV = try CSV<Named>(url: URL(fileURLWithPath: filePath))
//            return csvFile.rows.sequencedFlatMapEach(on: req.eventLoop) { item in
//                if let surID = item["survey_id"], let survey_id =  UUID(surID) {
//                    return Survey.query(on: req.db).filter(\.$id == survey_id).first().flatMap { survey in
//                        if let sur = survey, let giverID = item["giver_id"], let uuid = UUID(uuidString: giverID) {
//                            return  User.query(on: req.db).filter(\.$id == uuid).first().flatMap { user in
//                                if let usr = user {
//                                    sur.$taker.id = usr.id
//                                    return sur.update(on: req.db)
//                                } else {
//                                    return req.eventLoop.future()
//                                }
//                            }
//                        } else {
//                            return req.eventLoop.future()
//                        }
//                    }
//                } else {
//                    return req.eventLoop.future()
//                }
//            }
//
//        } catch let parseError as CSVParseError {
//            // Catch errors from parsing invalid CSV
//            print(parseError.localizedDescription)
//            return req.eventLoop.future()
//        } catch {
//            // Catch errors from trying to load files
//            return req.eventLoop.future()
//        }
    }
    
    fileprivate func createAddrssIfNeed(item:[String:String]) -> Address? {
        if let street1 = item["line1"], !street1.isEmpty, let city = item["city"], !city.isEmpty, let state = item["state"], !state.isEmpty, let zip = item["zip"], !zip.isEmpty {
            let address = Address(street: street1, city: city, state: state, zip: zip, country: "", county: "", kind: "main", note: item["addressInfo"])
            if let lat = item["lat"], !lat.isEmpty, let lon = item["lon"], !lon.isEmpty {
                address.lat = lat
                address.lon = lon
            }
            return address
        } else {
            return nil
        }
    }
    
    fileprivate func createPhoneIfNeed(item:[String:String]) -> PhoneNumber? {
        if let phone = item["phone"], !phone.isEmpty {
            return PhoneNumber(label: "main", number: phone)
        } else {
            return nil
        }
    }
    
//    func dashboard(req: Request) throws -> EventLoopFuture<View> {
////        guard let orgID = id else { throw NetworkError.error(type: .organization)}
//        return try! APIController.find(req: req, id: "5b7ed772-68bf-48c9-b956-d1b5cd1470d9").flatMap { org in
//            let totalUsers          = org.users.count
//            let totalMembers        = org.members.count
//            let totalTeams          = org.teams.count
//            let toatlCarepackages   = org.packages.count
//            return Survey.query(on: req.db).count().flatMap { totalSurveys in
//                //            org.members.reduce(into: [String:[String]]()) { json, member in
//                //
//                //            }
//                            
//                return req.view.render("dashboard", ReportContext(totalMembers: totalMembers, totalNavs: totalUsers, totalTeams: totalTeams, totalCarePacks: toatlCarepackages, totalSurveys: totalSurveys, chartItem: [
//                    ReportBarChart(title: "Hispanic", percentage:60.0, leftOver: 40.0),
//                    ReportBarChart(title: "African American", percentage:30.0, leftOver: 70.0),
//                    ReportBarChart(title: "English", percentage:10.0, leftOver: 90.0)
//                ]))
//            }
//        }
//    }
    
    func templates(req: Request) throws -> EventLoopFuture<View> {
        guard let id = req.parameters.get("userID") else { throw Abort(.notFound, reason: "User ID is required") }
        let layout:String? = req.query["layout"]
        return try! MembersController().buildDashboard(req: req, id: id).flatMap { dashboard in
            let url = dashboard.member.profileURL()
            let context = EHRContext(dashboard: dashboard, layout: layout ?? "full", memberURL: url)
            return req.view.render("index", context)
        }
    }
    
    
    func lookup(req: Request) throws -> EventLoopFuture<Report> {
        guard let id = req.parameters.get("userID") else { throw Abort(.notFound, reason: "User ID is required") }
        return try! MembersController().buildDashboard(req: req, id: id).flatMap { dashboard in
            return req.eventLoop.future( Report(dashboard:dashboard))
        }
    }
    
    static func createWith(req: Request, authUser:AuthUser, signup:UserSignup) throws -> EventLoopFuture<Void> {
        guard let authUser = try? authUser.asPublic() else {
            return req.eventLoop.future(error: Abort(.internalServerError))
        }
        return signup.user(auth: authUser.id.uuidString).create(on: req.db)
    }
    
    func members(req: Request) throws -> EventLoopFuture<Page<Member>> {
        guard let id = req.parameters.get("orgID") else { throw Abort(.notFound, reason: "Org ID is required") }
        return try! MembersController().orgMembers(req: req, orgID: id)
    }
    
    static func find(req: Request, id:String?) throws -> EventLoopFuture<Organization> {
        guard let orgID = id else { throw NetworkError.error(type: .organization)}
        guard let networkID = UUID(orgID) else { throw NetworkError.error(type: .organization)}
        return Organization.query(on: req.db).filter(\.$id == networkID)
            .with(\.$users)
                .with(\.$members)
                .with(\.$teams)
                .with(\.$users)
                .with(\.$packages)
                .first()
            .flatMapThrowing() {model in
            guard let foundModel = model else { throw NetworkError.error(type: .organization) }
            return foundModel
        }
    }
    
    func index(req: Request) throws -> EventLoopFuture<[Organization]> {
        return self.buildQuery(query: Organization.query(on: req.db), req: req).all()
    }

    func create(req: Request) throws -> EventLoopFuture<Organization> {
        let org = try req.content.decode(Organization.self)
        return org.save(on: req.db).transform(to: org)
    }
    
    func update(req: Request) throws -> EventLoopFuture<User> {
        guard let id = req.parameters.get("userID") else { throw Abort(.notFound, reason: "User ID is required") }
        let input = try req.content.decode(UserUpdateInput.self)
        return try UsersController.find(req: req, id: id).flatMap { usr in
            let user = input.returnUpdatedModel(user: usr)
            return user.update(on: req.db).transform(to: user)
        }
    }
    
    func creatAttachment(req: Request) throws -> EventLoopFuture<User> {
        guard let id = req.parameters.get("userID") else { throw Abort(.notFound, reason: "User ID is required") }
        let input = try req.content.decode(UploadInput.self)
        return try! UsersController.find(req: req, id: id).flatMap { usr in
            return try! AttachmentsController().uploadImageToCloudinary(req).flatMap { resposne in
                let attach = Attachment(name: input.name, 
                                        kind: input.type,
                                        type: "image",
                                        url: resposne.secure_url,
                                        refID: resposne.public_id)
                return usr.$attachments.create(attach, on: req.db).transform(to: usr).flatMap { updatedUser in
                    if input.isProfile() {
                        updatedUser.profile = resposne.secure_url
                        return updatedUser.update(on: req.db).transform(to: updatedUser)
                    } else {
                        return req.eventLoop.future(updatedUser)
                    }
                }
            }
        }
    }
    
//    //MARK: - Query
    fileprivate func buildQuery(query:QueryBuilder<Organization>, req:Request) -> QueryBuilder<Organization> {
        let title:String?            = req.query["title"]
        let type:String?            = req.query["type"]
//        let lastName:String?             = req.query["lastName"]
//        let email:String?                = req.query["email"]
//
//        if let nm = firstName  {
//            query.filter(\.$firstName, .custom("ilike"), "%\(nm.lowercased())%")
//        }
//
//        if let nm = lastName  {
//            query.filter(\.$lastName, .custom("ilike"), "%\(nm.lowercased())%")
//        }
//
        if let typ = type {
            query.filter(\.$type == typ)
        }
        
        if let tl = title {
            query.filter(\.$title == tl)
        }

//        return query.with(\.$attachments).with(\.$teams)
        return query.with(\.$users)
            .with(\.$attachments)
            .with(\.$address)
            .with(\.$phones)
            .with(\.$members)
            .with(\.$teams)
            .with(\.$networks)
            .with(\.$services)
            .with(\.$chats)
    }
}




extension DateFormatter {
    static func date(from string: String) -> Date? {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        return dateFormatter.date(from: string)
    }
}
