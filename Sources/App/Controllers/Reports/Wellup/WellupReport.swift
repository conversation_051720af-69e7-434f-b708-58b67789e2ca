//
//  ReportGenerator.swift
//
//
//  Created by <PERSON> on 5/6/24.
//


import Foundation
import Vapor
import Fluent
import Leaf
import SwiftCSV
import SotoCore
import SotoS3

struct WellupReportPieChartItem :Content {
    var title:String
    var value: Int
}

struct WellupReportLatLng :Content {
    var lat: String
    var lon: String
    var memberId:String? = nil
}

struct WellupAssesments:Content {
    //assessments
    var totalAssessments: Int = 0
    var assessmentsByTypes: [String:Int] = [:]
}

struct WellupOperational: Content {
    var totalNotes: Int = 0
    var totalAttachments: Int = 0
    var totalChats: Int = 0
}

struct WellupTasksData: Content {
    //Tasks
    var totalTasks: Int = 0
    var totalTasksCompleted: Int = 0
    var totalTasksPending: Int = 0
    var tasksByType: [String:Int] = [:]
    var tasksByNavigators: [String:Int] = [:]
}

struct WellupPlans: Content {
    //Plans
    var totalPlans: Int = 0
    var totalActivePlans: Int = 0
    var totalServices: Int = 0
    var totalNetworks: Int = 0
    var totalAppointments: Int = 0
    var appointmentBreakdown: [String:Int] = [:]
    
    var referralNetwork: [String:Int] = [:]
    var networkCategories: [String:Int] = [:]
}

struct WellupLocation:Content {
    var locations: [String:[WellupReportLatLng]] = [:]
}

struct WellupMemberDemographics:Content {
    var enrolled:Int = 0
    var households:Int = 0
    var totalInsurances:Int = 0
    var totalUnenrolled: Int = 0
    var pending: Int = 0
    var membersByTypes: [String:Int] = [:]
    var membersBySubStatus: [String:Int] = [:]
    var ethnicity: [String:Int] = [:]
    var genders: [String:Int] = [:]
    var insurances: [String:Int] = [:]
}

struct WellupReport: Content {
    var memberDemographics: WellupMemberDemographics = WellupMemberDemographics()
    var locations: WellupLocation = WellupLocation()
    var assessments: WellupAssesments = WellupAssesments()
    var plansData: WellupPlans = WellupPlans()
    var taskData: WellupTasksData = WellupTasksData()
    var operationalData: WellupOperational = WellupOperational()
}

final class ReportInput: Content {
    var startDate:String
    var endDate:String
    var org:String
    
    init(startDate: String, endDate: String, org: String) {
        self.startDate = startDate
        self.endDate = endDate
        self.org = org.lowercased()
    }
    
    func combineDate() -> String {
        return "\(startDate)-\(endDate)"
    }
    
    func toJSONData() -> Data? {
        let encoder = JSONEncoder()
        do {
            let jsonData = try encoder.encode(self)
            return jsonData
        } catch {
            return nil
        }
    }
}

struct ReportQuery {
    var start:Date
    var end: Date
    var org: String
    var input: ReportInput
    
    init(start: Date, end: Date, org: String, input: ReportInput) {
        self.start = start
        self.end = end
        self.org = org.lowercased()
        self.input = input
    }
    
    
    func combineDate() -> String {
        return "\(Date.yearMonthDayString(date: start))-\(Date.yearMonthDayString(date: end))"
    }
}

struct WellupReportBuilder {
    
    static let env:String = isProduction ? "production" : "staging"
    var model:ReportQuery
    var paths: [URL] = []
    var config: ReportConfiguration {
        ReportCreator.configuration(type: .wellup(orgId: model.org, reportDate: model.combineDate()))
    }
    
    func exportData(req: Request) throws -> EventLoopFuture<String> {
        return try export(req: req)
    }
    
    func buildDashboard(req: Request)  throws -> EventLoopFuture<WellupReport> {
        return try! buildReport(req: req)
    }
    
    func buildReport(req: Request)  throws -> EventLoopFuture<WellupReport> {
        var report = WellupReport()
        
        return try! buildMemberReport(req: req).flatMap { wellupMemberDemographics in
            
            report.memberDemographics = wellupMemberDemographics
            
            return try! buildLocationsReport(req: req).flatMap { wellupLocations in
                
                report.locations = wellupLocations
                
                return try! buildAssessmentReport(req: req).flatMap({ wellupAssesments in
                    
                    report.assessments = wellupAssesments
                    
                    return try! buildPlansReport(req: req).flatMap({ wellupPlans in
                        
                        report.plansData = wellupPlans
                        
                        return try! buildTasksReport(req: req).flatMap({ wellupTasksData in
                            
                            report.taskData = wellupTasksData
                            
                            return try! buildOperations(req: req).flatMap({ wellupOperational in
                                
                                report.operationalData = wellupOperational
                                
                                return req.eventLoop.future(report)
                            })
                        })
                    })
                })
            }
        }
    }
    
    func buildOperations(req: Request)  throws -> EventLoopFuture<WellupOperational> {
        var wellupOperationalReport = WellupOperational()
        return try! totalNotes(req: req).flatMap({ noteCount in
            
            wellupOperationalReport.totalNotes = noteCount
            
            return try! totalAttachments(req: req).flatMap({ attachments in
                
                wellupOperationalReport.totalAttachments = attachments
                
                return try! totalChats(req: req).flatMap({ chats in
                    
                    wellupOperationalReport.totalChats = chats
                    
                    return req.eventLoop.future(wellupOperationalReport)
                    
                })
            })
        })
    }
    
    
    func buildTasksReport(req: Request)  throws -> EventLoopFuture<WellupTasksData> {
        return TaskModel.query(on: req.db)
            .filter(\.$createdAt >= model.start)
            .filter(\.$createdAt <= model.end)
            .filter(\.$org.$id == UUID(model.org)!)
            .count().flatMap({ total in
                var tasksReport = WellupTasksData(totalTasks: total)
                
                return try! totalTasksCompleted(req: req).flatMap({ totalComplete in
                    
                    tasksReport.totalTasksCompleted = totalComplete
                    
                    return try! totalTasksPending(req: req).flatMap({ pendingCount in
                        
                        tasksReport.totalTasksPending = pendingCount
                        
                        return try! totalTasksByType(req: req).flatMap({ byType in
                            
                            tasksReport.tasksByType = byType
                            
                            return try! totalTasksByNav(req: req).flatMap({ tasksByNav in
                                
                                tasksReport.tasksByNavigators = tasksByNav
                                
                                return req.eventLoop.future(tasksReport)
                            })
                        })
                    })
                })
                
            })
    }    
    
    func buildPlansReport(req: Request)  throws -> EventLoopFuture<WellupPlans> {
        var report = WellupPlans()
        
        return try! totalPlans(req: req).flatMap { createdPlans in
            
            report.totalPlans = createdPlans
            
            return try! totalActivePlans(req: req).flatMap { activePlan in
                
                report.totalActivePlans = activePlan
                
               return try! totalServices(req: req).flatMap { totalCount in
                    
                    report.totalServices = totalCount
                    
                    return try! totalNetworks(req: req).flatMap { totalNetworks in
                        
                        report.totalNetworks = totalNetworks
                        
                        return try! totalAppointments(req: req).flatMap({ totalApts in
                            
                            report.totalAppointments = totalApts
                            
                            return try! totalAppointmentsByStatus(req: req).flatMap({ appointment in
                                
                                report.appointmentBreakdown = appointment
                                
                                return try! totalReferralNetworks(req: req).flatMap({ referralNetwork in
                                    
                                    report.referralNetwork = referralNetwork.networks
                                    
                                    report.networkCategories = referralNetwork.category
                                    
                                    return req.eventLoop.future(report)
                                })
                            })
                        })
                        
                    }
                }
            }
        }
    }
    
    func buildAssessmentReport(req: Request)  throws -> EventLoopFuture<WellupAssesments> {
        return Survey.query(on: req.db)
            .filter(\.$createdAt >= model.start)
            .filter(\.$createdAt <= model.end)
            .filter(\.$orgID == model.org)
            .all()
            .flatMap { totalAssessments in
                var report = WellupAssesments()
                report.totalAssessments = totalAssessments.count
                let items = totalAssessments.reduce(into:[String:Int]()) { data, assessment in
                    let key = assessment.name
                    if let value = data[key] {
                        data[key] = value + 1
                    } else {
                        data[key] = 1
                    }
                }
                report.assessmentsByTypes = items
                return req.eventLoop.future(report)
            }
    }
    
    func totalTasksByType(req: Request) throws -> EventLoopFuture<[String:Int]> {
        return TaskModel.query(on: req.db)
            .filter(\.$createdAt >= model.start)
            .filter(\.$createdAt <= model.end)
            .filter(\.$org.$id == UUID(model.org)!)
            .all().flatMap { tasks in
                let data = tasks.reduce(into: [String:Int]()) { json, task in
                    let key = task.type
                    if var data = json[key] {
                        data += 1
                        json[key] = data
                    } else {
                        json[key] = 1
                    }
                }
                return req.eventLoop.future(data)
            }
    }
    
    func totalTasksByNav(req: Request) throws -> EventLoopFuture<[String:Int]> {
        return TaskModel.query(on: req.db)
            .filter(\.$createdAt >= model.start)
            .filter(\.$createdAt <= model.end)
            .filter(\.$org.$id == UUID(model.org)!)
            .with(\.$assignee)
            .all().flatMap { tasks in
                
                let data = tasks.reduce(into: [String:Int]()) { json, task in
                    let fName = task.assignee?.firstName ?? ""
                    let lName = task.assignee?.lastName ?? ""
                    let key = "\(fName) \(lName)".lowercased()
                    if var data = json[key] {
                        data += 1
                        json[key] = data
                    } else {
                        json[key] = 1
                    }
                }
                return req.eventLoop.future(data)
            }
    }
    
    func totalTasksCompleted(req: Request) throws -> EventLoopFuture<Int> {
        return TaskModel.query(on: req.db)
            .filter(\.$createdAt >= model.start)
            .filter(\.$createdAt <= model.end)
            .filter(\.$org.$id == UUID(model.org)!)
            .filter(\.$status == "completed")
            .count()
    }
    
    func totalNotes(req: Request) throws -> EventLoopFuture<Int> {
        return User.query(on: req.db)
            .filter(\.$org.$id == UUID(model.org)!)
            .all(\.$id).flatMap { ids in
                return Note.query(on: req.db)
                    .filter(\.$createdAt >= model.start)
                    .filter(\.$createdAt <= model.end)
                    .filter(\.$creator.$id ~~ ids)
                    .count()
            }
    }
    
    func totalAttachments(req: Request) throws -> EventLoopFuture<Int> {
        return Attachment.query(on: req.db)
            .filter(\.$createdAt >= model.start)
            .filter(\.$createdAt <= model.end)
        //            .filter(\.$status == "completed")
            .count()
    }
    
    func totalChats(req: Request) throws -> EventLoopFuture<Int> {
        return Chat.query(on: req.db)
            .filter(\.$createdAt >= model.start)
            .filter(\.$createdAt <= model.end)
        //            .filter(\.$status == "completed")
            .count()
    }
    
    func totalTasksPending(req: Request) throws -> EventLoopFuture<Int> {
        return TaskModel.query(on: req.db)
            .filter(\.$createdAt >= model.start)
            .filter(\.$createdAt <= model.end)
            .filter(\.$status == "pending")
            .filter(\.$org.$id == UUID(model.org)!)
            .count()
    }
    
    func totalActivePlans(req: Request) throws -> EventLoopFuture<Int> {
        return CarePackage.query(on: req.db)
            .filter(\.$createdAt >= model.start)
            .filter(\.$createdAt <= model.end)
            .filter(\.$status == "active")
            .filter(\.$org.$id == UUID(model.org)!)
            .count()
    }
    
    func totalServices(req: Request) throws -> EventLoopFuture<Int> {
        return CarePackage.query(on: req.db)
            .filter(\.$createdAt >= model.start)
            .filter(\.$createdAt <= model.end)
            .filter(\.$status == "active")
            .filter(\.$org.$id == UUID(model.org)!)
            .all(\.$id).flatMap { cpIds  in
                return CarePackageItem.query(on: req.db)
                    .join(parent: \.$carepackage)
                    .filter(CarePackage.self, \.$id ~~ cpIds)
                    .count()
            }
    }
    
    func totalAppointments(req: Request) throws -> EventLoopFuture<Int> {
        return Appointment.query(on: req.db)
            .filter(\.$createdAt >= model.start)
            .filter(\.$createdAt <= model.end)
            .filter(\.$org.$id == UUID(model.org)!)
        //            .filter(\.$status == "active")
            .count()
    }
    
    func totalAppointmentsByStatus(req: Request) throws -> EventLoopFuture<[String:Int]> {
        return Appointment.query(on: req.db)
            .filter(\.$createdAt >= model.start)
            .filter(\.$createdAt <= model.end)
            .filter(\.$org.$id == UUID(model.org)!)
        //            .filter(\.$status == "active")
            .all().flatMap { appointments in
                let data = appointments.reduce(into: [String:Int]()) { json, appointment in
                    let key = appointment.status
                    if var data = json[key] {
                        data += 1
                        json[key] = data
                    } else {
                        json[key] = 1
                    }
                }
                return req.eventLoop.future(data)
            }
    }
    
    func totalNetworks(req: Request) throws -> EventLoopFuture<Int> {
        return CarePackage.query(on: req.db)
            .filter(\.$createdAt >= model.start)
            .filter(\.$createdAt <= model.end)
            .filter(\.$status == "active")
            .filter(\.$org.$id == UUID(model.org)!)
            .all(\.$id).flatMap { cpIds  in
                return CarePackageItem.query(on: req.db)
                    .join(parent: \.$carepackage)
                    .filter(CarePackage.self, \.$id ~~ cpIds)
                    .with(\.$network)
                    .all().flatMap { items in
                        let types = Set(items.compactMap({$0.network?.types.first}))
                        return req.eventLoop.future(types.count)
                    }
            }
    }
    
    
    func totalReferralNetworks(req: Request) throws -> EventLoopFuture<(networks: [String:Int], category:[String:Int])> {
        return CarePackage.query(on: req.db)
            .filter(\.$createdAt >= model.start)
            .filter(\.$createdAt <= model.end)
            .filter(\.$org.$id == UUID(model.org)!)
            .all(\.$id).flatMap { cpIds  in
                return CarePackageItem.query(on: req.db)
                    .join(parent: \.$carepackage)
                    .filter(CarePackage.self, \.$id ~~ cpIds)
                    .with(\.$network)
                    .all().flatMap { items in
                        var networkJson: [String: Int]  = [:]
                        var categoryNetwork: [String: Int]  = [:]
                        
                        for item in items {
                            let key = item.network?.name ?? "-"
                            if var data = networkJson[key] {
                                data += 1
                                networkJson[key] = data
                            } else {
                                networkJson[key] = 1
                            }
                            
                            let type = item.network?.types.first ?? ""
                            if var data = categoryNetwork[type] {
                                data += 1
                                categoryNetwork[type] = data
                            } else {
                                categoryNetwork[type] = 1
                            }
                        }
                        
                        return req.eventLoop.future((networkJson, categoryNetwork))
                        
                    }
            }
    }
    
    func totalPlans(req: Request) throws -> EventLoopFuture<Int> {
        return CarePackage.query(on: req.db)
            .filter(\.$createdAt >= model.start)
            .filter(\.$createdAt <= model.end)
            .filter(\.$org.$id == UUID(model.org)!)
            .count()
    }
    
    func buildLocationsReport(req: Request)  throws -> EventLoopFuture<WellupLocation> {
        var report = WellupLocation()
        return try! memberLocations(req: req).flatMap({ locations in
            report.locations = locations
            return req.eventLoop.future(report)
        })
    }
    
    struct MemberQueryResult: Codable {
        var ids:[UUID]
        var members:[Member]
    }
    
    func queryReportMembers(req: Request)  throws -> EventLoopFuture<MemberQueryResult> {
        return Member.query(on: req.db)
            .group(.or) { orGroup in
                orGroup.filter(\.$status == "active")
                orGroup.filter(\.$status == "enrolled")
            }
            .group(.or) { orGroup in
                orGroup.filter(\.$unenrolledDate == nil)
                orGroup.filter(\.$unenrolledDate == "")
            }
            .filter(\.$enrolledOn >= model.input.startDate)
            .filter(\.$enrolledOn <= model.input.endDate)
            .with(\.$address)
            .filter(\.$org.$id == UUID(model.org)!)
            .all().flatMap { members in
                let ids = members.compactMap { $0.id }
                return req.eventLoop.future(.init(ids:ids, members: members))
            }
    }
    
    func buildMemberReport(req: Request)  throws -> EventLoopFuture<WellupMemberDemographics> {
        var report = WellupMemberDemographics()
        return try queryReportMembers(req: req).flatMap { results in
            let ids = results.ids
                
            let idStrings = ids.compactMap({$0.uuidString.lowercased()})
                report.enrolled = ids.count
                
                return try! totalHouseholds(req: req).flatMap { totalHouseholds in
                    report.households = totalHouseholds
                    
                    return try! totalInsrances(req: req, members: idStrings).flatMap({ insuranceCount in
                        
                        report.totalInsurances = insuranceCount
                        
                        return try! totalUnenrolled(req: req).flatMap({ unenrolled in
                            
                            report.totalUnenrolled = unenrolled
                            
                            return  try! totalPending(req: req).flatMap({ pending in
                                
                                report.pending = pending
                                
                                return try! membersByType(req: req, ids: ids).flatMap({ types in
                                    
                                    report.membersByTypes = types
                                    
                                    return try! membersBySubStatus(req: req, ids: ids).flatMap({ subStatus in
                                        
                                        report.membersBySubStatus = subStatus
                                        
                                        return try! breakdownByEthnicities(req: req, ids: ids).flatMap({ eth in
                                            
                                            report.ethnicity = eth
                                            
                                            return try! membersByGender(req: req, ids: ids).flatMap({ gender in
                                                
                                                report.genders = gender
                                                
                                                return try! totalInsrancesTypes(req: req, idStrings: idStrings).flatMap({ insurances in
                                                    
                                                    report.insurances = insurances
                                                    
                                                    return req.eventLoop.future(report)
                                                    
                                                })
                                            })
                                        })
                                    })
                                })
                            })
                        })
                    })
                }
            }
    }
    
    func totalInsrances(req: Request, members: [String]) throws -> EventLoopFuture<Int> {
        return InsuranceCard.query(on: req.db)
            .filter(\.$createdAt >= model.start)
            .filter(\.$createdAt <= model.end)
            .filter(\.$policyholderID ~~ members)
            .count()
    }
    
    func totalInsrancesTypes(req: Request, idStrings: [String]) throws -> EventLoopFuture<[String:Int]> {
        return InsuranceCard.query(on: req.db)
            .filter(\.$createdAt >= model.start)
            .filter(\.$createdAt <= model.end)
            .filter(\.$policyholderID ~~ idStrings)
            .with(\.$policy)
            .all().flatMap { insurances in
                var json = [String:Int]()
                for insurance in insurances {
                    if let planType = insurance.policy?.planType {
                        if let value = json[planType] {
                            json[planType] = value + 1
                        } else {
                            json[planType] = 1
                        }
                    }
                }
                return req.eventLoop.future(json)
            }
    }
    
    func totalHouseholds(req: Request) throws -> EventLoopFuture<Int> {
        return Household.query(on: req.db)
            .filter(\.$createdAt >= model.start)
            .filter(\.$createdAt <= model.end)
            .filter(\.$org.$id == UUID(model.org)!)
            .count()
    }
    
    func totalUnenrolled(req: Request) throws -> EventLoopFuture<Int> {
        return Member.query(on: req.db)
            .filter(\.$unenrolledDate >= model.input.startDate)
            .filter(\.$unenrolledDate <= model.input.endDate)
            .with(\.$address)
            .filter(\.$org.$id == UUID(model.org)!)
            .count()
    }
    
    func totalPending(req: Request) throws -> EventLoopFuture<Int> {
        return Member.query(on: req.db)
            .filter(\.$status != "active")
            .filter(\.$unenrolledDate == "")
            .filter(\.$createdAt >= model.start)
            .filter(\.$createdAt <= model.end)
            .filter(\.$org.$id == UUID(model.org)!)
            .count()
    }
    
    func membersByType(req: Request, ids: [UUID]) throws -> EventLoopFuture<[String:Int]> {
        return Member.query(on: req.db) //NOTE we may need to only show active.
            .filter(\.$id ~~ ids)
            .all(\.$type).flatMap { types in
                let items = types.reduce(into:[String:Int]()) { data, type in
                    if let value = data[type] {
                        data[type] = value + 1
                    } else {
                        data[type] = 1
                    }
                }
                return req.eventLoop.future(items)
            }
    }
    
    func membersBySubStatus(req: Request, ids: [UUID]) throws -> EventLoopFuture<[String:Int]> {
        return Member.query(on: req.db) //NOTE we may need to only show active.
            .filter(\.$id ~~ ids)
            .all().flatMap { members in
                var json = [String:Int]()
                
                for member in members {
                    if let military = member.military {
                        if let value = json[military] {
                            json[military] = value + 1
                        } else {
                            json[military] = 1
                        }
                    }
                    
                    if let pregnancyStatus = member.pregnancyStatus {
                        if let value = json[pregnancyStatus] {
                            json[pregnancyStatus] = value + 1
                        } else {
                            json[pregnancyStatus] = 1
                        }
                    }
                    
                }
                return req.eventLoop.future(json)
            }
    }
    
    
    func breakdownByEthnicities(req: Request, ids: [UUID]) throws -> EventLoopFuture<[String:Int]> {
        
        return Member.query(on: req.db) //NOTE we may need to only show active.
            .filter(\.$id ~~ ids)
            .all().flatMap { members in
                let data = members.reduce(into: [String:Int]()) { json, member in
                    let key = member.ethnicity ?? ""
                    if var data = json[key] {
                        data += 1
                        json[key] = data
                    } else {
                        json[key] = 1
                    }
                }
                return req.eventLoop.future(data)
            }
    }
    
    
    func membersByGender(req: Request, ids: [UUID]) throws -> EventLoopFuture<[String:Int]> {
        
        return Member.query(on: req.db) //NOTE we may need to only show active.
            .filter(\.$id ~~ ids)
            .all().flatMap { members in
                let data = members.reduce(into: [String:Int]()) { json, member in
                    let key = member.gender ?? ""
                    if var data = json[key] {
                        data += 1
                        json[key] = data
                    } else {
                        json[key] = 1
                    }
                }
                return req.eventLoop.future(data)
            }
    }
    
    func addressFor(req:Request,  members:[UUID]) throws -> EventLoopFuture<[Address]> {
        return Address.query(on: req.db)
            .join(parent: \.$member)
            .filter(Member.self, \.$id ~~ members)
            .all()
    }
    
    func memberLocations(req: Request) throws -> EventLoopFuture<[String: [WellupReportLatLng]]> {
        return Member.query(on: req.db)
            .filter(\.$status == "active")
            .group(.or) { orGroup in
                orGroup.filter(\.$unenrolledDate == nil)
                orGroup.filter(\.$unenrolledDate == "")
            }
            .filter(\.$enrolledOn >= model.input.startDate)
            .filter(\.$enrolledOn <= model.input.endDate)
//            .filter(\.$score != nil)
            .filter(\.$org.$id == UUID(model.org)!)
            .with(\.$households, { house in
                house.with(\.$address)
            })
            .all().flatMap { members in
                var json: [String: [WellupReportLatLng]] = [:]
                
                for member in members {
                    
                    if let address = member.households.first?.address.first,
                       let lat = address.lat,
                       let lon = address.lon {
                        
                        let score = member.score ?? "999" //999 is other
                        let doubleValue = Double(score) ?? 999 //999 is other
                        var key = "needs_help"
                        
                        if doubleValue < 50 {
                            key = "urgent_needs"
                        } else if doubleValue > 50 && doubleValue < 75 {
                            key = "stable"
                        } else if doubleValue > 75 && doubleValue < 100 {
                            key = "resilient"
                        } else {
                            key = "location"
                        }
                        
                        if var data = json[key] {
                            data.append(WellupReportLatLng(lat: lon, 
                                                           lon:lat,
                                                           memberId: member.id?.uuidString))
                            json[key] = data
                        } else {
                            json[key] = [WellupReportLatLng(lat: lon, 
                                                            lon:lat,
                                                            memberId: member.id?.uuidString)]
                        }
                    }
                }
                
                return req.eventLoop.future(json)
            }
    }
    
}


extension WellupReportBuilder {
    
    func test(req: Request) {
        let directory = req.application.directory.publicDirectory +  "csv/"
        let fileManager = FileManager.default
        if fileManager.fileExists(atPath: directory) {
            print("directory exsists: \(directory)")
        }
    }
    
    func export(req: Request) throws -> EventLoopFuture<String> {
        let report = ExportData(model: self.model,
                                config: ReportCreator.configuration(type: .allData(orgId: self.model.input.org, reportDate: self.model.input.combineDate())))
        return try report.exportData(req: req).flatMap { response in
            return req.eventLoop.future(response.downloadLink)
        }
    }
    
    func exportDashboardOnly(req: Request) throws -> EventLoopFuture<String> {
        var localPaths = self.paths
        let reportGenerator = ReportGenerator()
        return try exportMembers(req: req).flatMap { filePaths in
            localPaths.append(contentsOf: filePaths)
            
            return try! exportTotalUnenrolled(req: req).flatMap { filePaths in
                localPaths.append(contentsOf: filePaths)
                
                return try! exportTotalPending(req: req).flatMap { filePaths in
                    localPaths.append(contentsOf: filePaths)
                    
                    return try! exportHousehold(req: req).flatMap { filePaths in
                        localPaths.append(contentsOf: filePaths)
                        
                        return try! exportInsurances(req: req).flatMap { filePaths in
                            localPaths.append(contentsOf: filePaths)
                            
                            return try! exportAssessments(req: req).flatMap { filePaths in
                                localPaths.append(contentsOf: filePaths)
                                
                                return try! exportTotalActivePlans(req: req).flatMap { filePaths in
                                    localPaths.append(contentsOf: filePaths)
                                    
                                    return try! exportServices(req: req).flatMap { filePaths in
                                        localPaths.append(contentsOf: filePaths)
                                        
                                        return try! exportAppointments(req: req).flatMap { filePaths in
                                            localPaths.append(contentsOf: filePaths)
                                            
                                            return try! exportTasks(req: req).flatMap { filePaths in
                                                localPaths.append(contentsOf: filePaths)
                                                
                                                return try! exportNotes(req: req).flatMap { filePaths in
                                                    localPaths.append(contentsOf: filePaths)
                                                    
                                                    return try! exportChats(req: req).flatMap { filePaths in
                                                        localPaths.append(contentsOf: filePaths)
                                                        
                                                        return try! reportGenerator.createZipFile(req: req, fileName: "compress-\(Int(Date().timeIntervalSince1970))", from: localPaths).flatMap { response in
                                                            print(response)
                                                            return reportGenerator.uploadToS3(req: req, zipFileURL: response, config: config).flatMap({ response in
                                                                return getDownloadFolder(req: req, config: config)
                                                            })
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    
    fileprivate func uploadMembersData(fileName: String, req:Request, members: [Member]) throws -> EventLoopFuture<[URL]> {
        let rows = members.map({CSVCreator().memberRowWithAddress(member: $0)}).joined()
        let csvText = "\(MemberWithAddress.headers())\(rows)"
        let fileName = fileName
        
        return try! ReportGenerator().generateAndCollectCSV(fileName: fileName, csvText: csvText, req: req, isEmpty: false, filePaths: paths)
        //        return try! ReportGenerator().generateCSV(fileName: fileName, csvText: csvText, req: req, isEmpty: false, config: config)
    }
    
    
    fileprivate func exportMembers(req: Request) throws -> EventLoopFuture<[URL]> {
        return Member.query(on: req.db)
            .filter(\.$status == "active")
            .group(.or) { orGroup in
                orGroup.filter(\.$unenrolledDate == nil)
                orGroup.filter(\.$unenrolledDate == "")
            }
            .filter(\.$enrolledOn >= model.input.startDate)
            .filter(\.$enrolledOn <= model.input.endDate)
            .filter(\.$org.$id == UUID(model.org)!)
            .with(\.$households, { house in
                house.with(\.$address)
            })
            .all().flatMap { members in
                return try! uploadMembersData(fileName: "members", req: req, members: members)
            }
    }
    
    func exportTotalUnenrolled(req: Request) throws -> EventLoopFuture<[URL]> {
        return Member.query(on: req.db)
            .filter(\.$unenrolledDate >= model.input.startDate)
            .filter(\.$unenrolledDate <= model.input.endDate)
            .filter(\.$org.$id == UUID(model.org)!)
            .with(\.$households, { house in
                house.with(\.$address)
            })
            .all().flatMap { members in
                return try! uploadMembersData(fileName: "unenrolled", req: req, members: members)
            }
    }
    
    func exportTotalPending(req: Request) throws -> EventLoopFuture<[URL]> {
        return Member.query(on: req.db)
            .filter(\.$status != "active")
            .filter(\.$unenrolledDate == "")
            .filter(\.$createdAt >= model.start)
            .filter(\.$createdAt <= model.end)
            .filter(\.$org.$id == UUID(model.org)!)
            .with(\.$households, { house in
                house.with(\.$address)
            })
            .all().flatMap { members in
                return try! uploadMembersData(fileName: "pending",req: req, members: members)
            }
    }
    
    fileprivate func exportHousehold(req: Request) throws -> EventLoopFuture<[URL]> {
        return Household.query(on: req.db)
            .join(parent: \.$headOfHouse)
            .filter(Member.self, \.$firstName != "")
            .filter(\.$org.$id == UUID(model.org)!)
            .with(\.$address)
            .all().flatMap { members in
                let rows = members.map({CSVCreator().householdRow(household: $0)}).joined()
                let csvText = "\(Households.headers())\(rows)"
                let fileName = "households"
                //                let config = ReportCreator.configuration(type: .wellup(orgId: model.org))
//                return try! ReportGenerator().generateCSV(fileName: fileName, csvText: csvText, req: req, isEmpty: false, config: config)
                return try! ReportGenerator().generateAndCollectCSV(fileName: fileName, csvText: csvText, req: req, isEmpty: false, filePaths: paths)
            }
        
    }
    
    fileprivate func exportInsurances(req: Request) throws -> EventLoopFuture<[URL]> {
        return InsuranceCard.query(on: req.db)
            .filter(\.$createdAt >= model.start)
            .filter(\.$createdAt <= model.end)
            .with(\.$policy)
            .all().flatMap { members in
                
                let rows = members.map({ InsuranceCardCSV.row(card: $0, policy: $0.policy!)}).joined()
                let csvText = "\(InsuranceCardCSV.headers())\(rows)"
                let fileName = "insurances"
                return try! ReportGenerator().generateAndCollectCSV(fileName: fileName, csvText: csvText, req: req, isEmpty: false, filePaths: paths)
//                return try! ReportGenerator().generateCSV(fileName: fileName, csvText: csvText, req: req, isEmpty: false, config: config)
            }
    }
    
    func exportAssessments(req: Request)  throws -> EventLoopFuture<[URL]> {
        return Survey.query(on: req.db)
            .filter(\.$createdAt >= model.start)
            .filter(\.$createdAt <= model.end)
            .filter(\.$orgID == model.org)
            .all()
            .flatMap { assessments in
                let rows = assessments.map({ SurveyCSV.row(survey: $0)}).joined()
                let csvText = "\(SurveyCSV.headers())\(rows)"
                let fileName = "assessments"
                return try! ReportGenerator().generateAndCollectCSV(fileName: fileName, csvText: csvText, req: req, isEmpty: false, filePaths: paths)
//                return try! ReportGenerator().generateCSV(fileName: fileName, csvText: csvText, req: req, isEmpty: false, config: config)
                
            }
    }
    
    func exportTotalActivePlans(req: Request) throws -> EventLoopFuture<[URL]> {
        return CarePackage.query(on: req.db)
            .filter(\.$createdAt >= model.start)
            .filter(\.$createdAt <= model.end)
            .filter(\.$status == "active")
            .filter(\.$org.$id == UUID(model.org)!)
            .all().flatMap { plans in
                let rows = plans.map({ CarePackageCSV.row(plan: $0)}).joined()
                let csvText = "\(CarePackageCSV.headers())\(rows)"
                let fileName = "social_plans"
                return try! ReportGenerator().generateAndCollectCSV(fileName: fileName, csvText: csvText, req: req, isEmpty: false, filePaths: paths)
//                return try! ReportGenerator().generateCSV(fileName: fileName, csvText: csvText, req: req, isEmpty: false, config: config)
            }
    }
    
    func exportServices(req: Request) throws -> EventLoopFuture<[URL]> {
        return CarePackage.query(on: req.db)
            .filter(\.$createdAt >= model.start)
            .filter(\.$createdAt <= model.end)
            .filter(\.$status == "active")
            .filter(\.$org.$id == UUID(model.org)!)
            .all(\.$id)
            .flatMap { cpIds  in
                return CarePackageItem.query(on: req.db)
                    .with(\.$network)
                    .filter(\.$carepackage.$id ~~ cpIds)
                    .all()
                    .flatMap { services in
                        let rows = services.map({ CarePackageItemCSV.row(item: $0)}).joined()
                        let csvText = "\(CarePackageItemCSV.headers())\(rows)"
                        let fileName = "network_services"
                        return try! ReportGenerator().generateAndCollectCSV(fileName: fileName, csvText: csvText, req: req, isEmpty: false, filePaths: paths)
//                        return try! ReportGenerator().generateCSV(fileName: fileName, csvText: csvText, req: req, isEmpty: false, config: config)
                    }
            }
    }
    
    func exportAppointments(req: Request) throws -> EventLoopFuture<[URL]> {
        return Appointment.query(on: req.db)
            .filter(\.$createdAt >= model.start)
            .filter(\.$createdAt <= model.end)
            .filter(\.$org.$id == UUID(model.org)!)
            .all()
            .flatMap { appointments  in
                let rows = appointments.map({ AppointmentsCSV.row(appointment: $0)}).joined()
                let csvText = "\(AppointmentsCSV.headers())\(rows)"
                let fileName = "appointments"
                return try! ReportGenerator().generateAndCollectCSV(fileName: fileName, csvText: csvText, req: req, isEmpty: false, filePaths: paths)
//                return try! ReportGenerator().generateCSV(fileName: fileName, csvText: csvText, req: req, isEmpty: false, config: config)
            }
    }
    
    
    func exportTasks(req: Request)  throws -> EventLoopFuture<[URL]> {
        return TaskModel.query(on: req.db)
            .filter(\.$createdAt >= model.start)
            .filter(\.$createdAt <= model.end)
            .filter(\.$org.$id == UUID(model.org)!)
            .with(\.$assignee)
            .all()
            .flatMap { tasks  in
                let rows = tasks.map({ TaskCSV.row(task: $0)}).joined()
                let csvText = "\(TaskCSV.headers())\(rows)"
                let fileName = "tasks"
                return try! ReportGenerator().generateAndCollectCSV(fileName: fileName, csvText: csvText, req: req, isEmpty: false, filePaths: paths)
//                return try! ReportGenerator().generateCSV(fileName: fileName, csvText: csvText, req: req, isEmpty: false, config: config)
            }
    }
    
    func exportNotes(req: Request) throws -> EventLoopFuture<[URL]> {
        return User.query(on: req.db)
            .filter(\.$org.$id == UUID(model.org)!)
            .all(\.$id).flatMap { ids in
                return Note.query(on: req.db)
                    .filter(\.$createdAt >= model.start)
                    .filter(\.$createdAt <= model.end)
                    .filter(\.$creator.$id ~~ ids)
                    .with(\.$member)
                    .all().flatMap { notes in
                        let rows = notes.map({ NoteCSV.row(note: $0)}).joined()
                        let csvText = "\(NoteCSV.headers())\(rows)"
                        let fileName = "notes"
                        return try! ReportGenerator().generateAndCollectCSV(fileName: fileName, csvText: csvText, req: req, isEmpty: false, filePaths: paths)
//                        return try! ReportGenerator().generateCSV(fileName: fileName, csvText: csvText, req: req, isEmpty: false, config: config)
                    }
            }
    }
    
//    func exportAttachments(req: Request) throws -> EventLoopFuture<ClientResponse> {
//        return Attachment.query(on: req.db)
//            .filter(\.$createdAt >= model.start)
//            .filter(\.$createdAt <= model.end)
//            .filter(\.$org.$id == UUID(model.org)!)
//            .all().flatMap { notes in
//                let rows = notes.map({ AttachmentCSV.row(attachment: $0)}).joined()
//                let csvText = "\(AttachmentCSV.headers())\(rows)"
//                let fileName = "attachments"
//                return try! ReportGenerator().generateCSV(fileName: fileName, csvText: csvText, req: req, isEmpty: false, config: config)
//            }
//    }
    
    func exportChats(req: Request) throws -> EventLoopFuture<[URL]> {
        return Chat.query(on: req.db)
            .filter(\.$createdAt >= model.start)
            .filter(\.$createdAt <= model.end)
            .filter(\.$org.$id == UUID(model.org)!)
            .all().flatMap { notes in
                let rows = notes.map({ ChatsCSV.row(chat: $0)}).joined()
                let csvText = "\(ChatsCSV.headers())\(rows)"
                let fileName = "chats"
                return try! ReportGenerator().generateAndCollectCSV(fileName: fileName, csvText: csvText, req: req, isEmpty: false, filePaths: paths)
//                return try! ReportGenerator().generateCSV(fileName: fileName, csvText: csvText, req: req, isEmpty: false, config: config)
            }
    }
    
    func getDownloadFolder(req:Request, config: ReportConfiguration) -> EventLoopFuture<String> {
        let s3 = S3(client: req.aws.client, region: .useast1)
        let folder = config.folder()
        let urlString = "https://wellup-reporting.s3.amazonaws.com/\(WellupReportBuilder.env)/\(config.orgID)/\(folder)/\(folder).zip"
        print(urlString)
        
        return s3.signURL(url: URL(string: urlString)!,
                          httpMethod: .GET, expires: .hours(24)).map { url in
            return url.absoluteString
        }
    }
}
