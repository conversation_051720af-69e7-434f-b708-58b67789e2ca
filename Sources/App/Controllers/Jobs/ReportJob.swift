//
//  File.swift
//  
//
//  Created by <PERSON> on 8/24/23.
//
import Vapor
import Fluent
import Queues
import Foundation

struct ReportingJob: Codable {
    var organization: String
    var reportKey: String
}

struct EndOfMonthJob: ScheduledJob {
    
//    var org: String
    var config: ReportConfiguration
    
    struct Payload: Codable {}
    
    func run(context: Queues.QueueContext) -> NIOCore.EventLoopFuture<Void> {
        let app = context.application
        
        let today = Date()
        let calendar = Calendar.current
        let day = calendar.component(.day, from: today)
        let range = calendar.range(of: .day, in: .month, for: today)
        let lastDay = range?.last
        
        guard day == lastDay else {
            app.logger.info("today is not the last day of the month")
            return context.eventLoop.makeSucceededFuture(())
        }
        
        guard let dates = getCurrentMonthStartEndDates() else {
            return context.eventLoop.future()
        }
         
        let input = ReportInput(startDate: dates.start,
                                endDate: dates.end,
                                org: config.orgID)
        
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        app.logger.info("Kicking off job by formatting.")
        guard let start = dateFormatter.date(from: input.startDate),
              let end = dateFormatter.date(from: input.endDate) else {
            return context.eventLoop.future()
        }
        
        do {
            let jsonData = try JSONEncoder().encode(input)
            let buffer = ByteBuffer(data: jsonData)
            print("encoding.")
            app.logger.info("job encoding.")
            
            var headers = HTTPHeaders()
            headers.add(name: .contentType, value: "application/json")
            
            let request = Request(application: app, headers: headers, collectedBody: buffer, on: context.eventLoop)
            
            let cloudwatch = CloudWatchLogger(req: request, logGroupName: .jobs)
        
            let report = ExportData(model: ReportQuery(start: start, end: end, org: input.org, input: input),
                                    config: config)
            
            return try! report.exportData(req: request).flatMap { response in
                print(response)
                return cloudwatch.putLog(message: logJob(name: "End Of Moth Report", params: "\(start) - \(end) for \(input.org)"), on: context.eventLoop)
            }
        }
        catch {
            app.logger.info("Failed to encode")
            return context.eventLoop.makeFailedFuture(error)
        }
    }

    func error(_ context: QueueContext, _ error: Error, _ payload: Payload) -> EventLoopFuture<Void> {
        // Handle any errors here
        context.application.logger.error("Failed to complete EndOfMonthJob: \(error)")
        return context.eventLoop.makeSucceededVoidFuture()
    }
    
    func getCurrentMonthStartEndDates() -> (start: String, end: String)? {
        let calendar = Calendar.current
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        
        // Get the current date
        let currentDate = Date()
        
        // Get the start of the current month
        guard let startOfMonth = calendar.date(from: calendar.dateComponents([.year, .month], from: currentDate)) else {
            return nil
        }
        
        // Get the end of the current month
        guard let range = calendar.range(of: .day, in: .month, for: currentDate) else {
            return nil
        }
        let endOfMonth = calendar.date(byAdding: .day, value: range.count - 1, to: startOfMonth)
        
        // Format the dates to strings
        let startOfMonthString = dateFormatter.string(from: startOfMonth)
        let endOfMonthString = endOfMonth != nil ? dateFormatter.string(from: endOfMonth!) : ""
        
        return (start: startOfMonthString, end: endOfMonthString)
    }
    
    //MARK: - CloudWatchLogs
    fileprivate func logJob(name: String, params: String) -> String {
        return CloudWatchLogMessage.send(msg: .jobs(name: name, prams: params))
    }
}

struct ReportJob: ScheduledJob {

    func run(context: QueueContext) -> EventLoopFuture<Void> {
        let client = context.application.client
        let url = queryURL(urlString: "http://127.0.0.1:8080/api/report")
        let uri = URI(string: url!)
        
        let json = [
            "startDate": "2023-08-01",
            "endDate": "2023-08-24",
            "report": "dona",
            "deIdentified":"false"
        ]
        
        guard let data = try? JSONEncoder().encode(json) else { return context.eventLoop.makeSucceededFuture(()) }
        
        return client.post(uri, headers: [
            "Content-type":"application/json",
            "Authorization": "Bearer zQ2bGlI177TEffXyeMYBrw=="
        ], beforeSend: { req in
            print(req)
            req.body = ByteBuffer.init(data: data)
        }).map { response in
            print(response)
        }
    }
}
