//
//  File.swift
//  
//
//  Created by <PERSON> on 5/8/23.
//

import Foundation
import Vapor
import Foundation
import Queues



struct APNSPush: Codable {
    var deviceID:String
    var userID:String
    var title:String
    var subtitle:String
    var msg:String
}

struct APNSJob: Job {
    
    typealias Payload = APNSPush

    func dequeue(_ context: QueueContext, _ payload: APNSPush) -> EventLoopFuture<Void> {
        
        return context.application.apns.send(
            .init(title: payload.title,
                  subtitle: payload.subtitle,
                  body: payload.msg),
            to: payload.deviceID
        )
    }

    func error(_ context: QueueContext, _ error: Error, _ payload: APNSPush) -> EventLoopFuture<Void> {
        // If you don't want to handle errors you can simply return a future. You can also omit this function entirely.
        print(error)
        return context.eventLoop.future()
    }
}


struct TestPush: Codable {
    var deviceID:String
}
struct ReportTestJob: Job {
    
    typealias Payload = TestPush

    func dequeue(_ context: QueueContext, _ payload: TestPush) -> EventLoopFuture<Void> {
        
        let client = context.application.client
        let url = queryURL(urlString: "http://127.0.0.1:8080/api/report")
        let uri = URI(string: url!)
        
        let json = [
            "startDate": "2023-08-01",
            "endDate": "2023-08-24",
            "orgID": "f7f9780d-4fb1-44e7-9965-93a6f584d6ee",
            "deIdentified":"false"
        ]
        
        guard let data = try? JSONEncoder().encode(json) else { return context.eventLoop.makeSucceededFuture(()) }
        
        return client.post(uri, headers: [
            "Content-type":"application/json",
            "Authorization": "Bearer zQ2bGlI177TEffXyeMYBrw=="
        ], beforeSend: { req in
            print(req)
            req.body = ByteBuffer.init(data: data)
        }).map { response in
            print(response)
        }
    }

    func error(_ context: QueueContext, _ error: Error, _ payload: TestPush) -> EventLoopFuture<Void> {
        // If you don't want to handle errors you can simply return a future. You can also omit this function entirely.
        return context.eventLoop.future()
    }
}

