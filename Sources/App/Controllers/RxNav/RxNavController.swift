//
//  RxNavController.swift
//  
//
//  Created by <PERSON> on 6/22/25.
//

import Foundation
import Vapor
import Fluent

// MARK: - Request/Response Models

struct MedicationSearchRequest: Content {
    let name: String
    let includeDetails: Bool?
}

struct MedicationSearchResponse: Content {
    let medications: [MedicationInfo]
    let searchTerm: String
    let totalResults: Int
}

struct MedicationInfo: Content {
    let rxcui: String
    let fullName: String
    let strength: String?
    let doseForm: String?
    let route: String?
    let displayName: String
    let brandName: String?
    let synonym: String?
    let tty: String
    let isGeneric: Bool
    let isPrescribable: Bool?
    let schedule: String?
    let humanDrug: String?
}

struct MedicationDetailRequest: Content {
    let rxcui: String
}

struct MedicationDetailResponse: Content {
    let medication: DetailedMedicationInfo
}

struct DetailedMedicationInfo: Content {
    let rxcui: String
    let fullName: String
    let strength: String?
    let doseForm: String?
    let route: String?
    let displayName: String
    let brandName: String?
    let synonym: String?
    let tty: String
    let isGeneric: Bool
    let isPrescribable: Bool?
    let schedule: String?
    let humanDrug: String?
    let properties: [MedicationProperty]
    let codes: [MedicationCode]
    let sources: [String]
}

struct MedicationProperty: Content {
    let category: String
    let name: String
    let value: String
}

struct MedicationCode: Content {
    let type: String
    let value: String
}

// MARK: - RxNav API Response Models

struct RxNavDrugsResponse: Content {
    let drugGroup: DrugGroup
}

struct DrugGroup: Content {
    let name: String?
    let conceptGroup: [ConceptGroup]
}

struct ConceptGroup: Content {
    let tty: String
    let conceptProperties: [ConceptProperty]?
}

struct ConceptProperty: Content {
    let rxcui: String
    let name: String
    let synonym: String?
    let tty: String
    let language: String
    let suppress: String
    let umlscui: String?
}

struct RxNavPropertiesResponse: Content {
    let propConceptGroup: PropConceptGroup
}

struct PropConceptGroup: Content {
    let propConcept: [PropConcept]
}

struct PropConcept: Content {
    let propCategory: String
    let propName: String
    let propValue: String
}

// MARK: - Additional API Response Models

struct RxNavApproximateResponse: Content {
    let approximateGroup: ApproximateGroup?
}

struct ApproximateGroup: Content {
    let candidate: [ApproximateCandidate]?
}

struct ApproximateCandidate: Content {
    let rxcui: String?
    let name: String?
    let score: String?
}

struct RxNavSpellingSuggestionsResponse: Content {
    let suggestionGroup: SuggestionGroup?
}

struct SuggestionGroup: Content {
    let suggestionList: SuggestionList?
}

struct SuggestionList: Content {
    let suggestion: [String]?
}

// MARK: - Controller

struct RxNavController: RouteCollection {
    
    func boot(routes: RoutesBuilder) throws {
        let rxnav = routes.grouped("rxnav")
        rxnav.post("search", use: searchMedications)
        rxnav.post("search", "approximate", use: searchApproximateMedications)
        rxnav.post("search", "spelling", use: getSpellingSuggestions)
        rxnav.post("search", "brand", use: searchByBrandName)
        rxnav.post("search", "ingredient", use: searchByIngredient)
        rxnav.post("details", use: getMedicationDetails)
        rxnav.get("health", use: healthCheck)
    }
    
    // MARK: - Route Handlers
    
    func searchMedications(req: Request) throws -> EventLoopFuture<MedicationSearchResponse> {
        let searchRequest = try req.content.decode(MedicationSearchRequest.self)

        return searchDrugs(req: req, name: searchRequest.name)
            .flatMap { rxNavResponse in
                let medications = self.convertToMedicationInfo(rxNavResponse: rxNavResponse)

                if searchRequest.includeDetails == true {
                    // Get detailed information for each medication
                    let detailFutures = medications.map { medication in
                        return self.getMedicationProperties(req: req, rxcui: medication.rxcui)
                            .map { properties in
                                return self.enhanceMedicationWithProperties(medication: medication, properties: properties)
                            }
                    }

                    return EventLoopFuture.whenAllSucceed(detailFutures, on: req.eventLoop)
                        .map { enhancedMedications in
                            return MedicationSearchResponse(
                                medications: enhancedMedications,
                                searchTerm: searchRequest.name,
                                totalResults: enhancedMedications.count
                            )
                        }
                } else {
                    return req.eventLoop.future(MedicationSearchResponse(
                        medications: medications,
                        searchTerm: searchRequest.name,
                        totalResults: medications.count
                    ))
                }
            }
    }

    func healthCheck(req: Request) throws -> EventLoopFuture<[String: String]> {
        // Test the RxNav API connectivity
        let testURL = URI(string: "https://rxnav.nlm.nih.gov/REST/version")

        return req.client.get(testURL)
            .map { response in
                if response.status == HTTPResponseStatus.ok {
                    return [
                        "status": "healthy",
                        "service": "RxNav API",
                        "timestamp": ISO8601DateFormatter().string(from: Date()),
                        "version": "1.0.0"
                    ]
                } else {
                    return [
                        "status": "unhealthy",
                        "service": "RxNav API",
                        "error": "API not responding",
                        "timestamp": ISO8601DateFormatter().string(from: Date()),
                        "version": "1.0.0"
                    ]
                }
            }
            .recover { error in
                return [
                    "status": "unhealthy",
                    "service": "RxNav API",
                    "error": error.localizedDescription,
                    "timestamp": ISO8601DateFormatter().string(from: Date()),
                    "version": "1.0.0"
                ]
            }
    }

    func getMedicationDetails(req: Request) throws -> EventLoopFuture<MedicationDetailResponse> {
        let detailRequest = try req.content.decode(MedicationDetailRequest.self)

        return getMedicationProperties(req: req, rxcui: detailRequest.rxcui)
            .flatMap { propertiesResponse in
                // First get basic info by searching for the medication name
                return self.getRxcuiName(req: req, rxcui: detailRequest.rxcui)
                    .map { name in
                        let detailedInfo = self.convertToDetailedMedicationInfo(
                            rxcui: detailRequest.rxcui,
                            name: name,
                            properties: propertiesResponse
                        )
                        return MedicationDetailResponse(medication: detailedInfo)
                    }
            }
    }

    func searchApproximateMedications(req: Request) throws -> EventLoopFuture<MedicationSearchResponse> {
        let searchRequest = try req.content.decode(MedicationSearchRequest.self)

        return getApproximateMatch(req: req, term: searchRequest.name)
            .flatMap { approximateResponse in
                // Extract RXCUIs from approximate match and get detailed info
                let rxcuis = self.extractRxcuisFromApproximateMatch(approximateResponse)

                let medicationFutures = rxcuis.map { rxcui in
                    return self.getRxcuiName(req: req, rxcui: rxcui)
                        .map { name in
                            return MedicationInfo(
                                rxcui: rxcui,
                                fullName: name,
                                strength: self.extractStrength(from: name),
                                doseForm: self.extractDoseForm(from: name),
                                route: self.extractRoute(from: name),
                                displayName: name,
                                brandName: self.extractBrandName(from: name, tty: ""),
                                synonym: nil,
                                tty: "",
                                isGeneric: true,
                                isPrescribable: nil,
                                schedule: nil,
                                humanDrug: nil
                            )
                        }
                }

                return EventLoopFuture.whenAllSucceed(medicationFutures, on: req.eventLoop)
                    .map { medications in
                        return MedicationSearchResponse(
                            medications: medications,
                            searchTerm: searchRequest.name,
                            totalResults: medications.count
                        )
                    }
            }
    }

    func getSpellingSuggestions(req: Request) throws -> EventLoopFuture<[String: [String]]> {
        let searchRequest = try req.content.decode(MedicationSearchRequest.self)

        return getSpellingSuggestionsFromAPI(req: req, term: searchRequest.name)
            .map { suggestions in
                return ["suggestions": suggestions]
            }
    }

    func searchByBrandName(req: Request) throws -> EventLoopFuture<MedicationSearchResponse> {
        let searchRequest = try req.content.decode(MedicationSearchRequest.self)

        // Search for brand name and filter results to only include branded drugs
        return searchDrugs(req: req, name: searchRequest.name)
            .map { rxNavResponse in
                let allMedications = self.convertToMedicationInfo(rxNavResponse: rxNavResponse)
                // Filter to only include branded medications (SBD, BPCK)
                let brandedMedications = allMedications.filter { !$0.isGeneric }

                return MedicationSearchResponse(
                    medications: brandedMedications,
                    searchTerm: searchRequest.name,
                    totalResults: brandedMedications.count
                )
            }
    }

    func searchByIngredient(req: Request) throws -> EventLoopFuture<MedicationSearchResponse> {
        let searchRequest = try req.content.decode(MedicationSearchRequest.self)

        // Search for ingredient and filter results to only include generic drugs
        return searchDrugs(req: req, name: searchRequest.name)
            .map { rxNavResponse in
                let allMedications = self.convertToMedicationInfo(rxNavResponse: rxNavResponse)
                // Filter to only include generic medications (SCD, GPCK)
                let genericMedications = allMedications.filter { $0.isGeneric }

                return MedicationSearchResponse(
                    medications: genericMedications,
                    searchTerm: searchRequest.name,
                    totalResults: genericMedications.count
                )
            }
    }

    // MARK: - Helper Methods

    private func searchDrugs(req: Request, name: String) -> EventLoopFuture<RxNavDrugsResponse> {
        let encodedName = name.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? name
        let searchURL = URI(string: "https://rxnav.nlm.nih.gov/REST/drugs.json?name=\(encodedName)")

        return req.client.get(searchURL).flatMapThrowing { response in
            guard response.status == HTTPResponseStatus.ok else {
                throw Abort(.badRequest, reason: "RxNav API error: \(response.status)")
            }
            return try response.content.decode(RxNavDrugsResponse.self)
        }
    }

    private func getMedicationProperties(req: Request, rxcui: String) -> EventLoopFuture<RxNavPropertiesResponse> {
        let propertiesURL = URI(string: "https://rxnav.nlm.nih.gov/REST/rxcui/\(rxcui)/allProperties.json?prop=all")

        return req.client.get(propertiesURL).flatMapThrowing { response in
            guard response.status == HTTPResponseStatus.ok else {
                throw Abort(.badRequest, reason: "RxNav API error: \(response.status)")
            }
            return try response.content.decode(RxNavPropertiesResponse.self)
        }
    }

    private func getRxcuiName(req: Request, rxcui: String) -> EventLoopFuture<String> {
        let nameURL = URI(string: "https://rxnav.nlm.nih.gov/REST/rxcui/\(rxcui).json")

        return req.client.get(nameURL).flatMapThrowing { response in
            guard response.status == HTTPResponseStatus.ok else {
                return "Unknown"
            }
            let json = try response.content.decode([String: String].self)
            return json["name"] ?? "Unknown"
        }.recover { _ in
            return "Unknown"
        }
    }

    private func getApproximateMatch(req: Request, term: String) -> EventLoopFuture<RxNavApproximateResponse> {
        let encodedTerm = term.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? term
        let approximateURL = URI(string: "https://rxnav.nlm.nih.gov/REST/approximateTerm.json?term=\(encodedTerm)")

        return req.client.get(approximateURL).flatMapThrowing { response in
            guard response.status == HTTPResponseStatus.ok else {
                throw Abort(.badRequest, reason: "RxNav API error: \(response.status)")
            }
            return try response.content.decode(RxNavApproximateResponse.self)
        }
    }

    private func getSpellingSuggestionsFromAPI(req: Request, term: String) -> EventLoopFuture<[String]> {
        let encodedTerm = term.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? term
        let suggestionsURL = URI(string: "https://rxnav.nlm.nih.gov/REST/spellingsuggestions.json?name=\(encodedTerm)")

        return req.client.get(suggestionsURL).flatMapThrowing { response in
            guard response.status == HTTPResponseStatus.ok else {
                return []
            }
            let suggestionsResponse = try response.content.decode(RxNavSpellingSuggestionsResponse.self)
            return suggestionsResponse.suggestionGroup?.suggestionList?.suggestion ?? []
        }.recover { _ in
            return []
        }
    }

    private func extractRxcuisFromApproximateMatch(_ response: RxNavApproximateResponse) -> [String] {
        var rxcuis: [String] = []

        if let candidates = response.approximateGroup?.candidate {
            for candidate in candidates {
                if let rxcui = candidate.rxcui {
                    rxcuis.append(rxcui)
                }
            }
        }

        return rxcuis
    }

    private func convertToMedicationInfo(rxNavResponse: RxNavDrugsResponse) -> [MedicationInfo] {
        var medications: [MedicationInfo] = []

        for conceptGroup in rxNavResponse.drugGroup.conceptGroup {
            guard let conceptProperties = conceptGroup.conceptProperties else { continue }

            for concept in conceptProperties {
                let medicationInfo = MedicationInfo(
                    rxcui: concept.rxcui,
                    fullName: concept.name,
                    strength: extractStrength(from: concept.name),
                    doseForm: extractDoseForm(from: concept.name),
                    route: extractRoute(from: concept.name),
                    displayName: concept.synonym ?? concept.name,
                    brandName: extractBrandName(from: concept.name, tty: concept.tty),
                    synonym: concept.synonym,
                    tty: concept.tty,
                    isGeneric: isGenericMedication(tty: concept.tty),
                    isPrescribable: nil,
                    schedule: nil,
                    humanDrug: nil
                )
                medications.append(medicationInfo)
            }
        }

        return medications
    }

    private func enhanceMedicationWithProperties(medication: MedicationInfo, properties: RxNavPropertiesResponse) -> MedicationInfo {
        var isPrescribable: Bool? = nil
        var schedule: String? = nil
        var humanDrug: String? = nil

        for prop in properties.propConceptGroup.propConcept {
            switch prop.propName {
            case "PRESCRIBABLE":
                isPrescribable = prop.propValue == "Y"
            case "SCHEDULE":
                schedule = prop.propValue
            case "HUMAN_DRUG", "RXNAV_HUMAN_DRUG":
                humanDrug = prop.propValue
            default:
                break
            }
        }

        return MedicationInfo(
            rxcui: medication.rxcui,
            fullName: medication.fullName,
            strength: medication.strength,
            doseForm: medication.doseForm,
            route: medication.route,
            displayName: medication.displayName,
            brandName: medication.brandName,
            synonym: medication.synonym,
            tty: medication.tty,
            isGeneric: medication.isGeneric,
            isPrescribable: isPrescribable,
            schedule: schedule,
            humanDrug: humanDrug
        )
    }

    private func convertToDetailedMedicationInfo(rxcui: String, name: String, properties: RxNavPropertiesResponse) -> DetailedMedicationInfo {
        var medicationProperties: [MedicationProperty] = []
        var codes: [MedicationCode] = []
        var sources: [String] = []

        var isPrescribable: Bool? = nil
        var schedule: String? = nil
        var humanDrug: String? = nil

        for prop in properties.propConceptGroup.propConcept {
            let medicationProperty = MedicationProperty(
                category: prop.propCategory,
                name: prop.propName,
                value: prop.propValue
            )
            medicationProperties.append(medicationProperty)

            // Extract specific values
            switch prop.propName {
            case "PRESCRIBABLE":
                isPrescribable = prop.propValue == "Y"
            case "SCHEDULE":
                schedule = prop.propValue
            case "HUMAN_DRUG", "RXNAV_HUMAN_DRUG":
                humanDrug = prop.propValue
            default:
                break
            }

            // Extract codes
            if prop.propCategory == "CODES" {
                let code = MedicationCode(type: prop.propName, value: prop.propValue)
                codes.append(code)
            }

            // Extract sources
            if prop.propCategory == "SOURCES" {
                sources.append(prop.propValue)
            }
        }

        return DetailedMedicationInfo(
            rxcui: rxcui,
            fullName: name,
            strength: extractStrength(from: name),
            doseForm: extractDoseForm(from: name),
            route: extractRoute(from: name),
            displayName: name,
            brandName: extractBrandName(from: name, tty: ""),
            synonym: nil,
            tty: "",
            isGeneric: true,
            isPrescribable: isPrescribable,
            schedule: schedule,
            humanDrug: humanDrug,
            properties: medicationProperties,
            codes: codes,
            sources: Array(Set(sources)) // Remove duplicates
        )
    }

    // MARK: - Utility Methods

    internal func extractStrength(from name: String) -> String? {
        // Extract strength patterns like "10 MG", "5 ML", "250 MCG", etc.
        let strengthPattern = #"(\d+(?:\.\d+)?)\s*(MG|ML|MCG|G|L|IU|UNIT|%)"#
        let regex = try? NSRegularExpression(pattern: strengthPattern, options: .caseInsensitive)
        let range = NSRange(location: 0, length: name.utf16.count)

        if let match = regex?.firstMatch(in: name, options: [], range: range) {
            return String(name[Range(match.range, in: name)!])
        }

        return nil
    }

    internal func extractDoseForm(from name: String) -> String? {
        // Extract dose forms like "Oral Tablet", "Injection", "Capsule", etc.
        let doseFormPattern = #"(Oral\s+)?(Tablet|Capsule|Injection|Solution|Cream|Ointment|Gel|Patch|Inhaler|Spray|Drops|Suspension|Syrup|Powder|Granules|Suppository|Enema|Lotion|Foam|Film|Strip|Disc|Ring|Insert|Implant|Device)"#
        let regex = try? NSRegularExpression(pattern: doseFormPattern, options: .caseInsensitive)
        let range = NSRange(location: 0, length: name.utf16.count)

        if let match = regex?.firstMatch(in: name, options: [], range: range) {
            return String(name[Range(match.range, in: name)!])
        }

        return nil
    }

    internal func extractRoute(from name: String) -> String? {
        // Extract routes like "Oral", "Topical", "Intravenous", etc.
        let routePattern = #"(Oral|Topical|Intravenous|Intramuscular|Subcutaneous|Inhalation|Nasal|Ophthalmic|Otic|Rectal|Vaginal|Transdermal|Sublingual|Buccal)"#
        let regex = try? NSRegularExpression(pattern: routePattern, options: .caseInsensitive)
        let range = NSRange(location: 0, length: name.utf16.count)

        if let match = regex?.firstMatch(in: name, options: [], range: range) {
            return String(name[Range(match.range, in: name)!])
        }

        return nil
    }

    internal func extractBrandName(from name: String, tty: String) -> String? {
        // For branded drugs (SBD, BPCK), extract brand name from brackets
        if tty == "SBD" || tty == "BPCK" {
            let brandPattern = #"\[([^\]]+)\]"#
            let regex = try? NSRegularExpression(pattern: brandPattern, options: [])
            let range = NSRange(location: 0, length: name.utf16.count)

            if let match = regex?.firstMatch(in: name, options: [], range: range),
               let brandRange = Range(match.range(at: 1), in: name) {
                return String(name[brandRange])
            }
        }

        return nil
    }

    internal func isGenericMedication(tty: String) -> Bool {
        // SCD = Semantic Clinical Drug (generic)
        // SBD = Semantic Branded Drug (brand)
        // GPCK = Generic Pack
        // BPCK = Branded Pack
        return tty == "SCD" || tty == "GPCK"
    }
}
