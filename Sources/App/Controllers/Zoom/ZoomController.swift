//
//  File.swift
//  
//
//  Created by <PERSON> on 8/6/23.
//

import Foundation
import Fluent
import Vapor

enum ZoomMeetingMeta: String {
    case zoomMeetingID
}

let defaultMeetingRecurrence = ZoomMeetingRecurrence(type: 1, repeat_interval: 1)
let defaultMeetingSettings = ZoomMeetingSettings(
    host_video:        "true",
    participant_video: "true",
    join_before_host:  "False",
    mute_upon_entry:   "False",
    watermark:         "true",
    audio:             "voip",
    auto_recording:    "cloud"
)


struct TokenResponse: Content {
    var access_token:  String
    var token_type:    String
    var expires_in:    Int
    var scope:         String
}

struct ZoomMeetingRecurrence: Content {
    var type: Int?  //1
    var repeat_interval: Int? //: 1
}

struct ZoomMeetingSettings: Content {
    var host_video:          String? //: "true",
    var participant_video:   String? //: "true",
    var join_before_host:    String? //: "False",
    var mute_upon_entry:     String? //: "False",
    var watermark:           String? //: "true",
    var audio:               String? //: "voip",
    var auto_recording:      String? //: "cloud"
    var meeting_invitees:    [[String:String]]? //: {"email": "<EMAIL>"}
    
}

struct ZoomMeeting: Content {
    var topic:        String? //: "Train Like a Ninja with Todd Super User",
    var type:         Int? //: 2,
    var start_time:   String? //: "2023-08-02T10: 21: 57",
    var duration:     String? //: "45" duration in mins,
    var timezone:     String? //: "america/Florida", UTC
    var agenda:       String? //: "test",
    var recurrence:   ZoomMeetingRecurrence?
    var settings:     ZoomMeetingSettings?
    
    
}

struct ZoomMeetingResponse: Content {
    var uuid: String? // "mV7dnWlQRUyyX/0IkCLkMQ==",
    var id: Int? // 81642588377,
    var host_id: String? // "q24fiwSGS5-UpqAkbFJmnQ",
    var host_email: String? // "<EMAIL>",
    var topic: String? // "Learn about Zala Hubs",
    var type: Int? // 2,
    var status: String? // "waiting",
    var start_time: String? // "2023-08-03T14:21:57Z",
    var duration: Int? // 45,
    var timezone: String? // "America/New_York",
    var agenda: String? // "Were going to host a Zala Hub workshop",
    var created_at: String? // "2023-08-01T19:36:28Z",
    var start_url: String? // "https://us02web.zoom.us/s/81642588377?zak=eyJ0eXAiOiJKV1QiLCJzdiI6IjAwMDAwMSIsInptX3NrbSI6InptX28ybSIsImFsZyI6IkhTMjU2In0.eyJhdWQiOiJjbGllbnRzbSIsInVpZCI6InEyNGZpd1NHUzUtVXBxQWtiRkptblEiLCJpc3MiOiJ3ZWIiLCJzayI6IjAiLCJzdHkiOjEwMCwid2NkIjoidXMwMiIsImNsdCI6MCwibW51bSI6IjgxNjQyNTg4Mzc3IiwiZXhwIjoxNjkwOTI1Nzg4LCJpYXQiOjE2OTA5MTg1ODgsImFpZCI6IjZUcTIybWR3Ukhpcmluc0hwY2h3dWciLCJjaWQiOiIifQ.7WfWC-sJdk8qZDs2axKaETgEz--XzNEZg1IWvQEQu4w",
    var join_url: String? // "https://us02web.zoom.us/j/81642588377?pwd=ZjNOZWpiZktMUUo2WXB2SHBEbnBGUT09",
    var password: String? // "YW8EYG",
    var h323_password: String? // "738863",
    var pstn_password: String? // "738863",
    var encrypted_password: String? // "ZjNOZWpiZktMUUo2WXB2SHBEbnBGUT09",
    
    func formattedDateAndTime() -> String {
        guard let startDate = self.start_time else { return "" }
        guard let date = DateFormatter.dateFromMultipleFormats(fromString: startDate) else { return "" }
        let formatter = DateFormatter()
        formatter.timeStyle = .medium
        formatter.dateStyle = .long
        return formatter.string(from: date)
    }
}

let zoomUserID:String = "70FhBLCTRfmneZfluLa_fw"


struct ZoomController: RouteCollection {
    func boot(routes: RoutesBuilder) throws {
//        let todos = routes.grouped("zoom")
//        todos.post("token", use: token)
//        todos.post("meeting", use: meeting)
    }
    
//    func token(req: Request) async throws -> TokenResponse {
//        try await createToken(req: req)
//    }
    
    func createMeeting(req: Request, input:ZoomMeeting) throws -> EventLoopFuture<ZoomMeetingResponse>  {
        let url = queryURL(urlString: "https://api.zoom.us/v2/users/\(zoomUserID)/meetings")
        let uri = URI(string: url!)
        
        let jsonData = try JSONEncoder().encode(input)
        
        return try! createToken(req: req).flatMap { token in
            return req.client.post(uri, headers: [
                "content-type":"application/json",
                "Authorization":"Bearer \(token.access_token)"], beforeSend: { req in
                
                req.body =  ByteBuffer.init(data: jsonData)
            }).flatMap { response in
                print(response)
                if let convo = try? response.content.decode(ZoomMeetingResponse.self) {
                    return req.eventLoop.future(convo)
                } else {
                    return req.eventLoop.makeFailedFuture(Abort(.badRequest))
                }
            }
        }
    }
    
    func deleteZoomMeeting(req: Request, zoomMeetingID:String) throws -> EventLoopFuture<ClientResponse>  {
        let url = queryURL(urlString: "https://api.zoom.us/v2/meetings/\(zoomMeetingID)")
        let uri = URI(string: url!)
                
        return try! createToken(req: req).flatMap { token in
            return req.client.delete(uri, headers: [
                "content-type":"application/json",
                "Authorization":"Bearer \(token.access_token)"]).flatMap { response in
                return req.eventLoop.future(response)
            }
        }
    }
   
    static func meetingMessage(phone:String, name:String, navigator:String, url:String, date:String) -> SMSInput {
        let msg = "Hi \(name.capitalized)! \n\nYou have been invited to a zoom meeting by \(navigator). The meeting is scheduled to take place on \(date)\n\n Please click on the link below to join the meeting 10 minutes before your scheduled time.\n\nZoom Link: \(url)\n\nIf you have any questions, please don't hesitate to reach out to a Dona Health representative.\n\n Thank you!"
        return SMSInput(to: phone, body: msg)
    }
    
    func createToken(req: Request) throws -> EventLoopFuture<TokenResponse> {
        let url = queryURL(urlString: "https://zoom.us/oauth/token")
        let uri = URI(string: url!)
        guard let data = self.dataEncode() else { throw Abort(.badRequest) }
        return req.client.post(uri, headers: [
            "content-type":"application/x-www-form-urlencoded",
            "Host":"zoom.us",
            "Authorization":"Basic \(self.basicAuth())"], beforeSend: { req in
            
            req.body = ByteBuffer.init(data: data)
        }).flatMap { response in
            print(response)
            if let convo = try? response.content.decode(TokenResponse.self) {
                return req.eventLoop.future(convo)
            } else {
                return req.eventLoop.makeFailedFuture(Abort(.badRequest))
            }
        }
    }
    
    fileprivate func basicAuth() -> String {
        let clientID = Environment.get("zoomClientID") ?? ""
        let secret = Environment.get("zoomSecret") ?? ""
        let loginString = String(format: "%@:%@", clientID, secret)
        let loginData = loginString.data(using: String.Encoding.utf8)!
        return loginData.base64EncodedString()
    }
    
    fileprivate func dataEncode() -> Data? {
        let accountID = Environment.get("zoomAccountID") ?? ""
        return "grant_type=account_credentials&account_id=\(accountID)".data(using: .utf8)
    }
}



