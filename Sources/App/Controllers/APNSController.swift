//
//  File.swift
//  
//
//  Created by <PERSON> on 5/8/23.
//

import Foundation
import Vapor
import Fluent
import SotoSNS

struct APNSPayload: Codable {
    let aps: APSPayload
    let customData: String // Example custom data

    struct APSPayload: Codable {
        let alert: AlertPayload
        let sound: String

        struct AlertPayload: Codable {
            let title: String
            let body: String
        }
    }
}


struct DeviceInput: Content {
    var deviceID:String
    var userID:String
    var model:String?
    
    func deviceModel(arn:String, subArn: String?) -> Device {
        return Device(deviceID: deviceID, userID: userID.lowercased(), model: model, arn: arn, subArn: subArn)
    }
}


struct APNSPushController: RouteCollection {
    
    func boot(routes: RoutesBuilder) throws {
        
        let device = routes.grouped("device")
        device.get("all", use: devices)
        device.post("register", use: register)
        device.post("unregister", use: unregister)
        device.post("reregister", use: reregister)
        device.post("noteTest", use: noteTest)
    }
    
    func devices(req: Request) throws -> EventLoopFuture<[Device]> {
        let user:String? = req.query["userId"]
        guard let id = user else { throw NetworkError.error(type: .user) }
        return Device.query(on: req.db)
            .group(.or) { device in
                device.filter(\.$userID == id.lowercased())
                device.filter(\.$userID == id.uppercased())
            }
            .all()
    }
    
    func verify(req: Request) throws -> EventLoopFuture<Device> {
        guard let id = req.parameters.get("userID") else { throw NetworkError.error(type: .user) }
        return Device.query(on: req.db)
            .group(.or) { device in
                device.filter(\.$userID == id.lowercased())
                device.filter(\.$userID == id.uppercased())
            }
            .first().flatMapThrowing() {model in
            guard let foundModel = model else {  throw NetworkError.error(type: .device) }
            return foundModel
        }
    }
    
    func noteTest(req: Request) throws -> EventLoopFuture<SuccessResposne> {
        let input = try req.content.decode(DeviceInput.self)
        return Device.query(on: req.db)
            .group(.or) { device in
                device.filter(\.$userID == input.userID.lowercased())
                device.filter(\.$userID == input.userID.uppercased())
            }
            .all().flatMap { models in
            if models.isEmpty {
                return req.eventLoop.future(SuccessResposne(res: "device not registered"))
            } else {
                return try!APNSPushController.send(req: req,
                                                   userID: input.userID,
                                                   title: "Wellup Notification",
                                                   subtitle: "Your device is ready for notifications.",
                                                   msg: "Your device is ready for notifications.").flatMap { _ in
                    return req.eventLoop.future(SuccessResposne(res: "pending"))
                }
            }
        }
    }
    
    
    func removeAllDevices(req: Request) throws -> EventLoopFuture<Void> {
        let input = try req.content.decode(DeviceInput.self)
        return Device.query(on: req.db)
            .group(.or) { device in
                device.filter(\.$deviceID == input.deviceID.lowercased())
                device.filter(\.$deviceID == input.deviceID.uppercased())
            }
            .all().flatMap { devices in
                return devices.delete(on: req.db).flatMap({_ in
                    return devices.sequencedFlatMapEach(on: req.eventLoop) { device in
                        return deleteApplicationEndpoint(req: req, arn: device.arn ?? "", subArn: device.subscriptionArn)
                    }
                })
        }
    }
    
    
    func reregister(req: Request) throws -> EventLoopFuture<SuccessResposne> {
        let input = try req.content.decode(DeviceInput.self)
        return try removeAllDevices(req: req).flatMap { _ in
            return try! register(req: req).flatMap({ response in
                if response.res == "failure" {
                    return req.eventLoop.future(SuccessResposne(res: "failed"))
                } else {
                    return try!APNSPushController.send(req: req,
                                                       userID: input.userID,
                                                       title: "Wellup Notification",
                                                       subtitle: "Your device is ready for notifications.",
                                                       msg: "Your device is ready for notifications.").flatMap { _ in
                        return req.eventLoop.future(SuccessResposne(res: "enabled"))
                    }
                }
            })
        }
    }
    
    func register(req: Request) throws -> EventLoopFuture<SuccessResposne> {
        let input = try req.content.decode(DeviceInput.self)
        return Device.query(on: req.db)
            .group(.or) { device in
                device.filter(\.$userID == input.userID.lowercased())
                device.filter(\.$userID == input.userID.uppercased())
            }
            .group(.or) { device in
                device.filter(\.$deviceID == input.deviceID.lowercased())
                device.filter(\.$deviceID == input.deviceID.uppercased())
            }
            .all().flatMap { models in
                if models.isEmpty {
                    return createApplicationEndpoint(req: req, deviceId: input.deviceID, userId: input.userID).flatMap { arnData in
                        return input.deviceModel(arn: arnData.arn, subArn: arnData.subArn).create(on: req.db).flatMap { _ in
                            return req.eventLoop.future(SuccessResposne(res: "success"))
                        }
                    }
                } else {
                    return req.eventLoop.future(SuccessResposne(res: "success"))
                }
            }
    }
    
    static func verify(req: Request) throws -> EventLoopFuture<Device> {
        guard let id = req.parameters.get("userID") else { throw NetworkError.error(type: .user) }
        return Device.query(on: req.db)
            .group(.or) { status in
                status.filter(\.$userID == id.lowercased())
                status.filter(\.$userID == id.uppercased())
            }
            .first().flatMapThrowing() {model in
            guard let foundModel = model else {  throw NetworkError.error(type: .device) }
            return foundModel
        }
    }
    
    static func find(req: Request, id:String) throws -> EventLoopFuture<Device> {
        return Device.query(on: req.db)
            .group(.or) { device in
                device.filter(\.$deviceID == id.lowercased())
                device.filter(\.$deviceID == id.uppercased())
            }
            .first().flatMapThrowing() {model in
            guard let foundModel = model else {  throw NetworkError.error(type: .device) }
            return foundModel
        }
    }
    
    func unregister(req: Request) throws -> EventLoopFuture<SuccessResposne> {
        let input = try req.content.decode(DeviceInput.self)
        return try APNSPushController.find(req: req, id: input.deviceID).flatMap { device in
            return deleteApplicationEndpoint(req: req, arn: device.arn ?? "", subArn: device.subscriptionArn).flatMap { _ in
                return device.delete(on: req.db).transform(to: SuccessResposne(res: "success"))
            }
        }
    }
    
    func createApplicationEndpoint(req: Request, deviceId: String, userId: String) -> EventLoopFuture<(arn:String, subArn: String?)> {
        let snsClient = SNS(client: req.aws.client, region: .useast1, timeout: .seconds(180))
        return snsClient.createPlatformEndpoint(SNS.CreatePlatformEndpointInput(
            customUserData: userId,
            platformApplicationArn: .navigatorEndpoint,
            token: deviceId)).flatMap { response in
                return snsClient.subscribe(SNS.SubscribeInput(
                    endpoint: response.endpointArn ?? "",          // The ARN of the device (mobile push)
                    protocol: "application",                       // Protocol type for mobile push notifications
                    topicArn: PushTopic.navigatorSubscriptionTopic // The ARN of the topic you're subscribing to
                )).flatMap { subscribeResponse in
                    if let subArn = subscribeResponse.subscriptionArn {
                        print("Successfully subscribed! Subscription ARN: \(subArn)")
                        return req.eventLoop.future((response.endpointArn ?? "", subArn))
                    } else {
                        print("Subscription failed")
                        return req.eventLoop.future((response.endpointArn ?? "", nil))
                    }
                }
            }
    }
    
    func deleteApplicationEndpoint(req: Request, arn: String, subArn: String?) -> EventLoopFuture<Void> {
        let snsClient = SNS(client: req.aws.client, region: .useast1, timeout: .seconds(90))
        return snsClient.deleteEndpoint(SNS.DeleteEndpointInput(endpointArn: arn)).flatMap { _ in
            if let subArn {
                return snsClient.unsubscribe(SNS.UnsubscribeInput.init(subscriptionArn: subArn))
            } else {
                return req.eventLoop.future()
            }
        }
    }
    
    
    static func send(req: Request, userID:String, title:String, subtitle:String, msg:String) throws -> EventLoopFuture<Void> {
        let snsClient = SNS(client: req.aws.client, region: .useast1, timeout: .seconds(90))
        return Device.query(on: req.db)
            .group(.or) { status in
                status.filter(\.$userID == userID.lowercased())
                status.filter(\.$userID == userID.uppercased())
            }
            .all().flatMap { models in
            if models.isEmpty {
                return req.eventLoop.future()
            } else {
//                let inputs = models.compactMap({createTestMesssage(model:$0, title: subtitle, msg: msg)})
//                snsClient.publishBatch(.init(publishBatchRequestEntries: inputs, topicArn: <#T##String#>))
//                return checkIfEndpointIsEnabled(client: snsClient, endpointArn: arn).flatMap { isEnabled in
//                    if isEnabled {
//                        return snsClient.publish(input).flatMap { response in
//                            return req.eventLoop.future()
//                        }
//                    } else {
//                        print("\nNOT ENABLED\nDevice: registered \(model.deviceID) for user:\(userID) with \(arn)\n")
//                        return req.eventLoop.future()
//                    }
//                }

                return models.sequencedFlatMapEach(on: req.eventLoop) { model in
                    let arn = model.arn ?? ""
                    print("\nDevice: registered \(model.deviceID) for user:\(userID) with \(arn)\n")
                    let message: [String: Any] = [
                        "default": "This is the default message",
                        "\(PushTopic.apns)": """
                        {
                            "aps": {
                                "alert": {
                                    "title": "\(subtitle)",
                                    "body": "\(msg)"
                                },
                                "sound": "default"
                            }
                        }
                        """,
                        "GCM": """
                        {
                            "notification": {
                                "title": "\(subtitle)",
                                "body": "\(msg)"
                            }
                        }
                        """
                    ]
                    let jsonString = message.toJSONString() ?? ""
                    let input = SNS.PublishInput(
                        message: jsonString, //String.trimStringToMaxCharacters(subtitle, maxCharacters: 100),
                        messageStructure: "json",
                        targetArn: arn)
                    return checkIfEndpointIsEnabled(client: snsClient, endpointArn: arn).flatMap { isEnabled in
                        if isEnabled {
                            return snsClient.publish(input).flatMap { response in
                                return req.eventLoop.future()
                            }
                        } else {
                            print("\nNOT ENABLED\nDevice: registered \(model.deviceID) for user:\(userID) with \(arn)\n")
                            return req.eventLoop.future()
                        }
                    }
                }
            }
        }
    }
    
    static func createTestMesssage(model: Device,
                                   title: String,
                                   msg: String) -> SNS.PublishBatchRequestEntry {
        let arn = model.arn ?? ""
        let message: [String: Any] = [
            "default": "This is the default message",
            "\(PushTopic.apns)": """
            {
                "aps": {
                    "alert": {
                        "title": "\(title)",
                        "body": "\(msg)"
                    },
                    "sound": "default"
                }
            }
            """,
            "GCM": """
            {
                "notification": {
                    "title": "\(title)",
                    "body": "\(msg)"
                }
            }
            """
        ]
        let jsonString = message.toJSONString() ?? ""
        let input = SNS.PublishBatchRequestEntry(id: UUID().uuidString,
                                     message: jsonString,
                                     messageStructure: "json")
//        let input = SNS.PublishInput(
//            message: jsonString, //String.trimStringToMaxCharacters(subtitle, maxCharacters: 100),
//            messageStructure: "json",
//            targetArn: arn)
        return input
    }
    
    static func checkIfEndpointIsEnabled(client: SNS, endpointArn: String) -> EventLoopFuture<Bool> {
        let input = SNS.GetEndpointAttributesInput(endpointArn: endpointArn)
        return client.getEndpointAttributes(input)
            .map { response in
                print(response)
                if let attributes = response.attributes,
                    let enabled = attributes["Enabled"] {
                    return enabled == "true"
                }
                return false
            }
    }
}

extension Dictionary where Key == String, Value == Any {
    func toJSONString(prettyPrinted: Bool = false) -> String? {
        // Convert dictionary to JSON data
        let options: JSONSerialization.WritingOptions = prettyPrinted ? .prettyPrinted : []
        
        if let jsonData = try? JSONSerialization.data(withJSONObject: self, options: options) {
            // Convert JSON data to String
            return String(data: jsonData, encoding: .utf8)
        }
        return nil
    }
}
