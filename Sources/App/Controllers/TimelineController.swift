//
//  File.swift
//  
//
//  Created by <PERSON> on 5/15/24.
//

import Foundation
import Vapor
import Fluent
import Queues

struct TimelineControllerController: RouteCollection {
    
    func boot(routes: RoutesBuilder) throws {
        let content = routes.grouped("timeline")
        content.get(use: index)
        content.get("status", use: status)
    }
    
    //MARK - Fetch
    func status(req: Request) throws -> EventLoopFuture<[MemberStatus]> {
        guard let id:String = req.query["memberId"] else { throw NetworkError.error(type: .content) }
        guard let memberID = UUID(id) else { throw NetworkError.error(type: .content)}
        return MemberStatus.query(on: req.db)
            .group(.or) { status in
                status.filter(\.$endDate == nil)
                status.filter(\.$endDate == "")
            }
            .join(parent: \.$member)
            .filter(Member.self, \.$id == memberID)
            .with(\.$address)
            .all()
    }
    
    func index(req: Request) throws -> EventLoopFuture<Page<TimelineItem>> {
        guard let id:String = req.query["memberId"] else { throw NetworkError.error(type: .content) }
        guard let memberID = UUID(id) else { throw NetworkError.error(type: .content)}
//        let status:String?           = req.query["status"]
        let query = TimelineItem.query(on: req.db)
            .filter(\.$memberId == memberID)
            .filter(\.$visible == true)
            .with(\.$creator) { user in
                user.with(\.$attachments)
            }
            .sort(\.$createdAt, .descending)
        return query.paginate(for: req)
    }
    
    
//    //MARK: - Query
    fileprivate func buildQuery(query:QueryBuilder<WellupContent>, req:Request) -> QueryBuilder<WellupContent> {
        let name:String?             = req.query["name"]
        let focus:String?            = req.query["focus"]
        let kind:String?            = req.query["kind"]
        let product:String?          = req.query["product"]
        let tags:[String]?           = req.query["tags"]
        let state:String?            = req.query["state"]
        
        if let nm = name  {
            query.filter(\.$title, .custom("ilike"), "%\(nm.lowercased())%")
        }

        if let kind  {
            query.filter(\.$kind == kind)
        }

        if let tags, !tags.isEmpty {
            query.join(Tag.self, on: \WellupContent.$id == \Tag.$content.$id)
            query.filter(Tag.self, \.$key ~~ tags)
        }
        
//        if let product = product {
//            query.filter(\.$product == product)
//        }
//
//        if let ref = ref?.lowercased() {
//            query.filter(\.$ref == ref)
//        }
        
        if let state = state {
            query.filter(\.$state == state)
        }
        
        return query
            .with(\.$attachments)
            .with(\.$tags)
    }
    
    static func create(_ items: [TimelineItem], creatorId: UUID, req: Request) throws -> EventLoopFuture<Void> {
        if items.isEmpty {
            return req.eventLoop.future()
            
        } else {
            return items.create(on: req.db).transform(to: items).flatMap { savedItems in
                return savedItems.sequencedFlatMapEach(on: req.eventLoop) { updatedTimeLineItem in
                    updatedTimeLineItem.$creator.id = creatorId
                    return updatedTimeLineItem.update(on: req.db).eventLoop.future()
                }.flatMap { _ in
                    return req.eventLoop.future()
                }
            }
        }
    }
}
