//
//  File.swift
//  
//
//  Created by <PERSON> on 1/14/24.
//

import Foundation
import Vapor
import Fluent

struct AddressService {
    
    static func updateAddressIfNeeded(req:Request, input:AddressInput?, model: any Model) throws -> EventLoopFuture<Void> {
        guard let input = input else { return req.eventLoop.future() }
        if let member = model as? Member {
            return try filterAndUpdate(req: req, member: member, input: input).flatMap { address in
                return try! MapboxService.updateAddress(req: req, address: address)
            }
        } 
        else if let household = model as? Household {
            return try filterAndUpdate(req: req, household: household, input: input).flatMap { address in
                return try! MapboxService.updateAddress(req: req, address: address)
            }
        }
        else if let network = model as? Network {
            return try filterAndUpdate(req: req, network: network, input: input).flatMap { address in
                return try! MapboxService.updateAddress(req: req, address: address)
            }
        }
        else if let team = model as? Team {
            return try filterAndUpdate(req: req, team: team, input: input).flatMap { address in
                return try! MapboxService.updateAddress(req: req, address: address)
            }
        }
        else if let task = model as? TaskModel {
            return try filterAndUpdate(req: req, task: task, input: input).flatMap { address in
                return try! MapboxService.updateAddress(req: req, address: address)
            }
        }
        else if let status = model as? MemberStatus {
            return try filterAndUpdate(req: req, memberStatus: status, input: input).flatMap { address in
                return try! MapboxService.updateAddress(req: req, address: address)
            }
        }
        else {
            return req.eventLoop.future()
        }
    }
    
    static func updateMultipleAddressIfNeeded(req:Request, input:[AddressInput]?, policy:InsurancePolicy) throws -> EventLoopFuture<InsurancePolicy> {
        if let addressInput = input, !addressInput.isEmpty {
            let address = addressInput.compactMap({$0.address()})
            return policy.$address.create(address, on: req.db).transform(to: policy)
        } else {
            return req.eventLoop.future(policy)
        }
    }
    
    static private func filterAndUpdate(req: Request, member: Member, input: AddressInput) throws -> EventLoopFuture<Address> {
        if var mainAddress = member.address.filter({$0.kind == input.kind?.lowercased() ?? "main"}).last {
            mainAddress = input.returnUpdatedModel(address: mainAddress)
            return mainAddress.update(on: req.db).transform(to: mainAddress)
       } else {
           let address = input.address()
           return member.$address.create(address, on: req.db).transform(to: address)
       }
    }
    
    static private func filterAndUpdate(req: Request, household: Household, input: AddressInput) throws -> EventLoopFuture<Address> {
        if var mainAddress = household.address.filter({$0.kind == input.kind?.lowercased() ?? "main"}).last {
            mainAddress = input.returnUpdatedModel(address: mainAddress)
            return mainAddress.update(on: req.db).transform(to: mainAddress)
       } else {
           let address = input.address()
           return household.$address.create(address, on: req.db).transform(to: address)
       }
    }

    static private func filterAndUpdate(req: Request, network: Network, input: AddressInput) throws -> EventLoopFuture<Address> {
        if var mainAddress = network.address.filter({$0.kind == input.kind?.lowercased() ?? "main"}).last {
            mainAddress = input.returnUpdatedModel(address: mainAddress)
            return mainAddress.update(on: req.db).transform(to: mainAddress)
       } else {
           let address = input.address()
           return network.$address.create(address, on: req.db).transform(to: address)
       }
    }
    
    static private func filterAndUpdate(req: Request, team: Team, input: AddressInput) throws -> EventLoopFuture<Address> {
        if var mainAddress = team.address.filter({$0.kind == input.kind?.lowercased() ?? "main"}).last {
            mainAddress = input.returnUpdatedModel(address: mainAddress)
            return mainAddress.update(on: req.db).transform(to: mainAddress)
       } else {
           let address = input.address()
           return team.$address.create(address, on: req.db).transform(to: address)
       }
    }

    static private func filterAndUpdate(req: Request, task: TaskModel, input: AddressInput) throws -> EventLoopFuture<Address> {
        if var mainAddress = task.location {
            mainAddress = input.returnUpdatedModel(address: mainAddress)
            return mainAddress.update(on: req.db).transform(to: mainAddress)
       } else {
           let address = input.address()
           return task.$location.create(address, on: req.db).transform(to: address)
       }
    }
    
    static private func filterAndUpdate(req: Request, memberStatus: MemberStatus, input: AddressInput) throws -> EventLoopFuture<Address> {
        if var mainAddress = memberStatus.address.filter({$0.kind == input.kind?.lowercased() ?? "main"}).last {
            mainAddress = input.returnUpdatedModel(address: mainAddress)
            return mainAddress.update(on: req.db).transform(to: mainAddress)
       } else {
           let address = input.address()
           return memberStatus.$address.create(address, on: req.db).transform(to: address)
       }
    }
}
