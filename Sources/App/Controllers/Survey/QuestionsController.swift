
import Foundation
import Vapor
import Fluent


struct QuestionsController: RouteCollection {
    
    var questionID = "questionID"
    
    func boot(routes: RoutesBuilder) throws {
        let networks = routes.grouped("questions")
        networks.get(":\(questionID)", use: find)
        networks.get([":\(questionID)","answers"], use: questionAnswers)
        
        networks.get(use: index)
        
        networks.put(":\(questionID)", use: update)
        
        networks.delete(":\(questionID)", use: delete)
        
        networks.post(use: createQuestion)
    }
    
    //MARK: - Query
    
    func find(req: Request) throws -> EventLoopFuture<Question> {
        guard let sectionID = req.parameters.get(questionID), let id = UUID(sectionID) else {
            throw NetworkError.error(type: .notFound, msg: "survey_id is required")
        }
        return Question.query(on: req.db).filter(\.$id == id).with(\.$questions).first().unwrap(or:  Abort(.notFound))
    }
    
    static func findQuestion(req: Request, id:String) throws -> EventLoopFuture<Question> {
        return Question.query(on: req.db).filter(\.$id == UUID(uuidString: id)!).with(\.$questions).first().flatMapThrowing() {model in
            guard let foundModel = model else { throw NetworkError.error(type: .member) }
            return foundModel
        }
    }
    
    func questionAnswers(req:Request) throws -> EventLoopFuture<[Answer]> {
        guard let questionID = req.parameters.get("\(questionID)") else { throw Abort(.notFound, reason: "question_id is required") }
        
        let emptyAnswer:[Answer] = []
        
        let query = Answer.query(on: req.db).filter(\.$parentID == questionID)
        
        if let latest:Bool? = req.query["latest"], latest == true {
            return query.sort(\.$createdAt, .descending).first().flatMap { ans in
                if let answer = ans {
                    return req.eventLoop.future([answer])
                } else {
                    return req.eventLoop.future(emptyAnswer)
                }
            }
        } else {
            
            return Answer.query(on: req.db)
                .filter(\.$parentID, .custom("ilike"), "%\(questionID)%").all()
        }
        
//        return try QuestionsController.findQuestion(req: req, id: questionID).flatMap { question in
//            let ids = question.questions.compactMap({$0.id?.uuidString})
//            print("ids:\(ids)")
//
//        }
    }
    
    func sectionsForSurvey(req:Request) throws -> EventLoopFuture<[Section]> {
        guard let surveyID = req.parameters.get("surveyID"),
            let id = UUID(surveyID) else {
                throw Abort(.notFound, reason: "survey_id is required")
        }
        return Section.query(on: req.db).filter(\.$survey.$id == id).all()
    }
    
    func index(req: Request) throws -> EventLoopFuture<[Question]> {
        return Question.query(on: req.db).all()
    }
    
    func findSection(req: Request, sectionID:String) throws -> EventLoopFuture<Section> {
        guard let id = UUID(sectionID) else { throw Abort(.notFound, reason: "section_id is not found") }
        return Section.query(on: req.db).filter(\.$id == id).first().unwrap(or: Abort(.notFound))
    }
    
    func findSectionWithQuestions(req: Request, sectionID:String) throws -> EventLoopFuture<Section> {
        guard let id = UUID(sectionID) else { throw Abort(.notFound, reason: "section_id is not found") }
        return Section.query(on: req.db).with(\.$questions).filter(\.$id == id).first().unwrap(or: Abort(.notFound))
    }
    
    
    //MARK: - Update
    
    func update(req: Request) throws -> EventLoopFuture<Section> {
        let questionInput    = try req.content.decode(QuestionInput.self)
        guard let sectionID   = questionInput.sectionID else {throw Abort(.notFound,  reason: "Section ID is required")  }
        let foundQuestion = try self.find(req: req)
        if let questionsInputs = questionInput.questions {
            let questions = try questionsInputs.reduce(into: [Question]()) { (questions, input) in
                      let title       = input.title
                guard let message     = input.message   else { throw Abort(.notFound, reason: "Message is required")  }
                guard let level       = input.level     else { throw Abort(.notFound, reason: "Level is required")  }
                guard let value       = input.value     else { throw Abort(.notFound, reason: "Value is required")  }
                questions.append(Question(title: title, message: message, level: level, value: value, score: nil, sectionID: nil, questionID: nil, type: input.type))
            }
            return foundQuestion.flatMap() {model in
                model.$questions.create(questions, on: req.db).flatMap({ data in
                    return try! self.findSectionWithQuestions(req: req, sectionID: sectionID)
                })
            }
        } else {
            return foundQuestion.flatMap() { model in
                return try! self.updateModel(req: req, input: questionInput, model: model).flatMap({ data in
                    return try! self.findSectionWithQuestions(req: req, sectionID: sectionID)
                })
            }
        }
    }
    
    fileprivate func updateModel(req:Request, input:QuestionInput, model:Question) throws -> EventLoopFuture<Question> {
        let updatedModel  = input.returnUpdatedModel(section: model)
        return updatedModel.update(on: req.db).transform(to: updatedModel)
    }
    
    //MARK: - Create
    
    func createQuestion(req: Request) throws -> EventLoopFuture<Section> {
        let question = try req.content.decode(QuestionInput.self)
        guard let sectionID   = question.sectionID else {throw Abort(.notFound,  reason: "Section ID is required")  }
        guard let sectionUUID = UUID(sectionID)    else {throw Abort(.notFound,  reason: "Section ID is required")  }
              let title       = question.title
        guard let message     = question.message   else { throw Abort(.notFound, reason: "Message is required")  }
        guard let level       = question.level     else { throw Abort(.notFound, reason: "Level is required")  }
        guard let value       = question.value     else { throw Abort(.notFound, reason: "Value is required")  }
        return try self.findSection(req: req, sectionID: sectionID).flatMap({ section in
            return section.$questions.create(Question(title: title, message: message, level: level, value: value, score: nil, sectionID: sectionUUID, questionID: nil, type: question.type), on: req.db).flatMap({ data in
                return try! self.findSectionWithQuestions(req: req, sectionID: sectionID)
            })
        })
    }
    
    //MARK: - Delete
    
    func delete(req: Request) throws -> EventLoopFuture<HTTPStatus> {
        return Question.find(req.parameters.get(questionID), on: req.db)
            .unwrap(or: Abort(.notFound))
            .flatMap { $0.delete(on: req.db) }
            .transform(to: .ok)
    }
}


