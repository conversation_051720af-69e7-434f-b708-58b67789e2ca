//
//  File.swift
//
//
//  Created by <PERSON> on 7/1/21.
//

import Foundation
import Vapor
import Fluent


struct QuestionAnswer: Content {
    let question: Question
    let answers: [Answer]
}

struct SuccessResposne: Content {
    var res:String
}

struct SurveyCreateInput: Content {
    var name: String
    var memberID:UUID?
    var status:String
    var key:String
    var score:String
    var startedAt:Date?
    var endedAt:Date?
    var firstSurvey:Int?
    var lastSurvey:Int?
    var orgID:String
    
    func model() -> Survey {
        return Survey(name: name, memberID: memberID, status: status, key: key, score: score, startedAt: startedAt, endedAt: endedAt, firstSurvey: firstSurvey, lastSurvey: lastSurvey, orgID: orgID)
    }
}

struct SurveyUpdateInput: Content {
    static var param = "surveyID"
    
    var name:      String?
    var memberID:  UUID?
    var status:    String?
    var score:     String?
    var startedAt: Date?
    var endedAt:   Date?
    
    func returnUpdatedModel(survey:Survey) -> Survey {
        if let title = name {
            survey.name = title
        }
        
        if let memberID = memberID {
            survey.memberID = memberID
        }
        
        if let status = status {
            survey.status = status
        }
        
        if let score = score {
            survey.score = score
        }
        
        if let startedAt = startedAt {
            survey.startedAt = startedAt
        }
        
        if let endedAt = endedAt {
            survey.endedAt = endedAt
        }
        
        return survey
    }
}


struct SurveysController: RouteCollection {
    let surveyID = "surveyID"
    let constants = ApplicationConstants()
    func boot(routes: RoutesBuilder) throws {
        let networks = routes.grouped("surveys")
        networks.get(use: index)
        networks.get(":surveyID",                  use: find)
        networks.get([":surveyID","dashboard"],    use: buildDashboardForSurvey)
        networks.get([":surveyID","answers"],      use: findAnswers)
        networks.get([":surveyID","questionAnswers"],      use: questionAndAnswers)
        
//        networks.get([":surveyID","questions"],      use: questions)
        networks.get([":surveyID","sections"],     use: sectionsForSurvey)
        
        
        networks.put(":surveyID", use: update)
        networks.post(use: create)
        networks.post([":surveyID", "attachments"], use: createAttachment)
        networks.delete(":surveyID", use: delete)
    }
    
    //MARK: - Query
        
    func index(req: Request) throws -> EventLoopFuture<Page<Survey>>{
        let org:String?            = req.query["org"]
        guard let orgID = org else { throw NetworkError.error(type: .organization) }
        return self.buildQuery(query: Survey.query(on: req.db), req: req, orgID: orgID).sort(\.$startedAt, .descending).paginate(for: req)
    }
    
    static func validate(lang:String) -> Bool {
        return ApplicationConstants().consentLangs.contains(lang)
    }
    
    
    func find(req: Request) throws -> EventLoopFuture<Survey> {
        guard let id = req.parameters.get(surveyID) else { throw Abort(HTTPResponseStatus.badRequest) }
        return try self.querySurveyByID(id: id, req: req).flatMapThrowing() {model in
            guard let foundModel = model else { throw Abort(.notFound, reason: "surveyID is required") }
            return foundModel
        }
    }
    
    
    static func findSurvey(req: Request, id:String) throws -> EventLoopFuture<Survey> {
        guard let surveyID = UUID(id) else { throw Abort(.notFound, reason: "survey_id is required") }
        return Survey.query(on: req.db).filter(\.$id == surveyID)
            .with(\.$taker, { taker in
                taker.with(\.$teams)
            })
            .with(\.$attachments)
            .with(\.$sections, { section in
                section.with(\.$questions)
            }).first().flatMapThrowing() {model in
                guard let foundModel = model else { throw Abort(.notFound, reason: "surveyID is required") }
                return foundModel
            }
    }
    
    
    /// Fetch the latest survey for member
    /// - Parameters:
    ///   - req: Request
    ///   - memberID: String
    ///   - status: active || complete (default)
    /// - Returns: returns the latest survey based on status.
    static func findLatestSurvey(req: Request, memberID:String, status:String = "complete", useLast:Bool = true) throws -> EventLoopFuture<[Survey]> {
        let query =  Survey.query(on: req.db)
            .filter(\.$memberID == UUID(memberID)!)
            .with(\.$taker)
            .with(\.$attachments)
            .with(\.$sections, { section in
                section.with(\.$questions)
            })
            .filter(\.$key ~~ ["full_hrp"])
            .filter(\.$status == status)
//            .filter(\.$key ~~ ["full_hrp","rapid_hrp"]) if were going to support rapid we need a new graph type.
        if useLast {
            query.filter(\.$lastSurvey == 1)
        } else {
            query.filter(\.$firstSurvey == 1)
        }
        
        return query.sort(\.$endedAt, .descending).all().flatMap { surveys in
            if surveys.count > 1, let latest = surveys.filter({$0.key == "full_hrp"}).last {
                return req.eventLoop.future([latest])
            } else if surveys.count == 1, let latest = surveys.last {
                return req.eventLoop.future([latest])
            } else {
                return req.eventLoop.future([])
            }
        }                            
    }
    
    
    static func findLatestScoreableSurvey(req: Request, id:String) throws -> EventLoopFuture<Survey> {
        guard let surveyID = UUID(id) else { throw Abort(.notFound, reason: "survey_id is required") }
        return Survey.query(on: req.db)
            .filter(\.$id == surveyID)
            .with(\.$taker, { taker in
                taker.with(\.$teams)
            })
            .with(\.$attachments)
            .with(\.$sections, { section in
                section.with(\.$questions)
            })
            .first().flatMapThrowing() {model in
                guard let foundModel = model else { throw Abort(.notFound, reason: "surveyID is required") }
                return foundModel
            }
    }
    
    func createAttachment(req: Request) throws -> EventLoopFuture<Survey> {
        let input = try req.content.decode(UploadInput.self)
        return try! find(req: req).flatMap { survey in
            return try! AttachmentsController().uploadImageToCloudinary(req).flatMap { resposne in
                let attach = Attachment(name: input.name, 
                                        kind: input.type,
                                        type: "image",
                                        url: resposne.secure_url,
                                        category: input.category,
                                        refID: resposne.public_id)
                return survey.$attachments.create(attach, on: req.db).transform(to: survey)
            }
        }
    }
    
    fileprivate func lookupSurveyByID(id:String, req:Request) throws -> EventLoopFuture<Survey> {
        guard let surveyID = UUID(id) else { throw Abort(.notFound, reason: "survey_id is required") }
        return Survey.find(surveyID, on: req.db).unwrap(or: Abort(.notFound, reason: "Could not find survey \(surveyID)"))
    }
    
    fileprivate func lookupSectionByID(id:String, req:Request) throws -> EventLoopFuture<Section> {
        guard let sectionID = UUID(id) else { throw Abort(.notFound, reason: "section_id is required") }
        return Section.find(sectionID, on: req.db).unwrap(or: Abort(.notFound, reason: "Could not find section \(sectionID)"))
    }
    
    fileprivate func querySurveyByID(id:String, req:Request) throws -> EventLoopFuture<Survey?> {
        guard let surveyID = UUID(id) else { throw Abort(.notFound, reason: "survey_id is required") }
        return Survey.query(on: req.db)
            .with(\.$taker)
            .with(\.$sections) { section in
            section.with(\.$questions) { question in
                question.with(\.$questions) { question in
                    question.with(\.$questions)
                }
            }
        }.filter(\.$id == surveyID).first()
    }
    
    fileprivate func querySurveyByIDWithSections(id:String, req:Request) throws -> EventLoopFuture<Survey?> {
        guard let surveyID = UUID(id) else { throw Abort(.notFound, reason: "survey_id is required") }
        return Survey.query(on: req.db).with(\.$sections).filter(\.$id == surveyID).first()
    }
    
    fileprivate func updateFirstLastBitForSurvey(userID:UUID, req:Request, survey:Survey) throws -> EventLoopFuture<Survey?> {
        return Survey.query(on: req.db).filter(\.$status == "complete").filter(\.$key == survey.key).filter(\.$memberID == userID).count().flatMap { total in
            let isFirst = total <= 1
            if isFirst {
                survey.firstSurvey = 1
                survey.lastSurvey = 0
                return req.eventLoop.future(survey)
            } else {
                return try! self.removeLastSurveyAnchor(userID: userID, req: req, survey: survey).flatMap { survey in
                    survey.firstSurvey = 0
                    survey.lastSurvey = 1
                    return req.eventLoop.future(survey)
                }
            }
        }
    }
    
    fileprivate func removeLastSurveyAnchor(userID:UUID, req:Request, survey:Survey) throws -> EventLoopFuture<Survey> {
        return Survey.query(on: req.db)
            .filter(\.$status == "complete")
            .filter(\.$key == survey.key)
            .filter(\.$memberID == userID)
            .filter(\.$lastSurvey == 1)
            .sort(\.$endedAt, .descending).first().flatMap { result in
                if let rst = result {
                    rst.lastSurvey = 0
                    return rst.update(on: req.db).transform(to: survey)
                } else {
                    let srvy = survey
                    srvy.lastSurvey = 1
                    return srvy.update(on: req.db).transform(to: survey)
                }
        }
    }
    
    func sectionsForSurvey(req:Request) throws -> EventLoopFuture<[Section]> {
        guard let surveyID = req.parameters.get(surveyID),
            let id = UUID(surveyID) else {
            throw Abort(.notFound, reason: "survey_id is required")
        }
        return Section.query(on: req.db).filter(\.$survey.$id == id).all()
    }
    
    func findAnswers(req: Request) throws -> EventLoopFuture<[Answer]> {
        guard let surveyID = req.parameters.get(surveyID) else {
            throw Abort(HTTPResponseStatus.badRequest)
        }
        return Answer.query(on: req.db).filter(\.$surveyID == surveyID).all()
    }
    
    func questionAndAnswers(req: Request) throws -> EventLoopFuture<SurveyAnswers> {
        guard let surveyID = req.parameters.get(surveyID), let uuid = UUID(uuidString: surveyID) else {
            throw Abort(HTTPResponseStatus.badRequest)
        }
        return try! surveyFor(req: req, id: uuid).flatMap { survey in
            let questions:[Question] = survey.sections.flatMap({$0.questions}).flatMap({$0.questions}).compactMap({$0.questions}).joined { questions1, questions2 in
                var data:[Question] = []
                data.append(contentsOf: questions1)
                data.append(contentsOf: questions2)
                return data
            }
            return Template.query(on: req.db).filter(\.$key == survey.key).first().flatMap({ template in
                let isScore = template?.scored ?? false
                return try! answersForSurvey(req: req, id: surveyID).flatMap { answers in
                    return try! mapQuestionAnswers(req: req, questions: questions).flatMap({ value in
                        return req.eventLoop.future(SurveyAnswers(survey: survey, answers: answers, scored: isScore, questionAnswers: value))
                    })
//                    return questions.sequencedFlatMapEachCompact(on: req.eventLoop) { question in
//                        return try! findAnswer(req:req, question: question)
//                    }
                                        
                }
            })
        }
    }

    fileprivate func mapQuestionAnswers(req: Request, questions: [Question]) throws -> EventLoopFuture<[QuestionAnswer]> {
        return questions.sequencedFlatMapEach(on: req.eventLoop) { question in
            return try! findAnswer(req:req, question: question)
        }
    }
    fileprivate func findAnswer(req: Request, question:Question) throws -> EventLoopFuture<QuestionAnswer> {
        let id = question.question?.id?.uuidString ?? ""
        return Answer.query(on: req.db)
            .group(.or) { answers in
                answers.filter(\.$parentID == id.uppercased())
                answers.filter(\.$parentID == id.lowercased())
            }
            .all().flatMap { answers in
                return req.eventLoop.future(QuestionAnswer(question: question, answers: answers))
            }
    }
    
    fileprivate func surveyFor(req: Request, id: UUID) throws -> EventLoopFuture<Survey> {
        return Survey.query(on: req.db).filter(\.$id == id).with(\.$sections) { section in
            section.with(\.$questions) { question in
                question.with(\.$question)
                
                question.with(\.$questions) { question in
                    question.with(\.$question)
                    
                    question.with(\.$questions) { question in
                        question.with(\.$question)
                    }
                }
            }
        }.first().flatMapThrowing() {model in
            guard let foundModel = model else { throw Abort(.notFound, reason: "surveyID is required") }
            return foundModel
        }
    }
    
    fileprivate func answersForSurvey(req: Request, id: String) throws -> EventLoopFuture<[Answer]> {
        return Answer.query(on: req.db)
            .group(.or) { answers in
                answers.filter(\.$surveyID == id.uppercased())
                answers.filter(\.$surveyID == id.lowercased())
            }
            .all()
            .flatMap { firstAnswers in
            if firstAnswers.isEmpty {
                return Answer.query(on: req.db).filter(\.$surveyID == id.lowercased()).all()
            } else {
                return req.eventLoop.future(firstAnswers)
            }
        }
    }
    
//    fileprivate func buildQuestionAnswer(req: Request, mapBuilder:[String: [Answer]]) throws -> EventLoopFuture<[QuestionAnswer]> {
//        let keys = Array(mapBuilder.keys)
//        return keys.sequencedFlatMapEach(on: req.eventLoop) { questionIdKey in
//            return try! QuestionsController.findQuestion(req: req, id: questionIdKey).flatMap({ question in
//                return req.eventLoop.future(QuestionAnswer.init(question: question, answers: mapBuilder[questionIdKey] ?? []))
//            })
//        }
//    }
//    
//    fileprivate func buildMap(req:Request, answers: [Answer]) throws -> EventLoopFuture<[String: [Answer]]> {
//        return answers
//            .sequencedFlatMapEach(on: req.eventLoop) { answer -> EventLoopFuture<(String, [Answer])> in
//                let key = answer.questionID ?? "-"
//                return req.eventLoop.makeSucceededFuture((key, [answer]))
//            }
//            .map { pairs in
//                var result: [String: [Answer]] = [:]
//                for (key, value) in pairs {
//                    result[key] = (result[key] ?? []) + value
//                }
//                return result
//            }
//    }
    
    fileprivate func buildQuery(query:QueryBuilder<Survey>, req:Request, orgID:String) -> QueryBuilder<Survey> {
        let memberID:String?     = req.query["memberID"] //memberID
        let statusType:String? = req.query["status"]
        let keyType:String?    = req.query["key"]
        
        
        if let byUser = memberID {
            query.filter(\.$memberID == UUID(byUser)!)
        }
        
        if let type = statusType, type != "all" {
            query.filter(\.$status == type)
        }
        
        if let key = keyType {
            query.filter(\.$key == key)
        }
        query.with(\.$taker) { user in
            user.with(\.$teams)
        }.with(\.$attachments)
        
        
        query.filter(\.$orgID == orgID.lowercased())
        
        return query.with(\.$sections) { section in
            section.with(\.$questions) { question in
                question.with(\.$questions) { question in
                    question.with(\.$questions)
                }
            }
        }
    }
    
    func buildDashboardForSurvey(req: Request) throws -> EventLoopFuture<Dashboard> {
        guard let id = req.parameters.get(surveyID) else { throw Abort(HTTPResponseStatus.badRequest) }
        return try! SurveysController.findLatestScoreableSurvey(req: req, id: id).flatMap { survey in
            if let memberID = survey.memberID?.uuidString {
                return try! MembersController.find(req: req, id: memberID).flatMap { member in
                    return req.eventLoop.future(Dashboard(member: member, survey: [survey]))
                }
            } else {
                return req.eventLoop.future(error: Abort(.badRequest))
            }
        }
    }
    
    //MARK: - Update
    func update(req: Request) throws -> EventLoopFuture<Survey> {
        let surveyInput = try req.content.decode(SurveyUpdateInput.self)
        
        return try! self.find(req: req).flatMap { model in
            let updatedModel = surveyInput.returnUpdatedModel(survey: model)
            
            if updatedModel.isComplete() {
                
                updatedModel.endedAt = Date()
                
                return try! assessmentCompleteTimeline(req: req, survey: model, memberId: updatedModel.memberID).flatMap { _ in
                    
                    return try! TemplatesController.findTemplateByKey(req: req, key: model.key).flatMap { template in
                        
                        if template.scored {
                            
                            return ScoreCalculator.score(survey: updatedModel, req: req).flatMap { scoreModel in

                                let updatedModel = scoreModel.survey
                                let score = scoreModel.score
                                
                                //often time complier errors result in not handle try clause.
                                if let usrID = updatedModel.memberID {
                                    return try! self.updateFirstLastBitForSurvey(userID: usrID, req: req, survey: updatedModel).flatMap { survy in
                                        if let survey = survy {
                                            
                                            return survey.update(on: req.db).transform(to: survey).flatMap{ updatedSurvey in
                                                
                                                return try! self.updateMembersLastScoredAt(req: req, memberID: usrID, scoreModel: score).transform(to: updatedSurvey)
                                            }
                                        } else {
                                            return updatedModel.update(on: req.db).transform(to: updatedModel)
                                        }
                                    }
                                } else {
                                    return updatedModel.update(on: req.db).transform(to: updatedModel)
                                }
                            }
                            
                        } else {
                            return updatedModel.update(on: req.db).transform(to: updatedModel)
                        }
                    }
                    
                }
                
            } else {
                return updatedModel.update(on: req.db).transform(to: updatedModel)
            }
        }
    }
    
    fileprivate func updateMembersLastScoredAt(req:Request, memberID:UUID?, scoreModel:ScoreModel) throws -> EventLoopFuture<Void> {
        guard let id = memberID else {  throw NetworkError.error(type: .member) }
        return try MembersController.find(req: req, id: id.uuidString).flatMap { member in
            let lastAt = Date.sentTime(date: Date())
            member.lastAt = lastAt
            member.score = scoreModel.formattedScore
            return member.update(on: req.db).transform(to: member).flatMap { member in
                return try! self.updateHouseholdScoreIfNeeded(req: req, member: member, scoreModel: scoreModel, lastAt: lastAt)
            }
        }
    }
    
    fileprivate func updateHouseholdScoreIfNeeded(req:Request, member:Member, scoreModel:ScoreModel, lastAt:String) throws -> EventLoopFuture<Void> {
        return try MembersController.membersHousehold(req: req, id: member.id!.uuidString).flatMapEach(on: req.eventLoop) { household in
            household.householdScore = scoreModel.householdScore()
            household.lastVisit = lastAt
            return household.update(on: req.db)
        }
    }
    
    //MARK: - Create
    
    func create(req: Request) throws -> EventLoopFuture<Survey> {
        let input = try req.content.decode(SurveyCreateInput.self)
        let survey = input.model()
        return survey.save(on: req.db).map { survey }
    }
    
    //MARK: - Delete
    
    func delete(req: Request) throws -> EventLoopFuture<SuccessResposne> {
        return Survey.find(req.parameters.get("surveyID"), on: req.db)
            .unwrap(or: Abort(.notFound))
            .flatMap { $0.delete(on: req.db) }
            .transform(to: SuccessResposne(res: "complete"))
    }
    
    fileprivate func renderAbortError(message:String) throws -> Abort {
        throw Abort(.notFound, reason: message)
    }
    
    
    fileprivate func assessmentCompleteTimeline(req: Request,
                                                survey: Survey,
                                                memberId: UUID?) throws -> EventLoopFuture<Void> {
        guard let memUUID = memberId else { return req.eventLoop.future() }
        guard let userId = survey.taker?.id else { return req.eventLoop.future() }
        let item = TimeLineItemMessage.general(memberId: memUUID,
                                    title: "\(survey.name) as been completed",
                                    desc: "assessment \(survey.name) as been completed",
                                    status: "assessment_complete",
                                    visible: true,
                                    meta: .init(data: ["assessment_id" : survey.id?.uuidString ?? ""])).toTimelineItem()
        return try TimelineControllerController.create([item], creatorId: userId, req: req)
    }
}
