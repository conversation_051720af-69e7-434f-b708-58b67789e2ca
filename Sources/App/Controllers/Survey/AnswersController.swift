
import Foundation
import Vapor
import Fluent


struct AnswersInput: Content {
    var parentID:String?
    var answers:[Answer]?
}

struct AnswersController: RouteCollection {
    
    var answerID = "answerID"
    
    func boot(routes: RoutesBuilder) throws {
        let networks = routes.grouped("answers")
        networks.get(":\(answerID)", use: find)
//        networks.get(["import"], use: importData)
        networks.get(use: index)
        networks.post(use: create)
        networks.group(":\(answerID)") { todo in
            todo.delete(use: delete)
        }
    }
    
    //MARK: - Fetch
    func find(req: Request) throws -> EventLoopFuture<Service> {
        return Service.find(req.parameters.get(answerID), on: req.db).unwrap(or: Abort(.notFound))
    }
    
    func importData(req: Request) throws -> EventLoopFuture<HTTPStatus> {
        return Answer.query(on: req.db).all().flatMap { allAns in
            return try! updateAll(req: req, allAns: allAns).flatMap { _ in
                return req.eventLoop.future(.accepted)
            }
        }
    }
    
    fileprivate func updateAll(req: Request, allAns:[Answer]) throws -> EventLoopFuture<Void> {
        return allAns.sequencedFlatMapEach(on: req.eventLoop) { answer in
            return Survey.find(UUID(uuidString: answer.surveyID!)!, on: req.db).flatMap { survey in
                answer.key = survey?.key
                return answer.update(on: req.db)
            }
        }
    }
    
    func index(req: Request) throws -> EventLoopFuture<[Answer]> {
        let org:String?            = req.query["org"]
        let key:String?            = req.query["key"]
        let value:String?          = req.query["value"]
        guard let orgID = org else { throw NetworkError.error(type: .organization) }
        let query = Answer.query(on: req.db).filter(\.$orgID == orgID)
        if let key = key {
            query.filter(\.$key == key)
        }
        if let val = value {
            query.filter(\.$value == val)
        }
        
        return query.all()
//        return Answer.query(on: req.db).all()
    }
    
    func findQuestion(req: Request, questionID:String) throws -> EventLoopFuture<Question> {
        return Question.query(on: req.db).filter(\.$id == UUID(questionID)!).first().unwrap(or:  Abort(.notFound))
    }
    
    
    //MARK: - Create
    func create(req: Request) throws -> EventLoopFuture<[Answer]> {
        let input = try req.content.decode(AnswersInput.self)
        guard let allAnswers = input.answers else { throw Abort(.notFound, reason: "One answer is required.") }
        guard !allAnswers.isEmpty else { throw Abort(.notFound, reason: "One answer is required.") }
        return Answer.query(on: req.db).filter(\.$parentID == input.parentID).all().flatMap { preAnswers in
            return preAnswers.delete(on: req.db).flatMap { _ in
                return allAnswers.create(on: req.db).transform(to: allAnswers).flatMap({ answers in
                    let totalScore = answers.map({(Int($0.value) ?? 0)}).reduce(0, +)
//                    print("Total Score for \(input.parentID ?? "{No Parent ID}") Score:\(totalScore)\n")
                    if let id = input.parentID {
                        return try! self.updateScore(req: req, parentID: id, score: totalScore).flatMap { _ in
                            return req.eventLoop.future(answers)
                        }
                    } else {
                        return req.eventLoop.future(answers)
                    }
                })
            }
        }
    }
    
    
    //MARK: - Update
    fileprivate func updateScore(req: Request, parentID:String, score:Int?) throws -> EventLoopFuture<Question> {
        return try self.findQuestion(req: req, questionID: parentID).flatMap({ question in
            let data = question
            data.score = score
            return data.update(on: req.db).transform(to: data)
        })
    }
    
    
    //MARK: - Delete
    func delete(req: Request) throws -> EventLoopFuture<HTTPStatus> {
        return Service.find(req.parameters.get(answerID), on: req.db)
            .unwrap(or: Abort(.notFound))
            .flatMap { $0.delete(on: req.db) }
            .transform(to: .ok)
    }
}


