//
//  File.swift
//
//
//  Created by <PERSON> on 7/1/21.
//


import Foundation
import Vapor
import Fluent


struct SectionsController: RouteCollection {
    
    var sectionID = "sectionID"
    
    func boot(routes: RoutesBuilder) throws {
        let networks = routes.grouped("sections")
        networks.get(":\(sectionID)", use: find)
        networks.get(use: index)
        
        networks.put(":\(sectionID)", use: update)
        
        networks.delete(":\(sectionID)", use: delete)
        
        networks.post(use: createSection)
    }
    
    //MARK: - Query
    
    func find(req: Request) throws -> EventLoopFuture<Section> {
        guard let sectionID = req.parameters.get(sectionID),
            let id = UUID(sectionID) else {
                throw Abort(.notFound, reason: "survey_id is required")
        }
        return Section.query(on: req.db).with(\.$questions) { question in
            question.with(\.$questions)
        }.filter(\.$id == id).first().unwrap(or:  Abort(.notFound))
    }
    
    func sectionsForSurvey(req:Request) throws -> EventLoopFuture<[Section]> {
        guard let surveyID = req.parameters.get("surveyID"),
            let id = UUID(surveyID) else {
                throw Abort(.notFound, reason: "survey_id is required")
        }
        return Section.query(on: req.db).filter(\.$survey.$id == id).all()
    }
    
    func index(req: Request) throws -> EventLoopFuture<[Section]> {
        return Section.query(on: req.db).all()
    }
    
    func findSurvey(req: Request, surveyID:String) throws -> EventLoopFuture<Survey> {
        guard let id = UUID(surveyID) else { throw Abort(.notFound, reason: "survey_id is not found") }
        return Survey.query(on: req.db).with(\.$sections) { section in
            section.with(\.$questions) { question in
                question.with(\.$questions)
            }
        }.filter(\.$id == id).first().unwrap(or: Abort(.notFound))
    }
    
    
    //MARK: - Update
    
    func update(req: Request) throws -> EventLoopFuture<Section> {
        let sectionInput    = try req.content.decode(SectionInput.self)
        let foundSection = try self.find(req: req)
        return foundSection.flatMap() { model in
            return try! self.updateModel(req: req, input: sectionInput, model: model)
        }
    }
    
    fileprivate func updateModel(req:Request, input:SectionInput, model:Section) throws -> EventLoopFuture<Section> {
        let updatedModel  = input.returnUpdatedModel(section: model)
        return updatedModel.update(on: req.db).transform(to: updatedModel)
    }
    
    //MARK: - Create
    
    func createSection(req: Request) throws -> EventLoopFuture<Survey> {
        let section = try req.content.decode(SectionInput.self)
        guard let surveyID = section.surveyID else {throw Abort(.notFound, reason: "Survey ID is required")  }
        guard let surveyUUID = UUID(surveyID) else {throw Abort(.notFound, reason: "Survey ID is required")  }
        guard let title = section.title       else { throw Abort(.notFound, reason: "Title is required")  }
        guard let complete = section.complete else { throw Abort(.notFound, reason: "Complete is required")  }
        
        return try self.findSurvey(req: req, surveyID: surveyID).flatMap({ survey in
            return survey.$sections.create(Section(title: title, score:nil, complete: complete, surveyID: surveyUUID, type: section.type), on: req.db).flatMap({ data in
                return try! self.findSurvey(req: req, surveyID: surveyID)
            })
        })
    }
    
    //MARK: - Delete
    
    func delete(req: Request) throws -> EventLoopFuture<HTTPStatus> {
        return Section.find(req.parameters.get(sectionID), on: req.db)
            .unwrap(or: Abort(.notFound))
            .flatMap { $0.delete(on: req.db) }
            .transform(to: .ok)
    }
}



