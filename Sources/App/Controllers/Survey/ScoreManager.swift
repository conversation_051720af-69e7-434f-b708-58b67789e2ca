//
//  File.swift
//  
//
//  Created by <PERSON> on 2/8/23.
//

import Foundation
import Vapor
import Fluent

struct SectionScore:Content {
    var type:     String? = ""
    var title:    String? = ""
    var score:    Double  = 0.0
    var avg:      Double  = 0.0
    var sum:      Double  = 0.0
    var maxScore: Double  = 0.0
    var formattedScore:String? = "0"
    
    func isHousehold() -> Bool {
        guard let typ = self.type  else { return false }
        return typ.lowercased() == "household"
    }
}

struct ScoreModel:Content {
    var type:           String = ""
    var formattedScore: String = "0"
    var totalScore:     Double = 0.0
    var totalQuestions: Double = 0.0
    var totalPoints:    Double = 0.0
    
    var sections:[SectionScore] = []
    
    func householdScore() -> String? {
        return self.sections.filter({$0.isHousehold() == true}).first?.formattedScore
    }
}

struct SurveyScore:Content {
    var survey:Survey
    var score:ScoreModel
}

struct ScoreCalculator {
    static func score(survey:Survey, req:Request) -> EventLoopFuture<SurveyScore> {
        switch survey.key {
        case SurveyType.full_hrp.rawValue:
            return score(survey: survey, req: req,type: SurveyType.full_hrp.rawValue)
            
        case SurveyType.rapid_hrp.rawValue:
            return score(survey: survey, req: req, type: SurveyType.rapid_hrp.rawValue)
            
        case "empowered_hra":
            return score(survey: survey, req: req, type: "empowered_hra", sumOnly: true)
            
        default:
            return score(survey: survey, req: req, type: survey.key, sumOnly: true)
        }
    }
    
    static func score(survey:Survey, req:Request, type:String, sumOnly: Bool = false) -> EventLoopFuture<SurveyScore> {
        //run score
        var scoreModel     = ScoreModel()
        scoreModel.type    = type
//        var totalScore     = 0.0
//        var totalQuestions = 0.0
//        var totalPoints    = 0.0
        
        var sectionsToUpdate = [Section]()
        for section in survey.sections {
            scoreModel.totalQuestions += Double(section.questions.count)
            //this is only first level of questions since there the ones that score
            let sum = section.questions.compactMap({$0.score}).reduce(0, +)
            let sectionMaxScore = Double(section.questions.count * 2)
            let sectionAvg = Double(sum) / Double(sectionMaxScore) * 100
            let formattedScore = String(format: "%.0f", sectionAvg)
                        
            
            //Section Score Model
            let sectionScore = SectionScore(type: section.type, title: section.title, score: sectionAvg, avg: sectionAvg, sum: Double(sum), maxScore: sectionMaxScore, formattedScore: formattedScore)
            scoreModel.sections.append(sectionScore)
            
            //Update Sections
            section.score = sumOnly ? "\(Double(sum))" : formattedScore //use %.2f for deci
            section.complete = true
            sectionsToUpdate.append(section)
            
//            print("\nScore of section \(section.type?.capitalized ?? "{Missing Type}"): \(section.title) is \(sum)")
            scoreModel.totalScore += Double(sum)
        }
        
        scoreModel.totalPoints = scoreModel.totalQuestions * 2
        let percent:Float = Float((scoreModel.totalScore / scoreModel.totalPoints) * 100)
//        print("\(type) Total Questions for Survey: \(scoreModel.totalQuestions)")
//        print("\(type) Score for survey:\(scoreModel.totalScore)")
//        print("\(type) Total Points for survey: \(scoreModel.totalPoints)")
//        print("\(type) Avg score for survey: \(percent)")
        survey.score = sumOnly ? "\(Double(scoreModel.totalScore))" :  "\(percent)"
        scoreModel.formattedScore = "\(percent)"
        
        //Build Survey Score Model
        let surveyScore = SurveyScore(survey: survey, score: scoreModel)
        
        return sectionsToUpdate.map { $0.update(on: req.db) }.flatten(on: req.eventLoop).flatMap { data in
            return survey.update(on: req.db).transform(to: surveyScore)
        }
    }
    
    //MARK: - Helpers
    
    static func isRapidHRP(survey:Survey) -> Bool {
        return survey.key == SurveyType.rapid_hrp.rawValue
    }
    
    static func isHRP(survey:Survey) -> Bool {
        return survey.key == SurveyType.full_hrp.rawValue
    }
}

