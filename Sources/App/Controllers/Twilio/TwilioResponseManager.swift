//
//  TwilioResponseManager.swift
//
//
//  Created by <PERSON> on 10/15/24.
//

import Foundation
import Vapor
import Fluent


struct TwilioResponseManager {
    
    static func handleFailure(req: Request) throws -> EventLoopFuture<Response> {
        //Lets log the failures
        let messageSid = try req.content.get(String.self, at: "MessageSid")
        let messageStatus = try req.content.get(String.self, at: "MessageStatus")
        let errorCode = try req.content.get(String?.self, at: "ErrorCode")
        let errorMessage = try req.content.get(String?.self, at: "ErrorMessage")
        
        let cloudWatchLogger = CloudWatchLogger(req: req, logGroupName: .reporting)
        
        let logMessage = """
                   Twilio Message Failure:
                   SID: \(messageSid)
                   Status: \(messageStatus)
                   Error Code: \(errorCode ?? "N/A")
                   Error Message: \(errorMessage ?? "N/A")
                   """
        
        req.logger.warning("Twilio Failure: \(logMessage)")
        
        let msg = CloudWatchLogMessage.send(msg: .sms(msg: logMessage))
                
        return cloudWatchLogger.putLog(message: msg, on: req.eventLoop).flatMapThrowing({ _ in
            return Response(status: .ok, body: .init(string: ""))
        })
    }
    
    static func handle(req: Request) async throws -> Response {
        
        let body = try req.content.get(String.self, at: "Body").lowercased().trimmingCharacters(in: .whitespacesAndNewlines)
        let fromNumber = try req.content.get(String.self, at: "From")
        //todo will nee to from number to lookup member.
        
        // Log the message details
        print("Message received: \(body) from \(fromNumber)")
        var msg = ""
        if body == "c" {
            //confirm
            msg = "Thanks for confirming your appointment. You’ll receive a reminder 24 hours before."
            
        } else if body == "x" {
            //cancel
            msg = "Your appointment has been canceled. To reschedule, please contact your navigation team."
            
            
        } else if body == "h" {
            //needs help
            msg = "A member of your navigation team will reach out to assist you at \(fromNumber)."
            
        } else {
            // invalid response
            msg = "Invalid response unable to process \(body)"
        }
        
        
        // Create the response in TwiML format
        let twimlResponse = """
               <?xml version="1.0" encoding="UTF-8"?>
               <Response>
                   <Message><Body>\(msg)</Body></Message>
               </Response>
               """
        
        var headers = HTTPHeaders()
        headers.add(name: .contentType, value: "application/xml")
        
        return Response(status: .ok, headers: headers, body: .init(string: twimlResponse))
    }
}
