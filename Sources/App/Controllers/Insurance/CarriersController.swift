//
//  File.swift
//  
//
//  Created by <PERSON> on 1/6/24.
//

import Foundation
import Vapor
import Fluent

struct CreateInsuranceCarrier: Content {
    var name: String
    var contactNumber: String?
    var website: String?
    var address: String?
    var tags: [TagInput]?
    
    func carrier() -> Carrier {
        return Carrier(name: name, contactNumber: contactNumber, url: website, address: address)
    }
    
    func allTags() -> [Tag] {
        return self.tags?.compactMap({$0.tag()}) ?? []
    }
}


struct CarriersController: RouteCollection {
    
    func boot(routes: RoutesBuilder) throws {
        let users = routes.grouped("carriers")
        users.get(use: index)
        users.get(["all"], use: findAll)
        users.post(use: create)
    }
    
    func index(req: Request) throws -> EventLoopFuture<Page<Carrier>> {
        return self.buildQuery(query: Carrier.query(on: req.db), req: req).paginate(for: req)
    }
    
    func findAll(req: Request) throws -> EventLoopFuture<[Carrier]> {
        return Carrier.query(on: req.db).all()
    }
    
    static func findAll(req: Request, ids:[String]) throws -> EventLoopFuture<[Carrier]> {
        let allids = ids.compactMap({ UUID(uuidString: $0)})
        return Carrier.query(on: req.db).filter(\.$id ~~ allids).all()
    }
    
    //MARK: - Create
    func create(req: Request) throws -> EventLoopFuture<Carrier> {
        let input   = try req.content.decode(CreateInsuranceCarrier.self)
        let carrier = input.carrier()
        let tags = input.allTags()
        return carrier.create(on: req.db).transform(to: carrier).flatMap { carrier in
            return carrier.$tags.create(tags, on: req.db).transform(to: carrier)
        }
    }
    
    
    //MARK: - Query
    fileprivate func buildQuery(query:QueryBuilder<Carrier>, req:Request) -> QueryBuilder<Carrier> {
        let name:String?            = req.query["name"]
        
        if let nm = name, !nm.isEmpty  {
            query.filter(\.$name, .custom("ilike"), "%\(nm.lowercased())%")
        }
        
        return query
    }
}
