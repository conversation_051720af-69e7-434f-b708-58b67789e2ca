//
//  File.swift
//  
//
//  Created by <PERSON> on 1/6/24.
//

import Foundation
import Vapor
import Fluent


struct InsurancePlanInfo: Content {
    var name: String // medicare part a medicare part b
    var number: String
    var entitlementDate: String
    var endDate: String?
    var type: String //part a part b
    var groupNumber: String?
    var coverage: String? // family individual
    var contact: String? // family individual
    var issuer: String //carrier
    var enrolled: Bool
    
    func plan() -> InsurancePlan {
        return InsurancePlan(name: name.lowercased(),
                             number: number,
                             entitlementDate: entitlementDate,
                             endDate: endDate,
                             groupNumber: groupNumber,
                             type: type,
                             issuer: issuer,
                             contact: contact,
                             enrolled: enrolled,
                             coverage: coverage)
    }
}

struct InsurancePolicyInput: Content {
    var policyNumber: String
    var startDate: String
    var endDate: String?
    var planType: String
    var planName: String
    var userId: UUID
    var carrierId: UUID
    var planInfo:[InsurancePlanInfo]
    var phones: [PhoneInput]?
    var address: [AddressInput]?
    var attachments: [UploadInput]?
    
    func policy() -> InsurancePolicy {
        return InsurancePolicy(policyNumber: policyNumber,
                               startDate: startDate,
                               endDate: endDate,
                               userId: userId,
                               planType: planType,
                               planName: planName,
                               carrierId: carrierId)
    }
    
    func card(member: Member) -> InsuranceCard {
        let groupNumber = planInfo.first(where: {$0.groupNumber != nil})?.groupNumber ?? ""
        return InsuranceCard(policyholderName: member.fullName().lowercased(),
                             dateOfBirth: member.dob,
                             policyholderID: member.id?.uuidString.lowercased() ?? "",
                             effectiveDate: startDate,
                             expirationDate: endDate,
                             groupNumber: groupNumber)
    }
    
    func plans() -> [InsurancePlan] {
        return planInfo.map { $0.plan() }
    }
}

struct InsuranceCardInput: Content {
    var policyholderName: String
    var dateOfBirth: String
    var policyholderID: String
    var effectiveDate: String
    var expirationDate: String
    var attachments: [UploadInput]?
}


struct InsuranceController: RouteCollection {
    
    func boot(routes: RoutesBuilder) throws {
        let insurances = routes.grouped("insurances")
        insurances.get(":id", use: lookup)
        insurances.post(use: create)
        insurances.delete(":id", use: delete)
    }
    
    //MARK - Fetch
    func lookup(req:Request) throws -> EventLoopFuture<InsurancePolicy> {
        guard let id = req.parameters.get("id") else { throw NetworkError.error(type: .insurance) }
        guard let uuid = UUID(uuidString: id) else { throw NetworkError.error(type: .insurance) }
        return InsurancePolicy.query(on: req.db)
            .filter(\.$id == uuid)
            .with(\.$cards) { $0.with(\.$attachments) }
            .with(\.$plans)
            .with(\.$phones)
            .with(\.$address)
            .first()
            .unwrap(or: NetworkError.error(type: .insurance))
    }
    
    func findMemberPolicyBy(id:UUID, req:Request) throws -> EventLoopFuture<Page<InsurancePolicy>> {
        return InsurancePolicy.query(on: req.db)
            .filter(\.$userId == id)
            .with(\.$cards, { card in
                card.with(\.$attachments)
            })
            .with(\.$plans)
            .with(\.$phones)
            .with(\.$address)
            .sort(\.$createdAt, .ascending)
            .paginate(for: req)
    }
    
    func findPolicyBy(id:UUID, req:Request) throws -> EventLoopFuture<InsurancePolicy> {
        return InsurancePolicy.query(on: req.db)
            .filter(\.$id == id)
            .with(\.$cards, { card in
                card.with(\.$attachments)
            })
            .with(\.$plans)
            .with(\.$phones)
            .with(\.$address)
            .sort(\.$createdAt, .ascending)
            .first().flatMapThrowing { model in
                guard let foundModel = model else { throw NetworkError.error(type: .policy) }
                return foundModel
            }
    }
    
    func findCardBy(id:UUID, req:Request) throws -> EventLoopFuture<InsuranceCard> {
        return InsuranceCard.query(on: req.db)
            .filter(\.$id == id)
            .with(\.$attachments)
            .sort(\.$createdAt, .ascending)
            .first().flatMapThrowing { model in
                guard let foundModel = model else { throw NetworkError.error(type: .policy) }
                return foundModel
            }
    }

    
    //MARK: - Create
    func create(req: Request) throws -> EventLoopFuture<InsurancePolicy> {
        let input = try req.content.decode(InsurancePolicyInput.self)
        let policy = input.policy()
        return try! MembersController().findMemberBy(id: input.userId, req: req).flatMap { member in
            let card = input.card(member: member)
            let plans = input.plans()
            return policy.create(on: req.db).transform(to: policy).flatMap { insurancePolicy in
                
                return insurancePolicy.$plans.create(plans, on: req.db).flatMap { _ in
                    
                    return insurancePolicy.$cards.create([card], on: req.db).transform(to: card).flatMap { insuranceCard in
                        
                        return try! updatePhoneIfNeeded(req: req,
                                                        input: input,
                                                        policy: insurancePolicy).flatMap({ policy in
                            
                            return try! updateAddressIfNeeded(req: req,
                                                              input: input,
                                                              policy: policy).flatMap({ policy in
                              
                                return try! createAttachments(req: req,
                                                              card: insuranceCard,
                                                              inputs: input.attachments ?? []).flatMap { cards in
                                    
                                    return try! AuthController.userFromToken(req: req).flatMap { user in
                                        
                                        return try! createTimelineItemIfNeeded(req: req, member: member, policy: policy, creator: user).flatMap({ _ in
                                            
                                            return try! findPolicyBy(id: insurancePolicy.id!, req: req)
                                            
                                        })
                                        
                                    }
                                }
                            })
                        })
                    }
                }
            }
        }
    }
  
    fileprivate func updatePhoneIfNeeded(req:Request, input:InsurancePolicyInput, policy:InsurancePolicy) throws -> EventLoopFuture<InsurancePolicy> {
        if let phonesInput = input.phones, !phonesInput.isEmpty {
            let phones = phonesInput.compactMap({$0.phone()})
            return policy.$phones.create(phones, on: req.db).transform(to: policy)
        } else {
            return req.eventLoop.future(policy)
        }
    }
    
    fileprivate func updateAddressIfNeeded(req:Request, input:InsurancePolicyInput, policy:InsurancePolicy) throws -> EventLoopFuture<InsurancePolicy> {
        return try AddressService.updateMultipleAddressIfNeeded(req: req, input: input.address, policy: policy)
    }
    
    func createAttachments(req: Request,
                           card: InsuranceCard,
                           inputs: [UploadInput]) throws -> EventLoopFuture<Void> {
        if !inputs.isEmpty, let uuid = card.id {
            return try! findCardBy(id: uuid, req: req).flatMap { insCard in
                return inputs.sequencedFlatMapEach(on: req.eventLoop) { uploadInput in
                    return try! AttachmentsController().uploadImageToCloudinaryWith(req,input: uploadInput).flatMap { resposne in
                        let attach = Attachment(name: uploadInput.name,
                                                kind: uploadInput.type,
                                                type: "image",
                                                url: isProfile(type: uploadInput.type) ? resposne.profileURL() : resposne.secure_url,
                                                category: uploadInput.category,
                                                refID: resposne.public_id)
                        return insCard.$attachments.create(attach, on: req.db)
                    }
                }
            }
        } else {
            return req.eventLoop.future()
        }
    }
    
    //MARK: - Delete
    func delete(req: Request) throws -> EventLoopFuture<InsurancePolicy> {
        guard let id = req.parameters.get("id") else { throw NetworkError.error(type: .policy) }
        guard let networkID = UUID(id) else { throw NetworkError.error(type: .policy)}
        return InsurancePolicy.find(networkID, on: req.db)
            .unwrap(or: Abort(.notFound))
            .flatMap { model in
                return model.delete(on: req.db).map { model }
            }
    }
    
    fileprivate func createTimelineItemIfNeeded(req: Request, member: Member, policy: InsurancePolicy, creator: User?) throws -> EventLoopFuture<InsurancePolicy> {
        var items:[TimelineItem] = []
        
        let createItem = TimeLineItemMessage.generalMemberUpdate(member: member,
                                                           title: "New Insurance Added",
                                                                 desc: "\(policy.planName.capitalized) has been added. It is a \(policy.planType) plan with \(policy.policyNumber) as the member number.",
                                                           status: "insurance_added").toTimelineItem()
        items.append(createItem)
        if items.isEmpty {
            
            return req.eventLoop.future(policy)
            
        } else {
            return items.create(on: req.db).transform(to: items).flatMap { savedItems in
                return savedItems.sequencedFlatMapEach(on: req.eventLoop) { updatedTimeLineItem in
                    if let id = creator?.id {
                        updatedTimeLineItem.$creator.id = id
                    }
                    return updatedTimeLineItem.update(on: req.db).eventLoop.future(member)
                }.flatMap { _ in
                    return req.eventLoop.future(policy)
                }
            }
        }
        
    }
}

