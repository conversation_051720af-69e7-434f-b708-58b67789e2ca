//
//  File.swift
//  hmbl-core
//
//  Created by <PERSON> on 3/20/25.
//

import Foundation
import Fluent
import Vapor
// Define the Message struct
struct WebhookMessage: Content {
    let id: String
    let type: WebhookMessageType
    let kind: WebhookMessageKind
    let data: JSONB
}

// Enum for message types
enum WebhookMessageType: String, Codable {
    case create
    case update
//    case delete
}

// Enum for message kinds (models)
enum WebhookMessageKind: String, Codable {
    case user
    case member
    case socialplan
}


struct WebhookManager {
    
    static func processMessage(_ message: WebhookMessage, on req: Request) throws -> EventLoopFuture<HTTPStatus> {        
        switch message.kind {
        case .user:
            return processUsers(message, on: req).transform(to: .ok)
        case .member:
            return processMembers(message, on: req).transform(to: .ok)
        case .socialplan:
            return processUsers(message, on: req).transform(to: .ok)
        }
    }
    
    static func processMembers(_ message: WebhookMessage, on req: Request) -> EventLoopFuture<Void> {
        do {
            return try MembersController.find(req: req, id: message.id).flatMap { member in
                let json = message.data.value as? [String: Any] ?? [:]
                switch message.type {
                case .create:
                    return req.eventLoop.makeSucceededFuture(())
                case .update:
                    var input = MembersUpdateInput()
                    if let fName = json["firstName"] as? String {
                        input.firstName = fName
                    }
                    let user = input.returnUpdatedModel(user: member)
                    return user.update(on: req.db).transform(to: user).eventLoop.makeSucceededFuture(())
                }
            }
        } catch {
            return req.eventLoop.makeSucceededFuture(())
        }
    }
    
    static func processUsers(_ message: WebhookMessage, on req: Request) -> EventLoopFuture<Void> {
        do {
            return try UsersController.find(req: req, id: message.id).flatMap { user in
                switch message.type {
                case .create:
                    return req.eventLoop.makeSucceededFuture(())
                case .update:
                    return req.eventLoop.makeSucceededFuture(())
                }
            }
        } catch {
            return req.eventLoop.makeSucceededFuture(())
        }
    }
}
