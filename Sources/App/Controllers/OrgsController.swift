//
//  File.swift
//
//
//  Created by <PERSON> on 1/30/23.
//

import Foundation
import Vapor
import Fluent

struct CreateOrgInput: Content {
    var title: String
    var type: String
    var desc: String?
    var url: String?
}


struct OrgsController: RouteCollection {
    
    func boot(routes: RoutesBuilder) throws {
        let users = routes.grouped("orgs")
        users.post(use: create)
        users.post([":orgID", "attachments"], use: creatAttachment)
        users.get([":orgID", "constants"], use: constants)
        users.put(":orgID", use: update)
        users.get(use: index)
        users.get([":orgID", "members"], use: members)
        users.get(":orgID", use: lookup)
    }
    
    
    func constants(req: Request) throws -> EventLoopFuture<Organization> {
        guard let id = req.parameters.get("orgID") else { throw NetworkError.error(type: .organization) }
        return try OrgsController.find(req: req, id: id)
    }
    
    func lookup(req: Request) throws -> EventLoopFuture<Organization> {
        guard let id = req.parameters.get("orgID") else { throw NetworkError.error(type: .organization) }
        guard let networkID = UUID(id) else { throw NetworkError.error(type: .organization) }
        return Organization.query(on: req.db).filter(\.$id == networkID)
            .with(\.$users)
            .with(\.$attachments)
            .with(\.$address)
            .with(\.$phones)
            .with(\.$members)
            .with(\.$teams)
            .with(\.$networks)
            .with(\.$services)
            .with(\.$households)
            .with(\.$appointments)
            .with(\.$chats).first().flatMapThrowing { org in
                guard let foundModel = org else { throw NetworkError.error(type: .organization) }
                return foundModel
            }
    }
    
    static func createWith(req: Request, authUser:AuthUser, signup:UserSignup) throws -> EventLoopFuture<Void> {
        guard let authUser = try? authUser.asPublic() else {
            return req.eventLoop.future(error: Abort(.internalServerError))
        }
        return signup.user(auth: authUser.id.uuidString).create(on: req.db)
    }
    
    func members(req: Request) throws -> EventLoopFuture<Page<Member>> {
        guard let id = req.parameters.get("orgID") else { throw Abort(.notFound, reason: "Org ID is required") }
        return try! MembersController().orgMembers(req: req, orgID: id)
    }
    
    static func find(req: Request, id:String?) throws -> EventLoopFuture<Organization> {
        guard let orgID = id else { throw NetworkError.error(type: .organization)}
        guard let networkID = UUID(orgID) else { throw NetworkError.error(type: .organization)}
        return Organization.find(networkID, on: req.db).flatMapThrowing() {model in
            guard let foundModel = model else { throw NetworkError.error(type: .organization) }
            return foundModel
        }
    }
    
    func index(req: Request) throws -> EventLoopFuture<[Organization]> {
        return self.buildQuery(query: Organization.query(on: req.db), req: req).all()
    }

    func create(req: Request) throws -> EventLoopFuture<Organization> {
        let input = try req.content.decode(CreateOrgInput.self)
        let org = Organization(title: input.title, type: input.type, desc: input.desc, url: input.url)
        return org.save(on: req.db).transform(to: org)
    }
    
    func update(req: Request) throws -> EventLoopFuture<User> {
        guard let id = req.parameters.get("userID") else { throw Abort(.notFound, reason: "User ID is required") }
        let input = try req.content.decode(UserUpdateInput.self)
        return try UsersController.find(req: req, id: id).flatMap { usr in
            let user = input.returnUpdatedModel(user: usr)
            return user.update(on: req.db).transform(to: user)
        }
    }
    
    func creatAttachment(req: Request) throws -> EventLoopFuture<User> {
        guard let id = req.parameters.get("userID") else { throw Abort(.notFound, reason: "User ID is required") }
        let input = try req.content.decode(UploadInput.self)
        return try! UsersController.find(req: req, id: id).flatMap { usr in
            return try! AttachmentsController().uploadImageToCloudinary(req).flatMap { resposne in
                let attach = Attachment(name: input.name, 
                                        kind: input.type,
                                        type: "image",
                                        url: resposne.secure_url,
                                        category: input.category,
                                        refID: resposne.public_id)
                return usr.$attachments.create(attach, on: req.db).transform(to: usr).flatMap { updatedUser in
                    if input.isProfile() {
                        updatedUser.profile = resposne.secure_url
                        return updatedUser.update(on: req.db).transform(to: updatedUser)
                    } else {
                        return req.eventLoop.future(updatedUser)
                    }
                }
            }
        }
    }
    
//    //MARK: - Query
    fileprivate func buildQuery(query:QueryBuilder<Organization>, req:Request) -> QueryBuilder<Organization> {
        let title:String?            = req.query["title"]
        let type:String?            = req.query["type"]
//        let lastName:String?             = req.query["lastName"]
//        let email:String?                = req.query["email"]
//
//        if let nm = firstName  {
//            query.filter(\.$firstName, .custom("ilike"), "%\(nm.lowercased())%")
//        }
//
//        if let nm = lastName  {
//            query.filter(\.$lastName, .custom("ilike"), "%\(nm.lowercased())%")
//        }
//
        if let typ = type {
            query.filter(\.$type == typ)
        }
        
        if let tl = title {
            query.filter(\.$title == tl)
        }

//        return query.with(\.$attachments).with(\.$teams)
        return query.with(\.$users)
            .with(\.$attachments)
            .with(\.$address)
            .with(\.$phones)
            .with(\.$members)
            .with(\.$teams)
            .with(\.$networks)
            .with(\.$services)
            .with(\.$chats)
            .with(\.$households)
            .with(\.$appointments)
    }
}

