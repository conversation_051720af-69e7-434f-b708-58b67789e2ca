//
//  File.swift
//  
//
//  Created by <PERSON> on 2/4/23.
//

import Foundation
import Vapor
import Fluent


struct ServicesController: RouteCollection {
    
    func boot(routes: RoutesBuilder) throws {
        let users = routes.grouped("services")
        users.post(use: create)
        users.put(":netID", use: update)
        users.get(use: index)
        users.get(":netID", use: lookup)
        
        users.delete(":netID", use: delete)
    }
    
    
    //MARK: -  Fetches
    func lookup(req: Request) throws -> EventLoopFuture<Service> {
        guard let id = req.parameters.get("netID") else { throw NetworkError.error(type: .services) }
        guard let networkID = UUID(id) else { throw NetworkError.error(type: .services)}
        return Service.find(networkID, on: req.db).flatMapThrowing() {model in
            guard let foundModel = model else { throw NetworkError.error(type: .services) }
            return foundModel
        }
    }
    
    static func findAll(req: Request, ids:[String]) throws -> EventLoopFuture<[Service]> {
        let allids = ids.compactMap({ UUID(uuidString: $0)})
        return Service.query(on: req.db).filter(\.$id ~~ allids).all()
    }
        
    static func find(req: Request, id:String?) throws -> EventLoopFuture<Service> {
        guard let id = id else { throw NetworkError.error(type: .services)}
        guard let serviceID = UUID(id) else { throw NetworkError.error(type: .services)}
        return Service.find(serviceID, on: req.db).flatMapThrowing() {model in
            guard let foundModel = model else { throw NetworkError.error(type: .services) }
            return foundModel
        }
    }
    
    func index(req: Request) throws -> EventLoopFuture<[Service]> {
        return self.buildQuery(query: Service.query(on: req.db), req: req).all()
    }

    //MARK: - Create
    func create(req: Request) throws -> EventLoopFuture<Service> {
        let input = try req.content.decode(ServiceCreateInput.self)
        let service = input.serviceModel()
        return try! OrgsController.find(req: req, id: input.orgID).flatMap { org in
            return org.$services.create(service, on: req.db).transform(to: service).flatMap { service in                
                return try! self.updateRulesIfNeeded(req: req, input: input, service: service)
            }
        }
    }
    
    
    //MARK: - Update
    fileprivate func updateRulesIfNeeded(req:Request, input:ServiceCreateInput, service:Service) throws -> EventLoopFuture<Service> {
        if let rulesInputs = input.rules {
            let rules = rulesInputs.compactMap({$0.serviceRule()})
            return service.$rules.create(rules, on: req.db).transform(to: service)
        } else {
            return req.eventLoop.future(service)
        }
    }
    
    func update(req: Request) throws -> EventLoopFuture<Service> {
        guard let id = req.parameters.get("netID") else { throw NetworkError.error(type: .services) }
        let input = try req.content.decode(ServiceCreateInput.self)
        return try ServicesController.find(req: req, id: id).flatMap { service in
            let service = input.returnUpdatedModel(service: service)
            return service.update(on: req.db).transform(to: service).flatMap { service in
                try! self.updateRulesIfNeeded(req: req, input: input, service: service)
            }
        }
    }
    
    //MARK: - Delete
    func delete(req: Request) throws -> EventLoopFuture<HTTPStatus> {
        guard let id = req.parameters.get("netID") else { throw NetworkError.error(type: .services) }
        return try ServicesController.find(req: req, id: id).flatMap { service in
            return service.delete(on: req.db).transform(to: HTTPStatus.accepted)
        }
    }
    
    
//    //MARK: - Query
    fileprivate func buildQuery(query:QueryBuilder<Service>, req:Request) -> QueryBuilder<Service> {
        let name:String?            = req.query["name"]
        let type:String?            = req.query["type"]
//        let lastName:String?             = req.query["lastName"]
//        let email:String?                = req.query["email"]
//
//        if let nm = firstName  {
//            query.filter(\.$firstName, .custom("ilike"), "%\(nm.lowercased())%")
//        }
//
        if let nm = name  {
            query.filter(\.$name, .custom("ilike"), "%\(nm.lowercased())%")
        }
//
        if let typ = type {
            query.filter(\.$type == typ)
        }
        
//        if let tl = name {
//            query.filter(\.$name == tl)
//        }

//        return query.with(\.$attachments).with(\.$teams)
        return query
            .with(\.$rules)
            .with(\.$note)
            .with(\.$networks)
    }
}

