//
//  File.swift
//  
//
//  Created by <PERSON> on 7/22/24.
//

import Foundation
import SotoCore
import SotoCloudWatchLogs
import Vapor

enum CloudWatchLogMessage {
    enum LogAction: String {
        case delete, update, create
    }
    
    case reportExport(bucket: String, endpoint: String, status: String, name: String)
    case logins(user: String, status: String, member: Bool = false)
    case actions(type: LogAction, model: String, by: String, source: String, reason: String? = nil)
    case jobs(name: String, prams: String)
    case sms(msg: String)
    case security(msg: String)
    
    static func send(msg: CloudWatchLogMessage) -> String {
        switch msg {
        case .reportExport(let bucket, let endpoint, let status, let name):
            return "Export Sent | ReportName: \(name) | Bucket: \(bucket) | Bucket: \(endpoint) | Status: \(status.capitalized)"
            
        case .logins(let user, let status, let isMember):
            return "\(isMember ? "MemberID:" : "UserID:") \(user) | Status: \(status.capitalized)"
            
        case .actions(type: let type, let model, let by, let source, let reason):
            var msg = "\(type.rawValue.uppercased()): \(model) | PerformedBy: \(by) | \(source)"
            if let reason, !reason.isEmpty {
                msg.append("| \(reason)")
            }
            return msg
//        Action: Member Deleted - PerformedBy: UserID: 98765 - MemberID: 12345 - Status: Success - Reason: Account closure request
            
        case .jobs(let name, let prams):
//            Scheduled Job Fired - JobName: DailyReportGeneration - Status: Started - Params: {reportType: "daily"}
            return "Scheduled Job Fired | JobName: \(name) | Status: Sent | Params:\(prams)"
            
        case .sms(let msg):
            return msg

        case .security(let msg):
            return msg
        }
    }
}

enum CloudwatchLogKind: String {
    case reporting
    case logins
    case actions
    case jobs
    case security
}

struct CloudWatchLogger {
    let client: AWSClient
    let cloudWatchLogs: CloudWatchLogs
    let logGroupName: String
    let logStreamName: String

    init(req: Request, logGroupName: CloudwatchLogKind) {
        self.client = req.aws.client
        self.cloudWatchLogs = CloudWatchLogs(client: client)
        self.logGroupName = logGroupName.rawValue
        self.logStreamName = isProduction ? "production" : "staging"
    }

    func putLog(message: String, on eventLoop: EventLoop) -> EventLoopFuture<Void> {
        let timeStamp = Int(Date().timeIntervalSince1970 * 1000)
        let logEvent = CloudWatchLogs.InputLogEvent(message: message, timestamp: Int64(timeStamp))
        let request = CloudWatchLogs.PutLogEventsRequest(
            logEvents: [logEvent],
            logGroupName: logGroupName,
            logStreamName: logStreamName
        )
        return cloudWatchLogs.putLogEvents(request).map { _ in }
    }

    func shutdown() {
        do {
            try client.syncShutdown()
        } catch {
            // Handle shutdown error
        }
    }
}
