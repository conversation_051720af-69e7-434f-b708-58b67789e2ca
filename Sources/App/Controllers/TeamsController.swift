//
//  File.swift
//  
//
//  Created by <PERSON> on 2/2/23.
//

import Foundation
import Vapor
import Fluent

enum NetworkError: String {
    case team, user, household, member, attachment, organization, pet, networks, services, carepackage, carepackageItem, note, chat, device, appointments, task, policy, token, timeline, content, status, tag, insurance
    func error() -> Abort {
        return Abort(.notFound, reason: "\(self.rawValue) ID is required")
    }
    
    static func error(type:NetworkError) -> Abort {
        return Abort(.notFound, reason: "\(type.rawValue) ID is required")
    }
    
    static func error(type:HTTPResponseStatus, msg:String) -> Abort {
        return Abort(type, reason: msg)
    }
}


struct TeamsController: RouteCollection {
    
    func boot(routes: RoutesBuilder) throws {
        let users = routes.grouped("teams")
        users.get(use: index)
        users.get(":teamID", use: lookup)
        users.get([":teamID", "households"], use: householdsOptimized)
        users.get([":teamID", "households",  "page"], use: householdsOptimizedPage)
        users.get([":teamID", "minimalHouseholds"], use: householdWithTeam)
        
        users.post(use: create)
        users.post([":teamID", "attachments"], use: creatAttachment)
        users.post([":teamID", "leads"], use: createTeamLead)
        
        users.put(":teamID", use: update)
        
        users.delete(":teamID", use: delete)
    }
    
    //MARK: - Fetches
    func lookup(req: Request) throws -> EventLoopFuture<Team> {
        guard let id = req.parameters.get("teamID") else { throw NetworkError.error(type: .team) }
        guard let networkID = UUID(id) else { throw NetworkError.error(type: .team) }
        return Team.query(on: req.db).filter(\.$id == networkID)
            .with(\.$navigators, { nav in
                nav.with(\.$attachments)
            })
            .with(\.$address)
            .with(\.$households)
            .with(\.$households, { household in
                household.with(\.$headOfHouse)
                household.with(\.$address)
                household.with(\.$attachments)
                household.with(\.$members)
                household.with(\.$pets)
            })
            .with(\.$leads).first()
            .flatMapThrowing() {model in
            guard let foundModel = model else { throw NetworkError.error(type: .team) }
            return foundModel
        }
    }
    
    func find(req: Request, id:String) throws -> EventLoopFuture<Team> {
        return try TeamsController.find(req: req, id: id)
    }
    
    func query(req: Request, id: String) throws -> EventLoopFuture<Team> {
        guard let networkID = UUID(id) else { throw NetworkError.error(type: .team) }
        return Team.query(on: req.db).filter(\.$id == networkID)
            .with(\.$address)
            .first()
            .flatMapThrowing() {model in
            guard let foundModel = model else { throw NetworkError.error(type: .team) }
            return foundModel
        }
    }
    
    static func findAll(req: Request, ids:[String]) throws -> EventLoopFuture<[Team]> {
        let allids = ids.compactMap({ UUID(uuidString: $0)})
        return Team.query(on: req.db).filter(\.$id ~~ allids).all()
    }
    
    static func find(req: Request, id:String) throws -> EventLoopFuture<Team> {
        guard let networkID = UUID(id) else { throw NetworkError.error(type: .team) }
        return Team.find(networkID, on: req.db).flatMapThrowing() {model in
            guard let foundModel = model else { throw NetworkError.error(type: .team) }
            return foundModel
        }
    }
    
    static func queryTeamWithHouseholds(req: Request, id: String) throws -> EventLoopFuture<Team> {
        guard let networkID = UUID(id) else { throw NetworkError.error(type: .team) }
        return Team.query(on: req.db).filter(\.$id == networkID)
            .with(\.$households)
            .first()
            .flatMapThrowing() {model in
            guard let foundModel = model else { throw NetworkError.error(type: .team) }
            return foundModel
        }
    }
    
    func households(req: Request) throws -> EventLoopFuture<[Household]> {
        guard let id = req.parameters.get("teamID") else { throw NetworkError.error(type: .team) }
        guard let uuid = UUID(uuidString: id) else { throw NetworkError.error(type: .team) }
        return self.buildQuery(query: Team.query(on: req.db), req: req, org: nil).filter(\.$id == uuid).first().flatMapThrowing() {model in
            guard let foundModel = model else { throw NetworkError.error(type: .team) }
            return foundModel.households
        }
    }
    
    struct HouseholdSummary: Content {
        var id: UUID
        var title: String
        var imageUrl: String?
        var address: String?
        var lastVisit: String?
        var householdScore: String?
        var teams: String?
    }
    
    func householdWithTeam(req: Request) throws -> EventLoopFuture<[HouseholdSummary]> {
        guard let id = req.parameters.get("teamID"), let teamID = UUID(uuidString: id) else {
            throw Abort(.badRequest, reason: "Invalid or missing teamID")
        }
        
        return HouseholdTeams.query(on: req.db)
            .filter(\.$team.$id == teamID)
            .join(parent: \.$household)
            .with(\.$household) { household in
                household.with(\.$attachments)
                household.with(\.$address)
                household.with(\.$teams)
            }
            .all()
            .sequencedFlatMapEachCompact { pivot in
                let h = pivot.household
                let teamNames = pivot.household.teams.compactMap({$0.name}).joined(separator: ",")
                guard let id = h.id else { return req.eventLoop.makeSucceededFuture(nil) }
                
                let summary = HouseholdSummary(
                    id: id,
                    title: h.title,
                    imageUrl: h.attachments.last?.url ?? "",
                    address: h.address.first?.fullAddress() ?? "",
                    lastVisit: h.lastVisit ?? "",
                    householdScore: h.householdScore ?? "",
                    teams: teamNames
                )
                return req.eventLoop.makeSucceededFuture(summary)
            }
    }
    
    func householdsOptimized(req: Request) throws -> EventLoopFuture<[Household]> {
        guard let id = req.parameters.get("teamID"),
              let uuid = UUID(uuidString: id) else {
            throw NetworkError.error(type: .team)
        }
        return Team.query(on: req.db)
            .filter(\.$id == uuid)
            .with(\.$households, { household in
                household.with(\.$address)
                household.with(\.$attachments)
            })
            .first()
            .flatMapThrowing { team in
                guard let teamModel = team else { throw NetworkError.error(type: .team) }
                return teamModel.households
            }
    }
    
    func householdsOptimizedPage(req: Request) throws -> EventLoopFuture<Page<Household>> {
        guard let id = req.parameters.get("teamID"),
              let uuid = UUID(uuidString: id) else {
            throw NetworkError.error(type: .team)
        }
        
        let search:String? = req.query["search"]

        let query = Household.query(on: req.db)
            .join(HouseholdTeams.self, on: \Household.$id == \HouseholdTeams.$household.$id)
            .filter(HouseholdTeams.self, \.$team.$id == uuid)
            .with(\.$address)
            .with(\.$attachments)
        
        if let search {
            query.filter(\.$title, .custom("ilike"), "%\(search.lowercased())%")
        }
        
        return query.paginate(for: req)
    }

    
    func index(req: Request) throws -> EventLoopFuture<Page<Team>> {
        let org:String?            = req.query["org"]
        guard let orgID = org else { throw NetworkError.error(type: .organization) }
        return self.buildQuery(query: Team.query(on: req.db), req: req, org: orgID).paginate(for: req)
    }
    
    //MARK: - Create
    func create(req: Request) throws -> EventLoopFuture<Team> {
        let input = try req.content.decode(TeamCreateInput.self)
        return try! OrgsController.find(req: req, id: input.orgID).flatMap { org in
            let team = input.team()
            return org.$teams.create(team, on: req.db).transform(to: team).flatMap { createdTeam in
                return try! addAddressIfNeeded(req: req, team: createdTeam, input: input.address).flatMap { team in
                    return try! addUsersIfNeeded(req: req, team: team, input: input)
                }
            }
        }
    }
    
    static func createWith(req: Request, authUser:AuthUser, signup:UserSignup, org:Organization) throws -> EventLoopFuture<Void> {
        guard let authUser = try? authUser.asPublic() else {
            return req.eventLoop.future(error: Abort(.internalServerError))
        }
        let user = signup.user(auth: authUser.id.uuidString)
        return org.$users.create(user, on: req.db)
    }
    
    fileprivate func addUsersIfNeeded(req:Request, team:Team, input:TeamCreateInput) throws -> EventLoopFuture<Team> {
        return try self.attachNavigatorsToTeam(req: req, team: team, input: input).flatMap { team in
            return try! self.attachTeamLeadsToTeam(req: req, team: team, input: input)
        }
    }
    
    fileprivate func attachNavigatorsToTeam(req:Request, team:Team, input:TeamCreateInput) throws -> EventLoopFuture<Team> {
        if let navs = input.navigators{
            if navs.isEmpty {
                return team.$navigators.detachAll(on: req.db).transform(to: team)
            } else {
                return try! UsersController.findAll(req: req, ids: navs).flatMap { navigators in
                    return team.$navigators.detachAll(on: req.db).flatMap { _ in
                        return team.$navigators.attach(navigators, on: req.db).transform(to: team)
                    }
                }
            }
        } else {
            return req.eventLoop.future(team)
        }
    }
    
    fileprivate func attachTeamLeadsToTeam(req:Request, team:Team, input:TeamCreateInput) throws -> EventLoopFuture<Team> {
        if let leads = input.teamLeads {
            if leads.isEmpty {
                return team.$leads.detachAll(on: req.db).transform(to: team)
            }  else {
                return try! UsersController.findAll(req: req, ids: leads).flatMap { navigators in
                    return team.$leads.detachAll(on: req.db).flatMap { _ in
                        return team.$leads.attach(navigators, on: req.db).transform(to: team)
                    }
                }
            }
       } else {
            return req.eventLoop.future(team)
        }
    }
    
    fileprivate func updateAddressIfNeeded(req: Request,
                                           input: TeamCreateInput,
                                           team: Team) throws -> EventLoopFuture<Team> {
        return try AddressService.updateAddressIfNeeded(req: req, input: input.address, model: team).transform(to: team)
    }
    
    fileprivate func addAddressIfNeeded(req:Request, team:Team, input:AddressInput?) throws -> EventLoopFuture<Team> {
        return try AddressService.updateAddressIfNeeded(req: req, input: input, model: team).transform(to: team)
    }
   
    
    //MARK: - Update
    func update(req: Request) throws -> EventLoopFuture<Team> {
        guard let id = req.parameters.get("teamID") else { throw NetworkError.error(type: .team) }
        let input = try req.content.decode(TeamCreateInput.self)
        return try query(req: req, id: id).flatMap { foundTeam in
            let updatedTeam = input.returnUpdatedModel(team: foundTeam)
            return updatedTeam.update(on: req.db).transform(to: updatedTeam).flatMap { team in
                return try! self.addUsersIfNeeded(req: req, team: team, input: input).flatMap{ _ in
                    return try! updateAddressIfNeeded(req: req, input: input, team: updatedTeam)
                }
            }
        }
    }
    
    func createTeamLead(req: Request) throws -> EventLoopFuture<Team> {
        guard let id = req.parameters.get("teamID") else { throw NetworkError.error(type: .team) }
        let input = try req.content.decode(TeamCreateInput.self)
        return try self.find(req: req, id: id).flatMap { team in
            return try! self.addUsersIfNeeded(req: req, team: team, input: input)
        }
    }
    
    func creatAttachment(req: Request) throws -> EventLoopFuture<User> {
        guard let id = req.parameters.get("teamID") else { throw NetworkError.error(type: .team) }
        let input = try req.content.decode(UploadInput.self)
        return try! UsersController.find(req: req, id: id).flatMap { usr in
            return try! AttachmentsController().uploadImageToCloudinary(req).flatMap { resposne in
                let attach = Attachment(name: input.name, 
                                        kind: input.type,
                                        type: "image",
                                        url: resposne.secure_url,
                                        category: input.category,
                                        refID: resposne.public_id)
                return usr.$attachments.create(attach, on: req.db).transform(to: usr).flatMap { updatedUser in
                    if input.isProfile() {
                        updatedUser.profile = resposne.secure_url
                        return updatedUser.update(on: req.db).transform(to: updatedUser)
                    } else {
                        return req.eventLoop.future(updatedUser)
                    }
                }
            }
        }
    }
    
    //MARK: - Delete
    func delete(req: Request) throws -> EventLoopFuture<HTTPStatus> {
        guard let id = req.parameters.get("teamID") else { throw NetworkError.error(type: .team) }
        return try find(req: req, id: id).flatMap { team in
            return team.delete(on: req.db).transform(to: HTTPStatus.accepted)
        }
    }
    
//    //MARK: - Query
    fileprivate func buildQuery(query:QueryBuilder<Team>, req:Request, org:String?) -> QueryBuilder<Team> {
        let name:String?            = req.query["name"]
        let search:String?            = req.query["search"]
        
        if let nm = name  {
            query.filter(\.$name, .custom("ilike"), "%\(nm.lowercased())%")
        }
        
        if let search, !search.isEmpty  {
            query.filter(\.$name, .custom("ilike"), "%\(search.lowercased())%")
        }
        
        let query = query
            .with(\.$navigators, { nav in
                nav.with(\.$attachments)
            })
            .with(\.$address)
            .with(\.$households, { household in
                household.with(\.$headOfHouse)
                household.with(\.$address)
                household.with(\.$attachments)
                household.with(\.$members)
                household.with(\.$pets)
                household.with(\.$teams)
            })
            .with(\.$leads)
        
        if let orgId = org, !orgId.isEmpty {
            return query
                    .join(parent: \.$org)
                    .filter(Organization.self, \.$id == UUID(uuidString: orgId)!)
        } else {
            return query
        }
        
        
            
    }
}

