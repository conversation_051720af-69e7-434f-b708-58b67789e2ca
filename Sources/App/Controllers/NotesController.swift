//
//  File.swift
//  
//
//  Created by <PERSON> on 2/7/23.
//

import Foundation
import Vapor
import Fluent

struct NotesController: RouteCollection {
    
    func boot(routes: RoutesBuilder) throws {
        let users = routes.grouped("notes")
        
        users.get(use: index)
        users.get(":netID", use: lookup)
             
        users.post(use: create)
        users.put([":netID"], use: update)
        users.put([":netID", "service"], use: attachServices)
                
        users.delete(":netID", use: delete)
    }
    
    //MARK - Fetch
    func lookup(req: Request) throws -> EventLoopFuture<Note> {
        guard let id = req.parameters.get("netID") else { throw NetworkError.error(type: .note) }
        guard let networkID = UUID(id) else { throw NetworkError.error(type: .note)}
        return Note.query(on: req.db)
            .filter(\.$id == networkID)
            .with(\.$attachments)
            .with(\.$member)
//            .with(\.$creator)
            .with(\.$creator, { user in
                user.with(\.$attachments)
            })
            .with(\.$tags)
            .first()
            .flatMapThrowing() { model in
            guard let foundModel = model else { throw NetworkError.error(type: .note) }
            return foundModel
        }
    }        
        
    static func find(req: Request, id:String?) throws -> EventLoopFuture<Note> {
        guard let id = id else { throw NetworkError.error(type: .note)}
        guard let networkID = UUID(id) else { throw NetworkError.error(type: .note)}
        return Note.find(networkID, on: req.db).flatMapThrowing() {model in
            guard let foundModel = model else { throw NetworkError.error(type: .note) }
            return foundModel
        }
    }
    
    func index(req: Request) throws -> EventLoopFuture<[Note]> {
        return self.buildQuery(query: Note.query(on: req.db), req: req).all()
    }

    
    //MARK: - Create
    func create(req: Request) throws -> EventLoopFuture<Note> {
        let input   = try req.content.decode(NoteInput.self)
        let note    = input.note()
        let allTags = input.allTags()
        return try UsersController.find(req: req, id: input.creator).flatMap { user in
            return note.create(on: req.db).transform(to: note).flatMap { note in
                note.$creator.id = user.id
                return note.$tags.create(allTags, on: req.db).flatMap { _ in
                    
                    return try! createAttachments(req: req, note: note, inputs: input.attachments ?? []).flatMap { updatedNote in
                        
                        if let member = input.memberID {
                            return try! MembersController.find(req: req, id: member).flatMap { member in
                                note.$member.id = member.id
                                return note.update(on: req.db).transform(to: note).flatMap { note in
                                    return try! TimelineControllerController.create([
                                        timelineItemNewNote(member.id!, note.id!)
                                    ], creatorId: user.id!, req: req).transform(to: note)
                                }
                            }
                        } else {
                            return note.update(on: req.db).transform(to: note)
                        }
                    }
                }
            }
        }
    }
    
    
    func update(req: Request) throws -> EventLoopFuture<Note> {
        let input   = try req.content.decode(NoteUpdateInput.self)
        let allTags = input.allTags()
        return try AuthController.userFromToken(req: req).flatMap { user in
            return try! NotesController().lookup(req: req).flatMap({ foundNote in
                let note = input.updatedNote(foundNote)
                return note.tags.delete(on: req.db).flatMap { _ in
                    return note.$tags.create(allTags, on: req.db).flatMap { _ in
                        
                        return try! createAttachments(req: req, note: note, inputs: input.attachments ?? []).flatMap { updatedNote in
                            
                            if let member = input.memberID {
                                return try! MembersController.find(req: req, id: member).flatMap { member in
                                    note.$member.id = member.id
                                    return note.update(on: req.db).transform(to: note).flatMap { note in
                                        return try! TimelineControllerController.create([
                                            timelineItemNotePublished(member.id!, note.id!)
                                        ], creatorId: user.id!, req: req).transform(to: note)
                                    }
                                }
                            } else {
                                if let publish = input.publish, publish == true,
                                    let memberId = note.member?.id {
                                    return note.update(on: req.db).transform(to: note).flatMap { note in
                                        return try! TimelineControllerController.create([
                                            timelineItemNotePublished(memberId, note.id!)
                                        ], creatorId: user.id!, req: req).transform(to: note)
                                    }
                                } else {
                                    return note.update(on: req.db).transform(to: note)
                                }
                            }
                        }
                    }
                }
            })
        }
    }
    
    fileprivate func updatePhoneIfNeeded(req:Request, input:NetworkCreateInput, network:Network) throws -> EventLoopFuture<Network> {
        if let phoneInput = input.phone {
            let phone = phoneInput.phone()
            return network.$phones.create(phone, on: req.db).transform(to: network)
        } else {
            return req.eventLoop.future(network)
        }
    }
    
    func attachServices(req: Request) throws -> EventLoopFuture<Network> {
        guard let id = req.parameters.get("netID") else { throw NetworkError.error(type: .note) }
        let input = try req.content.decode(NetworkCreateInput.self)
        guard let allServices  = input.services else { throw NetworkError.error(type: .badRequest, msg: "Services are required.") }
        return try NetworksController.find(req: req, id: id).flatMap { network in
            return try! ServicesController.findAll(req: req, ids: allServices).flatMap { services in
                return network.$services.attach(services, on: req.db).transform(to: network)
            }
        }
    }
    
    func createAttachments(req: Request,
                           note: Note,
                           inputs: [UploadInput]) throws -> EventLoopFuture<Void> {
        if !inputs.isEmpty {
            return inputs.sequencedFlatMapEach(on: req.eventLoop) { uploadInput in
                return try! AttachmentsController().uploadImageToCloudinaryWith(req,input: uploadInput).flatMap { resposne in
                    let attach = Attachment(name: uploadInput.name,
                                            kind: uploadInput.type,
                                            type: "image",
                                            url: resposne.secure_url,
                                            category: uploadInput.category,
                                            refID: resposne.public_id)
                    return note.$attachments.create(attach, on: req.db)
                }
            }
        } else {
            return req.eventLoop.future()
        }
    }
    
    func createAttachment(req: Request, note: Note, input: UploadInput?) throws -> EventLoopFuture<Note> {
        if let input = input {
            return try! AttachmentsController().uploadImageToCloudinaryWith(req,input: input).flatMap { resposne in
                let attach = Attachment(name: input.name,
                                        kind: input.type,
                                        type: "image",
                                        url: isProfile(type: input.type) ? resposne.profileURL() : resposne.secure_url,
                                        category: input.category,
                                        refID: resposne.public_id)
                return note.$attachments.create(attach, on: req.db).transform(to: note)
            }
        } else {
            return req.eventLoop.future(note)
        }
    }
    
    //MARK: - Delete
    func delete(req: Request) throws -> EventLoopFuture<HTTPStatus> {
        guard let id = req.parameters.get("netID") else { throw NetworkError.error(type: .note) }
        return try NotesController.find(req: req, id: id).flatMap { note in
            return note.delete(on: req.db).transform(to: HTTPStatus.accepted)
        }
    }
    
    
//    //MARK: - Query
    fileprivate func buildQuery(query:QueryBuilder<Note>, req:Request) -> QueryBuilder<Note> {
//        let name:String?            = req.query["name"]
        let type:String?            = req.query["type"]
        let status:String?            = req.query["status"]
//        let lastName:String?             = req.query["lastName"]
//        let email:String?                = req.query["email"]
//
//        if let nm = firstName  {
//            query.filter(\.$firstName, .custom("ilike"), "%\(nm.lowercased())%")
//        }
//
//        if let nm = lastName  {
//            query.filter(\.$lastName, .custom("ilike"), "%\(nm.lowercased())%")
//        }
//
        if let typ = type {
            query.filter(\.$type == typ)
        }
        
        if let status = status {
            query.filter(\.$status == status)
        } else {
            query.filter(\.$status == "active") //default
        }
                
        return query
            .with(\.$creator, { user in
                user.with(\.$attachments)
            })
            .with(\.$member)
            .with(\.$attachments)
            .with(\.$tags)
    }
    
    fileprivate func timelineItemNewNote(_ memberId: UUID, _ noteId: UUID) -> TimelineItem {
        return TimeLineItemMessage.general(memberId: memberId,
                                    title: "new note created",
                                    desc: "A new note was created for member.",
                                    status: "new_note",
                                    visible: true,
                                    meta: .init(data: ["note_id" : noteId.uuidString])).toTimelineItem()
    }
    
    fileprivate func timelineItemNotePublished(_ memberId: UUID, _ noteId: UUID) -> TimelineItem {
        return TimeLineItemMessage.general(memberId: memberId,
                                    title: "note published",
                                    desc: "A new note was published for member.",
                                    status: "update_note",
                                    visible: true,
                                    meta: .init(data: ["note_id" : noteId.uuidString])).toTimelineItem()
    }
}

