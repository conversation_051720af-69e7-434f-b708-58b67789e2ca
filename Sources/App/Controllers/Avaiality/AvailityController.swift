//
//  AvailityController.swift
//  hmbl-core
//
//  Created by <PERSON> on 4/6/25.
//

import Foundation
import Vapor
import Fluent

struct AvailityTokenRequest: Content {
    let grant_type: String
    let client_id: String
    let client_secret: String
    let scope: String

    func asURLEncoded() -> String {
        return [
            "grant_type=\(grant_type)",
            "client_id=\(client_id)",
            "client_secret=\(client_secret)",
            "scope=\(scope)"
        ]
        .joined(separator: "&")
        .addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? ""
    }
}

struct AvailityTokenResponse: Content {
        var token_type: String // "Bearer",
        var access_token: String // "AAIgNGExNDNkZTQzMzJmMmI1M2NjZTY2M2VkMWZkMzA4MDQUCKvEDDKENPBMCR3Pe4WhgPd_CTfVez1oIXMfG2X-6PtqExUDUdq2WVCkUdJiUXx25m2g8s7vifmvfcMWWE8pabUPKA6r7bCaMJT2EzyP9aXtAGr6c0JGmtXYcGl11yk",
        var scope: String //"hipaa",
        var expires_in: Int // 300,
        var consented_on: Int //**********
    
}

struct AvailityCoverageInput: Content {
    var payerId: String?              // e.g., 8630682165182464
    var providerNpi: String?          // e.g., 1813897596108800
    var memberId: String?             // e.g., 4187668726415360
    var patientLastName: String?      // e.g., Zanieri
    var patientFirstName: String?     // e.g., Max
    var patientBirthDate: String?     // e.g., 2008-02-08

    func asURLEncoded() -> String {
        var components = URLComponents()
        components.queryItems = [
            URLQueryItem(name: "payerId", value: payerId),
            URLQueryItem(name: "providerNpi", value: providerNpi),
            URLQueryItem(name: "memberId", value: memberId),
            URLQueryItem(name: "patientLastName", value: patientLastName),
            URLQueryItem(name: "patientFirstName", value: patientFirstName),
            URLQueryItem(name: "patientBirthDate", value: patientBirthDate)
        ].compactMap { $0 } // Removes nil values
        
        return components.percentEncodedQuery ?? ""
    }
}

func safe<T>(_ req: Request, _ block: () throws -> EventLoopFuture<T>) -> EventLoopFuture<T> {
    do {
        return try block()
    } catch {
        return req.eventLoop.makeFailedFuture(error)
    }
}

struct AvailityController: RouteCollection {
    
    func boot(routes: any RoutesBuilder) throws {
        let api = routes.grouped("availity")
        api.post("token", use: token)
        api.get("payers", use: payers)
        api.post("coverage", use: coverage)
    }
    
    fileprivate func payers(req: Request)  throws -> EventLoopFuture<ClientResponse> {
        return try token(req: req).flatMap { response in
            let token = response.access_token
            return safe(req) {
                 return try fetchAvaility(req: req, token: token, api: "availity-payer-list")
            }
        }
    }
    
    fileprivate func coverage(req: Request)  throws -> EventLoopFuture<ClientResponse> {
        let input = try req.content.decode(AvailityCoverageInput.self)
        return try token(req: req).flatMap { response in
            let token = response.access_token
            return safe(req) {
                return try postAvaility(req: req, token: token, api: "coverages", body: input.asURLEncoded())
            }
        }
    }    
    
    fileprivate func token(req: Request) throws -> EventLoopFuture<AvailityTokenResponse>{
        let tokenRequest = AvailityTokenRequest(
              grant_type: "client_credentials",
              client_id: "4a143de4332f2b53cce663ed1fd30804",
              client_secret: "5cfa45c217a285b708251f89b74ddcb7",
              scope: "hipaa")
        return try postAvaility(req: req,
                                token: nil,
                                api: "token",
                                body: tokenRequest.asURLEncoded()).flatMap { response in
            return response.decode(AvailityTokenResponse.self, on: req.eventLoop)
        }
    }
    
    fileprivate func fetchAvaility(req: Request, token: String, api: String) throws -> EventLoopFuture<ClientResponse> {
        return req.client.get(.availity(path: api),
                              headers: .availityHeaders(token: token))
    }
    
    fileprivate func postAvaility(req: Request, token: String?, api: String, body: String) throws -> EventLoopFuture<ClientResponse> {
        return req.client.post(.availity(path: api),
                               headers: .availityHeaders(token: token)) { request in
            request.body = ByteBuffer(string: body)
        }
    }
}

extension URI {
    static func availity(path: String) -> URI {
        let baseurl = isProduction ? "https://api.availity.com/availity/development-partner/v1/\(path)" : "https://api.availity.com/availity/development-partner/v1/\(path)"
        return URI(string: baseurl)
    }
}
extension HTTPHeaders {
    
    static func availityHeaders(token: String?) -> HTTPHeaders {
        var headers = HTTPHeaders()
        headers.add(name: .contentType, value: "application/x-www-form-urlencoded")
        if let token {
            headers.bearerAuthorization = .init(token: token)
        }
        return headers
    }
}
