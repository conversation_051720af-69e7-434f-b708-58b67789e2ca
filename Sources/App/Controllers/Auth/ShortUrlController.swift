//
//  ShortUrlController.swift
//  
//
//  Created by <PERSON> on 6/29/25.
//

import Foundation
import Vapor
import Fluent

struct ShortUrlController: RouteCollection {
    func boot(routes: RoutesBuilder) throws {
        let shortUrls = routes.grouped("s")
        shortUrls.get(":code", use: redirectShortUrl)
        
        // Optional: Analytics endpoint for tracking
        let api = routes.grouped("api", "short-urls")
        api.get(":code", "stats", use: getShortUrlStats)
    }
    
    // MARK: - Route Handlers
    
    /// Redirect short URL to original destination
    func redirectShortUrl(req: Request) async throws -> Response {
        guard let shortCode = req.parameters.get("code") else {
            throw Abort(.badRequest, reason: "Missing short code")
        }
        
        // Find the short URL record
        guard let shortUrl = try await ShortUrl.query(on: req.db)
            .filter(\.$shortCode == shortCode)
            .first() else {
            throw Abort(.notFound, reason: "Short URL not found")
        }
        
        // Check if the short URL is still valid
        if !shortUrl.isValid() {
            throw Abort(.gone, reason: "This reset link has expired. Please request a new password reset.")
        }
        
        // Record the click for analytics
        try await shortUrl.recordClick(on: req.db)
        
        // Log the access for security monitoring
        await logShortUrlAccess(req: req, shortUrl: shortUrl)
        
        // Redirect to the original URL
        return req.redirect(to: shortUrl.originalUrl, redirectType: .temporary)
    }
    
    /// Get analytics for a short URL (optional feature)
    func getShortUrlStats(req: Request) async throws -> ShortUrlStats {
        guard let shortCode = req.parameters.get("code") else {
            throw Abort(.badRequest, reason: "Missing short code")
        }
        
        guard let shortUrl = try await ShortUrl.query(on: req.db)
            .filter(\.$shortCode == shortCode)
            .first() else {
            throw Abort(.notFound, reason: "Short URL not found")
        }
        
        return ShortUrlStats(
            shortCode: shortUrl.shortCode,
            originalUrl: shortUrl.originalUrl,
            clickCount: shortUrl.clickCount,
            isValid: shortUrl.isValid(),
            createdAt: shortUrl.createdAt,
            expiresAt: shortUrl.expiresAt
        )
    }
    
    // MARK: - Helper Methods
    
    private func logShortUrlAccess(req: Request, shortUrl: ShortUrl) async {
        let clientIP = req.remoteAddress?.hostname ?? "unknown"
        let userAgent = req.headers.first(name: .userAgent) ?? "unknown"
        
        let cloudwatch = CloudWatchLogger(req: req, logGroupName: .actions)
        let logMessage = CloudWatchLogMessage.send(msg: .security(msg: "Short URL accessed: \(shortUrl.shortCode), IP: \(clientIP), User-Agent: \(userAgent)"))
        
        do {
            _ = try await cloudwatch.putLog(message: logMessage, on: req.eventLoop).get()
        } catch {
            req.logger.error("Failed to log short URL access: \(error)")
        }
    }
}

// MARK: - Response Models

struct ShortUrlStats: Content {
    let shortCode: String
    let originalUrl: String
    let clickCount: Int
    let isValid: Bool
    let createdAt: Date?
    let expiresAt: Date?
}

struct CreateShortUrlRequest: Content {
    let originalUrl: String
    let expirationMinutes: Int?
}

struct CreateShortUrlResponse: Content {
    let shortCode: String
    let shortUrl: String
    let originalUrl: String
    let expiresAt: Date?
}
