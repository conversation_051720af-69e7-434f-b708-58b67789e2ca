//
//  File.swift
//  
//
//  Created by <PERSON> on 1/30/23.
//

import Foundation
import Vapor
import Fluent

struct RemoveResponse: Content {
    var result: String
}
    
struct UploadResponse: Content {
    var secure_url:String
    var height:Int
    var width:Int
    var format:String
    var asset_id:String
    var public_id: String
    
    func profileURL() -> String {
        let transform = "https://res.cloudinary.com/cg1-solutions/image/upload/w_1000,c_fill,ar_1:1,g_auto,r_max/"
        var url = secure_url
        url = url.replacingOccurrences(of: "https://res.cloudinary.com/cg1-solutions/image/upload/", with: transform)        
        return url
    }
    
    func isPDF() -> Bool {
        return format.lowercased() == "pdf"
    }
}

struct RemoveInput: Content {
    var name:String //name or id of file
    var type:String //notes, profile
}

struct BatchDeleteInput: Content {
    var ids: [String]
}

struct UploadInput: Content {
    var base64: String
    var type:String //notes, profile
    var name:String //name or id of file
    var category: String? //name or id of file
    var isConsent: Bool? = false
//    var assetType: String? //image, file video
    
    func isProfile() -> Bool {
        return self.type.lowercased() == "profile"
    }        
}
struct AttachmentsController: RouteCollection {
    
    func boot(routes: RoutesBuilder) throws {
        let networks = routes.grouped("attachments")
        networks.get(use: index)
        networks.post(use: create)
        networks.post("upload", use: uploadImageToCloudinary)
        networks.delete(":attachID", use: removeImageToCloudinary)
        networks.post("batchDelete", use: batchDelete)
    }
    
    func uploadImageToCloudinaryWith(_ req: Request, input: UploadInput) throws -> EventLoopFuture <UploadResponse> {
        let encoderData = prepareCloudinaryData(input: input)
        
        return req.client.post(CloudinaryConfiguration.uploadUri) { req in
            req.headers = ["Content-Type":"x-www-form-urlencoded"]
            try req.content.encode(encoderData)
        }.map { response in
            print(response)
            let res = try! response.content.decode(UploadResponse.self)
            return res
        }
    }
    
    func uploadImageToCloudinary(_ req: Request) throws -> EventLoopFuture <UploadResponse> {
        let input = try req.content.decode(UploadInput.self)
        let encoderData = prepareCloudinaryData(input: input)
        
        return req.client.post(CloudinaryConfiguration.uploadUri) { req in
            req.headers = ["Content-Type":"x-www-form-urlencoded"]
            try req.content.encode(encoderData)
        }.map { response in
            print(response)
            let res = try! response.content.decode(UploadResponse.self)
            return res
        }
    }
    
    func batchDelete(_ req: Request) throws -> EventLoopFuture<RemoveResponse> {
        let input = try req.content.decode(BatchDeleteInput.self)
        return try! findAll(req: req, ids: input.ids).flatMap { allAttachments in
            return allAttachments.sequencedFlatMapEach(on: req.eventLoop) { attachment in
                return try! removeFromCloudinary(req: req, attachment: attachment)
            }.transform(to: RemoveResponse(result: "ok"))
        }
    }
    
    fileprivate func removeFromCloudinary(req: Request, attachment: Attachment) throws -> EventLoopFuture<RemoveResponse> {
        let encoder = CloudinaryConfiguration.destroySigned(refId: attachment.refID ?? "")
        return req.client.post(CloudinaryConfiguration.destroyUri) { req in
            req.headers = ["Content-Type":"x-www-form-urlencoded"]
            try req.content.encode(encoder)
        }.flatMap { response in
            print(response)
            if let res = try? response.content.decode(RemoveResponse.self),
                res.result.lowercased() == "ok" {
                return attachment.delete(on: req.db).transform(to: attachment).flatMap({ _ in
                    return req.eventLoop.future(res)
                })
            } else {
                return req.eventLoop.future(RemoveResponse(result: "bad request missing public id"))
            }
        }
    }
    
    
    func removeImageToCloudinary(_ req: Request) throws -> EventLoopFuture<RemoveResponse> {
        return try find(req: req).flatMap { attachment in
            return try! removeFromCloudinary(req: req, attachment: attachment)
        }
    }
    
    fileprivate func prepareCloudinaryData(input:UploadInput) -> CloudinaryUploadInput {
        return CloudinaryConfiguration.input(name: input.name, type: input.type, base64: input.base64)
    }
    
    func find(req: Request) throws -> EventLoopFuture<Attachment> {
        return Attachment.find(req.parameters.get("attachID"), on: req.db).unwrap(or: Abort(.notFound))
    }
    
    func findAll(req: Request, ids:[String]) throws -> EventLoopFuture<[Attachment]> {
        let allids = ids.compactMap({ UUID(uuidString: $0)})
        return Attachment.query(on: req.db).filter(\.$id ~~ allids).all()
    }
    
    func index(req: Request) throws -> EventLoopFuture<[Attachment]> {
        return Attachment.query(on: req.db).all()
    }
    
    func create(req: Request) throws -> EventLoopFuture<Attachment> {
        let attachment = try req.content.decode(Attachment.self)
        return attachment.create(on: req.db).transform(to: attachment)
    }
    
    static func create(req: Request, attachment: Attachment) throws -> EventLoopFuture<Attachment> {
        return attachment.create(on: req.db).transform(to: attachment)
    }
    
    func delete(req: Request) throws -> EventLoopFuture<HTTPStatus> {
        return Attachment.find(req.parameters.get("attachID"), on: req.db)
            .unwrap(or: Abort(.notFound))
            .flatMap { $0.delete(on: req.db) }
            .transform(to: .ok)
    }
}



