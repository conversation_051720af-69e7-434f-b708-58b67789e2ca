//
//  File.swift
//  hmbl-core
//
//  Created by <PERSON> on 4/7/25.
//

import Foundation
import Vapor
import Fluent

struct NPIQuery: Content {
    var number: String?
    var firstName: String?
    var lastName: String?
    var enumerationType: String? // "NPI-1" or "NPI-2"
    var taxonomyDescription: String?
    var postalCode: String?

    func asQueryParams() -> String {
        var params: [URLQueryItem] = []

        if let number = number {
            params.append(URLQueryItem(name: "number", value: number))
        }
        if let firstName = firstName {
            params.append(URLQueryItem(name: "first_name", value: firstName))
        }
        if let lastName = lastName {
            params.append(URLQueryItem(name: "last_name", value: lastName))
        }
        if let enumerationType = enumerationType {
            params.append(URLQueryItem(name: "enumeration_type", value: enumerationType))
        }
        if let taxonomyDescription = taxonomyDescription {
            params.append(URLQueryItem(name: "taxonomy_description", value: taxonomyDescription))
        }
        if let postalCode = postalCode {
            params.append(URLQueryItem(name: "postal_code", value: postalCode))
        }

        var components = URLComponents()
        components.queryItems = params
        return components.percentEncodedQuery ?? ""
    }
}

struct NPIValidationResult: Content {
    let isValid: Bool
    let status: String
    let message: String
}

struct NPIResponse: Content {
    let results: [NPIResult]?
    
    struct NPIResult: Content {
        let created_epoch: String
        let enumeration_type: String
        let last_updated_epoch: String
        let number: String
        let basic: BasicInfo?
        let addresses: [Address]?
        let taxonomies: [Taxonomy]?
        let practiceLocations: [Address]?
        let identifiers: [IdentifierType]?
    }

    struct BasicInfo: Content {
        var first_name: String? // "PEGAH",
        var last_name: String? // "MASHAYEKHI",
        var middle_name: String? // "MARYAM",
        var credential: String? // "D.O.",
        var sole_proprietor: String? // "YES",
        var sex: String? // "F",
        var enumeration_date: String? // "2012-05-15",
        var last_updated: String? // "2025-03-03",
        var certification_date: String? // "2025-03-02",
        var status: String? // "A",
        var name_prefix: String? // "Ms."
        
        //ORG
        var organization_name: String? // "CURAVITA LLC",
        var organizational_subpart: String? // "NO",
        var authorized_official_first_name: String? // "BRIAN",
        var authorized_official_last_name: String? // "ANDERSON",
        var authorized_official_telephone_number: String? // "3125130170",
        var authorized_official_title_or_position: String? // "Co-Owner"
    }

    struct Address: Content {
        let country_code: String?
        let country_name: String?
        let address_purpose: String?
        let address_type: String?
        let address_1: String?
        let city: String?
        let state: String?
        let postal_code: String?
        let telephone_number: String? //"************",
        let fax_number: String? // "************"
    }
    
    struct Taxonomy: Content {
        let code: String? // "207RS0012X",
        let taxonomy_group: String? // "",
        let desc: String? // "Internal Medicine, Sleep Medicine",
        let state: String? // "CA",
        let license: String? // "20A12987",
        let primary: Bool? // true
    }
    
    struct IdentifierType: Content {
        let code: String? // "01",
        let desc: String? // "Other (non-Medicare)",
        let issuer: String? // "Medical License",
        let identifier: String? // "20A12987",
        let state: String? // "CA"
    }
}
struct NPIController: RouteCollection {
    
    func boot(routes: any RoutesBuilder) throws {
        let api = routes.grouped("npi")
        api.get("fetch", use: fetch)
        api.post("validate", use: validate)
    }
    
    fileprivate func fetch(req: Request)  throws -> EventLoopFuture<NPIResponse> {
        let query = try req.query.decode(NPIQuery.self)
        return try npiLookup(req: req, query: query)
    }
    
    fileprivate func npiLookup(req: Request, query: NPIQuery) throws -> EventLoopFuture<NPIResponse> {
        let baseURL = "https://npiregistry.cms.hhs.gov/api/?version=2.1"
        let fullURL = baseURL + "&" + query.asQueryParams()
        let url = URI(string: fullURL)
        return safe(req) {
            return req.client.get(url)
                .flatMap { response in
                return try! req.eventLoop.future(response.content.decode(NPIResponse.self))
            }
        }
    }
 
    fileprivate func validate(req: Request)  throws -> EventLoopFuture<NPIValidationResult> {
        let input = try req.content.decode(NPIQuery.self)
        return try npiLookup(req: req, query: input).flatMap { response in
            let notFound = NPIValidationResult(isValid: false, status: "Not Found", message: "No provider found with this NPI.")
            
            guard let result = response.results?.first else {
                return req.eventLoop.future(notFound)
            }
            
            let isActive = result.basic?.status?.lowercased() == "a"
            
            if isActive, let basic = result.basic {
                var name = ""
                if let org = basic.organization_name, !org.isEmpty {
                    name = org
                } else {
                    name = "\(basic.first_name ?? "") \(basic.last_name ?? "")"
                }
                
                return req.eventLoop.future(.init(isValid: true, status: "Active", message: "NPI is registered to \(name)"))
                
            } else {
                return req.eventLoop.future(.init(isValid: false, status: "Incomplete", message: "NPI found but missing basic provider info."))
            }
        }
    }
}
