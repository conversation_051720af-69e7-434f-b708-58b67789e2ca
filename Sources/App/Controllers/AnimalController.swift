//
//  File.swift
//  
//
//  Created by <PERSON> on 5/11/23.
//
import Foundation
import Vapor
import Fluent

struct AnimalController: RouteCollection {
    
    func boot(routes: RoutesBuilder) throws {
        let users = routes.grouped("pets")
        users.get([":userID"],use: findPet)
        users.post([":userID","attachment"],use: createPetAttachment)
        users.post(use: createPet)
        users.put([":userID"],use: updatePet)
        users.delete([":userID"],use: removePet)
    }
    
    func findPet(req: Request) throws -> EventLoopFuture<Animal> {
        guard let id = req.parameters.get("userID") else { throw NetworkError.error(type: .pet) }
        return try! self.findPet(req: req, id: id)
    }
    
    func findPet(req: Request, id:String) throws -> EventLoopFuture<Animal> {
        guard let networkID = UUID(id) else { throw NetworkError.error(type: .pet)}
        return Animal.query(on: req.db).filter(\.$id == networkID).with(\.$attachments).first().flatMapThrowing() {model in
            guard let foundModel = model else { throw NetworkError.error(type: .pet) }
            return foundModel
        }
    }
    
    func createPet(req: Request) throws -> EventLoopFuture<Animal> {
        let input = try req.content.decode(AnimalInput.self)
        guard let orgID = input.orgID else { throw NetworkError.error(type: .member) }
        let pet = input.pet()
        return try! OrgsController.find(req: req, id: orgID).flatMap { org in
            return pet.create(on: req.db).transform(to: pet)
        }
    }
    
    func createPetAttachment(req: Request) throws -> EventLoopFuture<Animal> {
        let input = try req.content.decode(UploadInput.self)
        return try! findPet(req: req).flatMap { pet in
            return try! AttachmentsController().uploadImageToCloudinary(req).flatMap { resposne in
                let attach = Attachment(name: input.name, 
                                        kind: input.type,
                                        type: "image",
                                        url: isProfile(type: input.type) ? resposne.profileURL() : resposne.secure_url,
                                        category: input.category,
                                        refID: resposne.public_id)
                return pet.$attachments.create(attach, on: req.db).transform(to: pet)
            }
        }
    }
    
    func updatePet(req: Request) throws -> EventLoopFuture<Animal> {
        guard let id = req.parameters.get("userID") else { throw NetworkError.error(type: .pet) }
        let input = try req.content.decode(AnimalAssignInput.self)
        return try! self.findPet(req: req, id: id).flatMap { pet in
            let animal = input.returnUpdatedModel(pet:pet)
            return animal.update(on: req.db).transform(to: animal).flatMap { updatedAnimal in
                if let householdID = input.household {
                    return try! HouseholdsController.find(req: req, id: householdID).flatMap { household in
                        pet.$household.id = household.id
                        return pet.update(on: req.db).transform(to: pet)
                    }
                } else {
                    return req.eventLoop.future(updatedAnimal)
                }
            }
        }
    }
    
    func removePet(req: Request) throws -> EventLoopFuture<Animal> {
        return try self.findPet(req: req).flatMap { pet in
            return pet.delete(on: req.db).transform(to: pet)
        }
    }
}
