//
//  File.swift
//  
//
//  Created by <PERSON> on 3/30/24.
//
import Foundation
import Fluent
import Vapor

struct MemberChatsController: RouteCollection {
    
    func boot(routes: RoutesBuilder) throws {
        let chats = routes.grouped("memberChats")
        chats.get(use: index)
        chats.post(use: create)
//        networks.get([":chatID", "participants"], use: participants)
//        networks.put([":chatID", "members"], use: add)
//        networks.post([":chatID", "message"], use: addMessage)
//        networks.post([":chatID", "notification"], use: addPush)
//        networks.delete(":chatID", use: delete)
    }
    
    func index(req: Request) throws -> EventLoopFuture<Page<MemberChat>> {
        return MemberChat.query(on: req.db)
            .with(\.$creator)
            .with(\.$latestMessageSender)
            .with(\.$participants)
            .sort(\.$updatedAt, .descending)
            .paginate(for: req)
    }
    
    static func find(req: Request, chatID: String) throws -> EventLoopFuture<MemberChat> {
        return MemberChat.query(on: req.db).filter(\.$id == UUID(uuidString: chatID)!).with(\.$participants).first().unwrap(or:  Abort(.notFound))
    }
    
    static func chatsForMember(creatorID:String, req:Request) throws -> EventLoopFuture<Page<MemberChat>> {
        guard let uuid = UUID(uuidString:creatorID) else { throw NetworkError.error(type: .member) }
        let query = ChatMemberChats.query(on: req.db)
        return query.join(parent: \.$chatMember)
            .filter(ChatMember.self, \.$personId == uuid).all().flatMap { chats in
                let ids = chats.compactMap({$0.$chat.id})
//                print(ids)
                return MemberChat.query(on: req.db)
                    .filter(\.$id ~~ ids)
                    .with(\.$creator)
                    .with(\.$latestMessageSender)
                    .with(\.$participants)
                    .sort(\.$updatedAt, .descending)
                    .paginate(for: req)
            }
    }
    
    
    func find(req: Request, navigatorId: String) throws -> EventLoopFuture<User> {
        return try! UsersController.find(req: req, id: navigatorId)
    }
    
    func find(req: Request, memberId: String) throws -> EventLoopFuture<Member> {
        return try! MembersController.find(req: req, id: memberId)
    }
    
    fileprivate func clean(ids:[String]) -> [String] {
        let uniqueIds:Set<String> = Set(ids)
        return Array(uniqueIds)
    }
    
    func mapNavigatorParticipants(req: Request, ids:[String]) throws -> EventLoopFuture<[ChatMember]> {
        return try! UsersController.findAll(req: req, ids: ids).flatMap{ allPartisipants in
            return allPartisipants.sequencedFlatMapEach(on: req.eventLoop) { navigator in
                return req.eventLoop.future(ChatMember.from(navigator: navigator))
            }
       }
    }
    
    func mapMembersParticipants(req: Request, ids:[String]) throws -> EventLoopFuture<[ChatMember]> {
        return try! MembersController.findAll(req: req, ids: ids).flatMap{ allPartisipants in
            return allPartisipants.sequencedFlatMapEach(on: req.eventLoop) { member in
                return req.eventLoop.future(ChatMember.from(member: member))
            }
       }
    }
    
    func mapParticipants(req: Request, ids:[String]) throws -> EventLoopFuture<[ChatMember]> {
        return try! mapNavigatorParticipants(req: req, ids: ids).flatMap { navChatMembers in
            return try! mapMembersParticipants(req: req, ids: ids).flatMap { memChatMembers in
                return req.eventLoop.future(navChatMembers + memChatMembers)
            }
        }
    }
    
    func create(req: Request) throws -> EventLoopFuture<MemberChat> {
        let input = try req.content.decode(CreateMemberConversationInput.self)
        let participantsIds:[String] = clean(ids: input.participants)
        
        return try! OrgsController.find(req: req, id: input.orgID).flatMap { org in
            
            if input.role == "navigator" {
                return try! find(req:req, navigatorId: input.creatorID).flatMap { navigator in
                    let creatorId: UUID = navigator.id!
                    let chatMemberCreator = ChatMember.from(navigator: navigator)
                    
                    return try! createChat(req: req,
                               participantsIds: participantsIds,
                               chatMemberCreator: chatMemberCreator,
                               org: org,
                               creatorId: creatorId,
                               message: input.message)
                }
            } else {
                //member
                return try! find(req:req, memberId: input.creatorID).flatMap { member in
                    let creatorId: UUID = member.id!
                    let chatMemberCreator = ChatMember.from(member: member)
                    
                    return try! createChat(req: req,
                               participantsIds: participantsIds,
                               chatMemberCreator: chatMemberCreator,
                               org: org,
                               creatorId: creatorId,
                               message: input.message)
                }
            }
        }
    }
    
    fileprivate func createChat(req: Request,
                                participantsIds:[String],
                                chatMemberCreator: ChatMember,
                                org: Organization,
                                creatorId: UUID,
                                message: AddConversationMessageInput) throws -> EventLoopFuture<MemberChat> {
        return try! mapParticipants(req: req, ids: participantsIds).flatMap{ mappedChatMembers in
            
            return try! create(req: req, creator: chatMemberCreator).flatMap { chatCreator in
                
                return try! create(req: req, chatMembers: mappedChatMembers).flatMap { chatMembers in
                    
                    var allChatMembers = chatMembers
                    allChatMembers.append(chatCreator)
                    
                    return try! TwilioService.createConversation(req: req,
                                                                 org: org,
                                                                 creatorId: creatorId,
                                                                 message: message,
                                                                 chatMembers: allChatMembers,
                                                                 chatMemberCreator: chatCreator)
                }
            }
        }
    }
    
    fileprivate func create(req: Request, creator: ChatMember) throws -> EventLoopFuture<ChatMember> {
        return ChatMember.query(on: req.db).filter(\.$personId == creator.personId).first().flatMap({ member in
            if let member {
                return req.eventLoop.future(member)
            } else {
                return creator.create(on: req.db).transform(to: creator)
            }
        })        
    }
    
    func create(req: Request, chatMembers: [ChatMember]) throws -> EventLoopFuture<[ChatMember]> {
        return chatMembers.sequencedFlatMapEach(on: req.eventLoop) { member in
            try! create(req: req, creator: member)
        }
    }
}
