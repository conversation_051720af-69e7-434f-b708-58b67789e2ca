//
//  File.swift
//  
//
//  Created by <PERSON> on 3/29/23.
//

import Foundation
import Vapor
import Fluent
import JWTKit
import APNS

struct AddUserToChatInput: Content {
    var userID:String?
}

struct CreateChatInput: Content {
    var creatorID:String
    var FriendlyName: String
    
    func toJSON() -> [String:Any?]  {
        return [ "FriendlyName" : self.FriendlyName ]
    }
    
    func dataEncode() -> Data? {
        "FriendlyName=\(FriendlyName)".data(using: .utf8)
    }
}


struct ChatsController: RouteCollection {
    
    func boot(routes: RoutesBuilder) throws {
        let networks = routes.grouped("chats")
        networks.get(use: index)
        networks.post(use: create)
        networks.get(":chatID", use: fetch)
        networks.put(":chatID", use: update)
        networks.get([":chatID", "participants"], use: participants)
        networks.put([":chatID", "members"], use: add)
        networks.post([":chatID", "message"], use: addMessage)
        networks.post([":chatID", "notification"], use: addPush)
        networks.delete(":chatID", use: delete)
    }
        
    func index(req: Request) throws -> EventLoopFuture<Page<Chat>> {
        return Chat.query(on: req.db)
            .with(\.$creator)
            .with(\.$latestMessageSender)
            .with(\.$participants, { user in
                user.with(\.$teams)
            })
            .sort(\.$updatedAt, .descending)
            .paginate(for: req)
    }
    
    func fetch(req: Request) throws -> EventLoopFuture<Chat> {
        guard let id = req.parameters.get("chatID") else { throw NetworkError.error(type: .chat) }
        return Chat.query(on: req.db)
            .filter(\.$conversationSid == id)
            .with(\.$participants, { user in
                user.with(\.$teams)
            })
            .first().unwrap(or:  Abort(.notFound))
    }
    
    
    static func creatorChats(req:Request, creatorID:String) throws -> EventLoopFuture<Page<Chat>> {
        guard let uuid = UUID(uuidString:creatorID) else { throw NetworkError.error(type: .member) }
        let query = UserChats.query(on: req.db)
        return query.join(parent: \.$user)
            .filter(User.self, \.$id == uuid).all().flatMap { chats in
                let ids = chats.compactMap({$0.$chat.id})
//                print(ids)
                return Chat.query(on: req.db)
                    .filter(\.$id ~~ ids)
                    .with(\.$creator)
                    .with(\.$latestMessageSender)
                    .with(\.$participants, { user in
                        user.with(\.$teams)
                    })
                    .sort(\.$updatedAt, .descending)
                    .paginate(for: req)
            }
    }
    
    func update(req: Request) throws -> EventLoopFuture<Chat> {
        guard let id = req.parameters.get("chatID") else { throw NetworkError.error(type: .chat) }
        let input = try req.content.decode(UpdateConversationInput.self)
        return try ChatsController.find(req: req, chatID: id).flatMap { chat in
            chat.title = input.title
            return chat.save(on: req.db).map { chat }
        }
    }
    
    func create(req: Request) throws -> EventLoopFuture<Chat> {
        let input = try req.content.decode(CreateConversationInput.self)
        
        return try! OrgsController.find(req: req, id: input.orgID).flatMap { org in
            //find user
            return try! UsersController.find(req: req, id: input.creatorID).flatMap { creator in
                
                return try! UsersController.findAll(req: req, ids: input.participants).flatMap{ allPartisipants in
                    
                    var allParts = allPartisipants
                    allParts.append(creator)
                    
                    //create conversation
                    return try! TwilioController().create(req: req).flatMap { conversation in
                                        
                        //add creator to converstation as a partisipant
                        var responses: [ClientResponse] = []
                        
                        let chat = conversation.chat(creator: creator)
                        let convoID = conversation.sid ?? ""
                        return allParts.compactMap { partis in
                            let memberData = partis.memberInfo()
                            
                            return try! TwilioController.addMember(req: req, id: convoID, input: AddMemberInput(identity: memberData.identity, attributes: ["fullName":memberData.fullName])).flatMap { res in
//                                print("Added member:\(memberData.fullName)")
                                responses.append(res)
                                return req.eventLoop.future(chat)
                            }
                        }.flatten(on: req.eventLoop).transform(to: responses).flatMap { twilioRes in
//                            print("Added Member Complete")
                            return try! TwilioController.createMessage(req: req, convoID: convoID, input: input.message).flatMap { msgResponse in
//                                print("Created Message Complete")
                                
                                return org.$chats.create(chat, on: req.db).transform(to: chat).flatMap { newChat in
                                    
                                    newChat.$creator.id = creator.id
                                    newChat.$latestMessageSender.id = creator.id
                                    newChat.latestMessage = input.message.body
                                    
                                    return newChat.update(on: req.db).transform(to: newChat).flatMap { updatedChat in
                                        
                                        return updatedChat.$participants.attach(allParts, on: req.db).flatMap { _ in
                                            let users = allPartisipants.filter({$0.id != creator.id})
                                            return users.compactMap { user in
                                                return try! sendChatNotification(req: req,
                                                                                 name: creator.fullName(),
                                                                                 userId: user.id?.uuidString.lowercased() ?? "",
                                                                                 refId: chat.id?.uuidString.lowercased() ?? "",
                                                                                 body: input.message.body,
                                                                                 pushTitle: chat.conversationFriendlyName)
                                            }.flatten(on: req.eventLoop).transform(to: updatedChat)
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    
    static func find(req: Request, convoID: String) throws -> EventLoopFuture<Chat> {
        return Chat.query(on: req.db).filter(\.$conversationSid == convoID).first().unwrap(or:  Abort(.notFound))
    }
    
    static func find(req: Request, chatID: String) throws -> EventLoopFuture<Chat> {
        return Chat.query(on: req.db).filter(\.$id == UUID(uuidString: chatID)!).with(\.$participants).first().unwrap(or:  Abort(.notFound))
    }
    
    func addPush(req: Request) throws -> EventLoopFuture<ClientResponse> {
        guard let id = req.parameters.get("chatID") else { throw NetworkError.error(type: .chat) }
        return try! ChatsController.find(req: req, chatID: id).flatMap { chat in
            return try! TwilioController().enablePushNotification(req: req, chatServiceID: chat.chatServiceSid)
        }
    }
    
    fileprivate func configurePush(req: Request, topic: PushTopic) throws -> EventLoopFuture<Void> {
        print("\napns.configuration for: \(topic.rawValue)\n")
        req.application.apns.configuration =  try APNSwiftConfiguration(authenticationMethod: .jwt(key: .private(filePath: "apns/AuthKey_2VMTKXY3Z7.p8"),
                                                                                                   keyIdentifier: JWKIdentifier(string: "2VMTKXY3Z7"),
                                                                                                   teamIdentifier: "43Q4UWBL85"),
                                                                        topic: topic.rawValue, environment: isProduction ? .production : .sandbox)
        return req.eventLoop.future()
    }
    
    func addMessage(req: Request) throws -> EventLoopFuture<ClientResponse> {
        guard let id = req.parameters.get("chatID") else { throw NetworkError.error(type: .chat) }
        let input = try req.content.decode(AddConversationMessageInput.self)
        let membersController = MemberChatsController()
        
        //load correct topic
        return try! configurePush(req: req, topic: PushTopic.topicFor(type: input.sendTo)).flatMap({ _ in
            //since adding message only contains 1 user or member will check for both and grab the last
            return try! membersController.mapParticipants(req: req, ids: [input.author]).flatMap { foundMembers in
                
                //create if needed
                return try! membersController.create(req: req, chatMembers: foundMembers).flatMap { creator in
                    let messageSender =  creator.last
                    let isMemberMessage = input.memberMessage // messageSender?.role == "member"
                    
                    if isMemberMessage ?? false {
                        
                        return try! MemberChatsController.find(req: req, chatID: id).flatMap { chat in
                            return try! createTwilioMessage(req: req, member: chat, input: input, senderId: messageSender?.id).flatMap({ response in
                                
                                let users = chat.participants.filter({$0.id != messageSender?.personId})
                                return users.compactMap { user in
                                    return try! sendChatNotification(req: req,
                                                                     name: messageSender?.personName.capitalized ?? "",
                                                                     userId: user.personId.uuidString,
                                                                     refId: chat.conversationSid,
                                                                     body: input.body,
                                                                     pushTitle: chat.conversationFriendlyName).transform(to: response)
                                }.flatten(on: req.eventLoop).transform(to: response)
                            })
                        }
                    } else {
                        return try! ChatsController.find(req: req, chatID: id).flatMap { chat in
                            return try! createTwilioMessage(req: req, navigator: chat, input: input, senderId: messageSender?.personId).flatMap({ response in
                                
                                let users = chat.participants.filter({$0.id != messageSender?.personId})
                                return users.compactMap { user in
                                    return try! sendChatNotification(req: req,
                                                                     name: messageSender?.personName.capitalized ?? "",
                                                                     userId: user.id?.uuidString ?? "",
                                                                     refId: chat.conversationSid,
                                                                     body: input.body,
                                                                     pushTitle: chat.conversationFriendlyName).transform(to: response)
                                }.flatten(on: req.eventLoop).transform(to: response)
                            })
                        }
                    }
                }
            }
        })
    }
    
    func createTwilioMessage(req: Request,
                             member: MemberChat? = nil,
                             navigator: Chat? = nil,
                             input: AddConversationMessageInput,
                             senderId: UUID?) throws -> EventLoopFuture<ClientResponse> {
        
        guard let convId = member?.conversationSid ?? navigator?.conversationSid else { throw NetworkError.error(type: .badRequest, msg: "missing conversation id.") }
        
        return try! TwilioController.createMessage(req: req,
                                                   convoID: convId,
                                                   input: input).flatMap({ response in
            if let memberChat = member {
                
                memberChat.latestMessage = input.body
                memberChat.$latestMessageSender.id = senderId
                return memberChat.update(on: req.db).transform(to: response)
                
            } 
            else if let navigatorChat = navigator {
            
                navigatorChat.latestMessage = input.body
                navigatorChat.$latestMessageSender.id = senderId
                return navigatorChat.update(on: req.db).transform(to: response)
                
            } 
            else {
                
                return req.eventLoop.makeFailedFuture( NetworkError.error(type: .badRequest, msg: "missing chat.") )
            }
        })
    }
    
    func sendChatNotification(req: Request, name: String, userId: String, refId: String, body: String, pushTitle: String) throws -> EventLoopFuture<Void> {
        let title = "New chat message received"
        let msg = "Message from \(name.capitalized)."
        return try! NotificationsController.notification(req: req,
                                                         input: NotificationCreateInput(title: title,
                                                                                        kind: "chat",
                                                                                        message: msg,
                                                                                        read: false,
                                                                                        userID: userId,
                                                                                        meta: MetaData(data: ["chat" : refId.lowercased()]))).flatMap({ _ in
            
            
            return try! APNSPushController.send(req: req,
                                                userID: userId.lowercased(),
                                                title: pushTitle,
                                                subtitle: msg,
                                                msg: body)
        })
    }
    
    func participants(req: Request) throws -> EventLoopFuture<ClientResponse> {
        guard let id = req.parameters.get("chatID") else { throw NetworkError.error(type: .chat) }
        return try! ChatsController.find(req: req, chatID: id).flatMap { chat in
            return try! TwilioController.fetchParticipants(req: req, convoID: chat.conversationSid)
        }
    }
    
    func add(req: Request) throws -> EventLoopFuture<Chat> {
        guard let id = req.parameters.get("chatID") else { throw NetworkError.error(type: .chat) }//CHXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
        let input = try req.content.decode(AddUserToChatInput.self)
        
        guard let userID = input.userID else { throw NetworkError.error(type: .badRequest, msg: "User ID is required when adding a person to a chat.")}
        return try! UsersController.find(req: req, id: userID).flatMap { user in
            
            let memberInfo = user.memberInfo()
            
            return try! ChatsController.find(req: req, chatID: id).flatMap { chat in
                let memberInput = AddMemberInput(identity: memberInfo.identity, attributes: ["fullName":memberInfo.fullName])
                
                return try! TwilioController.addMember(req: req, id: chat.conversationSid, input: memberInput).flatMap { res in
                    
                    return chat.$participants.attach(user, on: req.db).transform(to: chat)
                }
            }
        }
    }
        
    static func create(req: Request, convo: Conversation, forUser:String) throws -> EventLoopFuture<Chat> {
        return try! UsersController.find(req: req, id: forUser).flatMap { creator in
            let chat = convo.chat(creatorID: forUser)
            
            return chat.create(on: req.db).transform(to: chat).flatMap { newChat in
                newChat.$creator.id = creator.id
                
                return newChat.update(on: req.db).transform(to: newChat).flatMap { updatedChat in
                    return updatedChat.$participants.attach(creator, on: req.db).transform(to: updatedChat)
                }
            }
        }
    }
    
    func delete(req: Request) throws -> EventLoopFuture<HTTPStatus> {
        return Chat.find(req.parameters.get("chatID"), on: req.db).unwrap(or: NetworkError.error(type: .chat))
            .flatMap { chat in
                return try! TwilioController().deleteConvo(req: req, id: chat.conversationSid).flatMap { response in
                    return chat.delete(on: req.db).transform(to: .ok)
                }
            }
    }
}



