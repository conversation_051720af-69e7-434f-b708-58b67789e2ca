//
//  File.swift
//  
//
//  Created by <PERSON> on 3/7/24.
//

import Foundation
import Vapor
import Fluent
import Queues


struct AttachmentInput: Content {
    var name:       String?
    var kind:       String
    var type:       String
    var url:        String
    var category:   String?
    
    func attachmentModel() -> Attachment {
        return Attachment(name: name, kind: kind, type: type, url: url, category: category)
    }
    
    func returnUpdatedModel(attachment:Attachment) -> Attachment {
        
        if let name = name {
            attachment.name = name
        }
        
        attachment.kind = kind
        attachment.type = type
        attachment.url = url
        
        if let category = category {
            attachment.category = category
        }
       
        return attachment
    }
}


struct WellupContentInput: Content {
    var title:      String
    var desc:      String?
    var kind:      String
    var state:    String
    var markdown: String?
    var ref:      String?
    
    var attachments:  [UploadInput]?
    var tags: [TagInput]?
    
    func content() -> WellupContent {
        return WellupContent(title: title, desc: desc, kind: kind, state: state, markdown: markdown, ref: ref)
    }
    
    func allTags() -> [Tag] {
        return self.tags?.compactMap({$0.tag()}) ?? []
    }
}


struct ContentController: RouteCollection {
    
    func boot(routes: RoutesBuilder) throws {
        let content = routes.grouped("content")
        
        content.get(use: index)
//        content.get(":netID", use: lookup)
             
        content.post(use: create)
//        content.put(":netID", use: update)
//        users.put([":netID", "service"], use: attachServices)
                
        content.delete(":netID", use: delete)
    }
    
    //MARK - Fetch
    func lookup(req: Request) throws -> EventLoopFuture<WellupContent> {
        guard let id = req.parameters.get("netID") else { throw NetworkError.error(type: .content) }
        guard let networkID = UUID(id) else { throw NetworkError.error(type: .content)}
        return WellupContent.query(on: req.db).filter(\.$id == networkID)
            .with(\.$attachments).first().flatMapThrowing() {model in
            guard let foundModel = model else { throw NetworkError.error(type: .content) }
            return foundModel
        }
    }
        
    func find(req: Request, uuid:UUID) throws -> EventLoopFuture<WellupContent> {
        return WellupContent.query(on: req.db).filter(\.$id == uuid)
            .with(\.$attachments)
            .with(\.$tags)
            .first().flatMapThrowing() { model in
            guard let foundModel = model else { throw NetworkError.error(type: .content) }
            return foundModel
        }
    }
    
    func index(req: Request) throws -> EventLoopFuture<Page<WellupContent>> {
        return self.buildQuery(query: WellupContent.query(on: req.db), req: req).paginate(for: req)
    }
    
    func createAttachments(req: Request,
                           content: WellupContent,
                           inputs: [UploadInput]) throws -> EventLoopFuture<Void> {
        if !inputs.isEmpty, let uuid = content.id {
            return try! find(req: req, uuid: uuid).flatMap { foundContent in
                
                return inputs.sequencedFlatMapEach(on: req.eventLoop) { uploadInput in
                    return try! AttachmentsController().uploadImageToCloudinaryWith(req,input: uploadInput).flatMap { resposne in
                        let attach = Attachment(name: uploadInput.name,
                                                kind: uploadInput.type,
                                                type: "image",
                                                url: resposne.secure_url,
                                                category: uploadInput.category,
                                                refID: resposne.public_id)
                        return foundContent.$attachments.create(attach, on: req.db)
                    }
                }
            }
            
        } else {
            return req.eventLoop.future()
        }
    }

    
    //MARK: - Create
    func create(req: Request) throws -> EventLoopFuture<WellupContent> {
        let input = try req.content.decode(WellupContentInput.self)
        let content = input.content()
        let allTags = input.allTags()
        return content.save(on: req.db).transform(to: content).flatMap { content in
            
            return content.$tags.create(allTags, on: req.db).transform(to: content).flatMap { content in
                return try! createAttachments(req: req, content: content, inputs: input.attachments ?? []).flatMap { _ in
                    return try! find(req: req, uuid: content.id!)
                }
            }
        }
    }
    
//    func update(req: Request) throws -> EventLoopFuture<ZalaContent> {
//        guard let id = req.parameters.get("netID") else { throw NetworkError.error(type: .content) }
//        let input = try req.content.decode(ZalaContentInput.self)
//        return try! ContentController.find(req: req, id: id).flatMap { content in
//            let content = input.returnUpdatedModel(content: content)
//            return content.save(on: req.db).transform(to: content).flatMap { content in
//                return try! createAttachmentsIfNeeded(req: req, input: input, content: content).flatMap { updatedContent in
//                    return try! createThumbnailsIfNeeded(req: req, input: input, content: content).flatMap { updatedContent in
//                        return req.eventLoop.future(updatedContent)
//                    }
//                }
//            }
//        }
//    }
    
//    //MARK: - Delete
    func delete(req: Request) throws -> EventLoopFuture<HTTPStatus> {
        guard let id = req.parameters.get("netID") else { throw NetworkError.error(type: .content) }
        guard let networkID = UUID(id) else { throw NetworkError.error(type: .content)}
        return try find(req: req, uuid: networkID).flatMap { network in
            return network.delete(on: req.db).transform(to: HTTPStatus.accepted)
        }
    }
    
    
//    //MARK: - Query
    fileprivate func buildQuery(query:QueryBuilder<WellupContent>, req:Request) -> QueryBuilder<WellupContent> {
        let name:String?             = req.query["name"]
        let _:String?            = req.query["focus"]
        let kind:String?            = req.query["kind"]
        let _:String?          = req.query["product"]
        let tags:[String]?           = req.query["tags"]
        let state:String?            = req.query["state"]
        
        if let nm = name  {
            query.filter(\.$title, .custom("ilike"), "%\(nm.lowercased())%")
        }

        if let kind  {
            query.filter(\.$kind == kind)
        }

        if let tags, !tags.isEmpty {
            query.join(Tag.self, on: \WellupContent.$id == \Tag.$content.$id)
            query.filter(Tag.self, \.$key ~~ tags)
        }
        
//        if let product = product {
//            query.filter(\.$product == product)
//        }
//        
//        if let ref = ref?.lowercased() {
//            query.filter(\.$ref == ref)
//        }
        
        if let state = state {
            query.filter(\.$state == state)
        }
        
        return query
            .with(\.$attachments)
            .with(\.$tags)
    }
}

