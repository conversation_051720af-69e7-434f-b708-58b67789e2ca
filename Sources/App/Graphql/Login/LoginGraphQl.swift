//
//  File.swift
//  hmbl-core
//
//  Created by <PERSON> on 3/20/25.
//
import Vapor

// Define the GraphQL mutation as a struct
struct LoginMutation: Content {
    static let query: String = """
     mutation Login($email: String!, $password: String!) {
                login(input: { email: $email, password: $password }) {
                    success
                    user {
                        token
                    }
                    errors {
                        message
                    }
                }
            }        
    """
}

struct GraphQLRequest: Content {
    let query: String
    let variables: [String: AnyCodable]?
}

struct GraphQLMutationRequest: Content {
    let mutation: String
    let variables: [String: AnyCodable]?
}

struct GraphQLResponse<T: Codable>: Codable {
    let data: T?
}

struct LoginGraphQlResponse: Content, Codable {
    let login: LoginData
}

struct LoginData: Codable {
    let success: Bool
    let errors: [GraphQLError]?
    let user: UserGraphQl?
}

struct UserGraphQl: Codable {
    let token: String
}

struct GraphQLError: Codable {
    let message: String
}
