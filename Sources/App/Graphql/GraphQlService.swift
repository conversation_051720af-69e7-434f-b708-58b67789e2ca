//
//  File.swift
//  hmbl-core
//
//  Created by <PERSON> on 3/23/25.
//

import Foundation
import Vapor
import Fluent

class GraphQLService {
    let client: Client
    let endpoint: URI
    let token: String?

    init(client: Client, token: String? = nil) {
        self.client = client
        self.endpoint = URI(string: isProduction ? "https://wellup-stg-5c9a56db8960.herokuapp.com/gql" : "https://wellup-stg-5c9a56db8960.herokuapp.com/gql")
        self.token = token
    }

    func sendRequest<T: Content>(query: String, isMutation: Bool = false, variables: [String: AnyCodable]? = nil, responseType: T.Type) -> EventLoopFuture<T> {
            return client.post(endpoint) { req in
                do {
                    // Use a single request type for both queries and mutations
                    let graphQLRequest = GraphQLRequest(query: query, variables: variables)
                    try req.content.encode(graphQLRequest)
                    
                    req.headers.contentType = .json
                    if let token = self.token {
                        req.headers.bearerAuthorization = .init(token: token)
                    }
                    
                    // Debugging: Print the request JSON
                    let requestData = try JSONEncoder().encode(graphQLRequest)
                    print("📡 GraphQL Request Sent: \(String(data: requestData, encoding: .utf8) ?? "Encoding Failed")")
                } catch {
                    print("❌ Error encoding GraphQL request: \(error)")
                }
            }
            .flatMapThrowing { res in
                print(res.content)
                let decodedResponse = try res.content.decode(GraphQLResponse<T>.self)
                
                
                guard let data = decodedResponse.data else {
                    throw Abort(.internalServerError, reason: "No data returned from GraphQL API")
                }
                
                return data
            }
        }
    
    func create<T: Content>(service req: Request, input: T, token: String) throws -> EventLoopFuture<ClientResponse> {
        return client.post(endpoint) { postReq in
             do {
                 try postReq.content.encode(input)
                 postReq.headers.contentType = .json
                 postReq.headers.bearerAuthorization = BearerAuthorization(token: token) // If needed

                 // Debugging: Print request payload
                 let requestData = try JSONEncoder().encode(input)
                 print("📡 GraphQL Request Sent: \(String(data: requestData, encoding: .utf8) ?? "Encoding Failed")")

             } catch {
                 print("❌ Error encoding GraphQL request: \(error)")
             }
         }
    }
}
