//
//  File.swift
//  
//
//  Created by <PERSON> on 1/12/24.
//

import Foundation
import Vapor
import Fluent

struct MapboxAddress: Content {
    var id: String
    var placeName: String
    var center:[Double]
    
    enum CodingKeys: String, CodingKey {
        case id
        case placeName = "place_name"
        case center
    }
}

struct MapboxService {
    static func mapboxAddress(req: Request, address:String) throws -> EventLoopFuture<MapboxAddress?> {
        //        guard let key = Environment.get("GOOGLE_KEY") else { return req.eventLoop.future(nil) }
        let client = req.client
        let url = queryURL(urlString: "https://api.mapbox.com/geocoding/v5/mapbox.places/\(address).json?types=address&access_token=pk.eyJ1IjoibWlzZml0bGFicyIsImEiOiJjbGFpaWlnMDIwMzBrM3ZwZ3QyOTRjaWNmIn0.MnJqKb3rEFu-JsMAbwFAEQ")
        let uri = URI(string: url!)
        
        return client.get(uri).flatMap { res in
            let option = res.body ?? ByteBuffer(string: "0")
            let data = try! JSONSerialization.jsonObject(with: Data(buffer: option), options: .allowFragments)
            
            if let json = data as? [String:Any],
               let features = json["features"] as? [[String:Any]],
               let addressData = features.last,
               let placeName = addressData["place_name"] as? String,
               let addId = addressData["id"] as? String,
               let centerData = addressData["center"] as? [Double] {
                
                return req.eventLoop.future(MapboxAddress(id: addId, placeName: placeName, center: centerData))
            } else {
                return req.eventLoop.future(nil)
            }
        }
    }
    
    static func updateAddress(req:Request, address:Address) throws -> EventLoopFuture<Void> {
        return try MapboxService.mapboxAddress(req: req, address: address.fullAddress()).flatMap { data in
            if let mapAddress = data,
               let lat = mapAddress.center.first,
               let lng = mapAddress.center.last  {
                address.lat = "\(lat)"
                address.lon = "\(lng)"
                return address.update(on: req.db)
            } else {
                return req.eventLoop.future()
            }
        }
    }
}
