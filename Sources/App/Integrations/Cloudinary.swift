//
//  File.swift
//  
//
//  Created by <PERSON> on 2/17/24.
//

import Foundation
import Vapor

struct CloudinaryUploadInput: Content {
    var file: String
    var api_key: String
    var public_id: String
    var timestamp: Double
    var signature: String
}

struct CloudinaryDestroyInput: Content {
    var api_key: String
    var public_id: String
    var timestamp: Double
    var signature: String
}

struct CloudinarySignature {
    var publicId: String
    var signature: String
    var timestamp: Double
    var uploadUri: URI
}

struct CloudinaryConfiguration {
    
    static let uploadUri: URI =  URI(string:"https://api.cloudinary.com/v1_1/cg1-solutions/image/upload")
    
    static let destroyUri: URI =  URI(string:"https://api.cloudinary.com/v1_1/cg1-solutions/image/destroy")
    
    static let apiKey = Environment.get("CLOUDINARY_API_KEY") ?? ""
        
    static func destroySigned(refId: String) -> CloudinaryDestroyInput {
        let timestamp  = Date().timeIntervalSince1970
        let publicId   = refId
        let secret     = Environment.get("CLOUDINARY_SECRET") ?? ""
        let signature  = SHA256.hash(data:Data("public_id=\(publicId)&timestamp=\(timestamp)\(secret)".utf8))
        let hashString = signature.compactMap { String(format: "%02x", $0) }.joined()
        
        return CloudinaryDestroyInput(api_key: Environment.get("CLOUDINARY_API_KEY") ?? "", public_id: publicId, timestamp: timestamp, signature: hashString)
    }
    
    static func sanitizePublicId(_ input: String) -> String {
        let allowedChars = CharacterSet.alphanumerics.union(CharacterSet(charactersIn: "_-/"))
        return input
            .components(separatedBy: allowedChars.inverted)
            .joined()
    }
    
    static func signed(name: String, type: String) -> CloudinarySignature {
        let path       =  isProduction ?  "donahealth/production" : "donahealth/staging"
        let timestamp  = Date().timeIntervalSince1970
        var publicId   = "\(path)/\(type.lowercased())/\(name.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? name)"
        
        publicId = "\(publicId)"+"\(timestamp)"
        publicId = sanitizePublicId(publicId)
        
        
        let secret     = Environment.get("CLOUDINARY_SECRET") ?? ""
        let signature = SHA256.hash(data:Data("public_id=\(publicId)&timestamp=\(timestamp)\(secret)".utf8))
        
        let hashString = signature.compactMap { String(format: "%02x", $0) }.joined()
        //https://api.cloudinary.com/v1_1/<cloud name>/<resource_type>/upload
        //resource_type is the type of file to upload. Valid values: image, raw, video and auto to automatically detect the file type.
        return CloudinarySignature(publicId: publicId, signature: hashString, timestamp: timestamp, uploadUri: uploadUri)
    }
    
    static func input(name: String, type: String, base64: String) -> CloudinaryUploadInput {
        let config = CloudinaryConfiguration.signed(name: name, type: type)
        return CloudinaryUploadInput(file: base64,
                                     api_key: CloudinaryConfiguration.apiKey,
                                     public_id: config.publicId,
                                     timestamp: config.timestamp,
                                     signature: config.signature)
    }
}
