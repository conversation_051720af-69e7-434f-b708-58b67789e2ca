//
//  File.swift
//  
//
//  Created by <PERSON> on 2/1/23.
//

import Foundation
import Vapor
import Fluent

struct GoogleAddress: Content {
    var lat:Double
    var lng:Double
}

func queryURL(urlString:String) -> String? {
    return urlString.addingPercentEncoding(withAllowedCharacters: CharacterSet.urlQueryAllowed)
}

struct GoogleService {
    static func storeAddress(req: Request, address:String) throws -> EventLoopFuture<GoogleAddress?> {
        guard let key = Environment.get("GOOGLE_KEY") else { return req.eventLoop.future(nil) }
        let client = req.client
        let url = queryURL(urlString: "https://maps.googleapis.com/maps/api/geocode/json?address=\(address)&key=\(key)")
        let uri = URI(string: url!)
        return client.get(uri).flatMap { res in
            let option = res.body ?? ByteBuffer(string: "0")
            let data = try! JSONSerialization.jsonObject(with: Data(buffer: option), options: .allowFragments)
            
            if let json = data as? [String:Any], let status = json["status"] as? String {                
                if status.lowercased() == "ok",
                   let results  = json["results"] as? [[String:Any]],
                   let resultJSON = results.last,
                   let gemoetryJSON = resultJSON["geometry"] as? [String:Any],
                   let locationJSON = gemoetryJSON["location"] as? [String : Double] {
                    guard let lat = locationJSON["lat"] else { return req.eventLoop.future(nil) }
                    guard let lng = locationJSON["lng"] else { return req.eventLoop.future(nil) }
                                        
                    return req.eventLoop.future(GoogleAddress(lat: lat, lng: lng))
                } else {
                    return req.eventLoop.future(nil)
                }
            } else {
                return req.eventLoop.future(nil)
            }
        }
    }
    
    
    static func createGoogleAddressIfNeeded(req:Request, address:Address) throws -> EventLoopFuture<Address> {
        return try! GoogleService.storeAddress(req: req, address: address.fullAddress()).flatMap { googleAddress in
            if let gogleAddress = googleAddress {
//                print("goggle: Lat: \(gogleAddress.lat) lng: \(gogleAddress.lng)")
                address.lat = String(gogleAddress.lat)
                address.lon = String(gogleAddress.lng)
                return address.save(on: req.db).transform(to: address)
            } else {
//                print("invalid address for \(address.fullAddress())")
                return req.eventLoop.future(address)
            }
        }
    }
}
//{
//    "results": [
//        {
//            "address_components": [
//                {
//                    "long_name": "1600",
//                    "short_name": "1600",
//                    "types": [
//                        "street_number"
//                    ]
//                },
//                {
//                    "long_name": "Amphitheatre Parkway",
//                    "short_name": "Amphitheatre Pkwy",
//                    "types": [
//                        "route"
//                    ]
//                },
//                {
//                    "long_name": "Mountain View",
//                    "short_name": "Mountain View",
//                    "types": [
//                        "locality",
//                        "political"
//                    ]
//                },
//                {
//                    "long_name": "Santa Clara County",
//                    "short_name": "Santa Clara County",
//                    "types": [
//                        "administrative_area_level_2",
//                        "political"
//                    ]
//                },
//                {
//                    "long_name": "California",
//                    "short_name": "CA",
//                    "types": [
//                        "administrative_area_level_1",
//                        "political"
//                    ]
//                },
//                {
//                    "long_name": "United States",
//                    "short_name": "US",
//                    "types": [
//                        "country",
//                        "political"
//                    ]
//                },
//                {
//                    "long_name": "94043",
//                    "short_name": "94043",
//                    "types": [
//                        "postal_code"
//                    ]
//                }
//            ],
//            "formatted_address": "1600 Amphitheatre Pkwy, Mountain View, CA 94043, USA",
//            "geometry": {
//                "location": {
//                    "lat": 37.4217404,
//                    "lng": -122.0857055
//                },
//                "location_type": "ROOFTOP",
//                "viewport": {
//                    "northeast": {
//                        "lat": 37.42308938029149,
//                        "lng": -122.0843565197085
//                    },
//                    "southwest": {
//                        "lat": 37.42039141970849,
//                        "lng": -122.0870544802915
//                    }
//                }
//            },
//            "place_id": "ChIJVYBZP-Oxj4ARls-qJ_G3tgM",
//            "plus_code": {
//                "compound_code": "CWC7+MP Mountain View, CA, USA",
//                "global_code": "849VCWC7+MP"
//            },
//            "types": [
//                "street_address"
//            ]
//        }
//    ],
//    "status": "OK"
//}
