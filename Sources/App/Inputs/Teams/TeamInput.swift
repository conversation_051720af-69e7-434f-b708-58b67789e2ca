//
//  File.swift
//  
//
//  Created by <PERSON> on 2/2/23.
//

import Foundation
import Vapor
import Fluent

struct TeamCreateInput: Content {
    var orgID:          String?
    var name:           String?
    var address:        AddressInput?
    var navigators:     [String]?
    var teamLeads:      [String]?
        
    func returnUpdatedModel(team:Team) -> Team {
        if let nme = name {
            team.name = nme
        }
        return team
    }
    
    
    func team() -> Team {
        return Team(name: name ?? "")
    }
}
