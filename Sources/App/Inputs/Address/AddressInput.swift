//
//  File.swift
//  
//
//  Created by <PERSON> on 2/1/23.
//

import Foundation
import Vapor
import Fluent

struct AddressInput: Content {
     var street:    String?
     var street2:   String?
     var city:      String?
     var state:     String?
     var zip:       String?
     var country:   String?
     var county:    String?
     var kind:      String?
     var note:      String?
     var label:     String?    
    
    
    func address() -> Address {
        return Address(
            street: self.street ?? "",
            street2: self.street2 ?? "",
            city: self.city ?? "",
            state: self.state ?? "" ,
            zip: self.zip ?? "",
            country: self.country ?? "US",
            county: self.county ?? "",
            kind: self.kind ?? "main")
    }

    func returnUpdatedModel(address:Address) -> Address {
        if let street = street {
            address.street = street
        }
        if let street2 = street2 {
            address.street2 = street2
        }
        if let city = city {
            address.city = city
        }
        if let state = state {
            address.state = state
        }
        if let zip = zip {
            address.zip = zip
        }
        if let country = country {
            address.country = country
        }
        if let county = county {
            address.county = county
        }
        if let kind = kind {
            address.kind = kind
        }
       
        if let note = note {
            address.note = note
        }
       
        
        return address
    }
}
