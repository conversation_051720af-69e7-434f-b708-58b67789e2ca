//
//  File.swift
//  
//
//  Created by <PERSON> on 2/4/23.
//

import Foundation
import Vapor
import Fluent

struct PhoneInput: Content {
     var label:    String
     var number:   String
    
    func phone() -> PhoneNumber {
        return PhoneNumber(label: label, number: number)
    }

    func returnUpdatedModel(phone:PhoneNumber) -> PhoneNumber {
        phone.label  = label
        phone.number = number
        return phone
    }
}
