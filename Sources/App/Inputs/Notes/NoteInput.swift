//
//  File.swift
//  
//
//  Created by <PERSON> on 2/7/23.
//
import Foundation
import Vapor
import Fluent


struct TagInput: Content {
    var name:      String
    var key:       String
    var color:     String?
    
    func tag() -> Tag {
        return Tag(name: name, key: key, color: color ?? "0ABF89")
    }
}

struct NoteUpdateInput: Content {
    var memberID:   String?
    var title:      String?
    var subtitle:   String?
    var type:       String?
    var msg:        String?
    var status:     String?
    var tags:       [TagInput]?
    var attachments: [UploadInput]?
    var publish: Bool?
    
    func updatedNote(_ note: Note) -> Note {
        if let title = title {
            note.title = title
        }
        if let subtitle = subtitle {
            note.subtitle = subtitle
        }
        if let type = type {
            note.type = type
        }
        if let msg = msg {
            note.msg = msg
        }
        if let status = status {
            note.status = status
        }
        return note
    }
    
    func allTags() -> [Tag] {
        return self.tags?.compactMap({$0.tag()}) ?? []
    }
}

struct NoteInput: Content {
    var creator:    String
    var memberID:   String?
    var title:      String
    var subtitle:   String?
    var type:       String
    var msg:        String
    var status:     String? = "active"
    var tags:       [TagInput]?
    var attachments: [UploadInput]?
    
        
    func note() -> Note {
        return Note(title: title, type: type, subtitle: subtitle,  msg: msg, status: status)
    }
    
    func allTags() -> [Tag] {
        return self.tags?.compactMap({$0.tag()}) ?? []
    }
}
