//
//  File.swift
//  
//
//  Created by <PERSON> on 2/1/23.
//

import Foundation
import Vapor
import Fluent

struct HouseholdAttachMembersInput: Content {
    var members:[String]
}

struct HouseholdCreatePetInput: Content {
    var pet: String    
}

struct HouseholdInput: Content {
    var orgID:           String?
    var title:           String?
    var type:            String?
    var kind:            String?
    var lastVisit:       String?
    var householdScore:  String?
    var members:        [String]?
    var headOfHouseID:   String?
    var address:         AddressInput?
    
    var attachment:      UploadInput?
    
    var teams:           [String]?
        
    
    func minHousehold() -> Household {
        return Household(title: title ?? "", type: type ?? "", kind: kind ?? "")
    }
    
    func returnUpdatedModel(household:Household) -> Household {
        if let title = title {
            household.title = title
        }
        if let type = type {
            household.type = type
        }
        if let kind = kind {
            household.kind = kind
        }
        if let lastVisit = lastVisit {
            household.lastVisit = lastVisit
        }
        if let householdScore = householdScore {
            household.householdScore = householdScore
        }
        
        return household
    }
    
    
    func hasHeadHousehold() -> Bool {
        return headOfHouseID != nil
    }
    
    func hasAdd<PERSON>() -> Bool {
        return address != nil
    }
   
}
