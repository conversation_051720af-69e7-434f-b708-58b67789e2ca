//
//  File.swift
//  
//
//  Created by <PERSON> on 1/31/23.
//

import Foundation
import Vapor
import Fluent

struct AnimalAssignInput: Content {
    var orgID: String?
    var name:  String?
    var type:  String?
    var kind:  String?
    var age:   String?
    var household:String?
    
    func returnUpdatedModel(pet:Animal) -> Animal {
        if let name = name {
            pet.name = name
        }
        if let type = type {
            pet.type = type
        }
        if let kind = kind {
            pet.kind = kind
        }
        if let age = age {
            pet.age = age
        }
        
        return pet
    }
}

struct AnimalInput: Content {
    var orgID: String?
    var name:  String
    var type:  String
    var kind:  String?
    var age:   String?
    
    func pet() -> Animal {
        return Animal(name: name, type: type, kind: kind, age: age)
    }
}

struct SchoolInput: Content {
    var name: String
    var grade: String
}

struct MemberStatusRemoveInput: Content {
    var id: String
    var kind: String
}


struct MemberStatusInput: Content {
    var id: String?
    var name: String?
    var state: String?
    var kind: String?
    var location: String?
    var startDate: String?
    var endDate: String?
    var address: AddressInput?
    
    func isStateChange(state: String) -> Bool {
        print("\ndoes State: \(state.lowercased()) match \(self.state?.lowercased() ?? "")\n")
        return state.lowercased() != self.state?.lowercased()
    }
    
    func returnUpdatedModel(status: MemberStatus) -> MemberStatus {
        if let name {
            status.name = name
        }
        if let state {
            status.state = state
        }
        if let kind {
            status.kind = kind
        }
        if let location {
            status.location = location
        }
        if let startDate {
            status.startDate = startDate
        }
        if let endDate {
            status.endDate = endDate
        }
        
        return status
    }
}


struct MembersUpdateInput: Content {
    var email:              String?
    var firstName:          String?
    var middleName:         String?
    var lastName:           String?
    var type:               String?
    var roles:             [String]?
    var dob:                String? /// Date format: yyyy-MM-dd
    var gender:             String?
    var ethnicity:          String?
    var sexualIdentity:     String?
    var genderIdentity:     String?
    var pronouns:           String?
    var lang:               String?
    var referredBy:         String?
    var lastAt:             String?
    var status:             String?
    var score:              String?
    var phone:              String?
    
    var refId:              String?
    var pregnancyStatus:    String?
    /// Date format: yyyy-MM-dd
    var deliveryDate:       String?
    var military:           String?
    
    var school:             SchoolInput?
    var pet:                AnimalInput?
    var address:            AddressInput?
    var reason:             ReasonInput?
    var enrolledOn:         String?
    var unenrolledDate:     String?
    var lastContact:        String?
    var homeless:           Bool?
    var memberStatus:       MemberStatusInput?
    var meta:               MetaData?
    var tags:              [TagInput]?
    
    var allTags: [Tag] {
        tags?.compactMap({$0.tag()}) ?? []
    }
        
    func returnUpdatedModel(user:Member) -> Member {
        if let email = email {
            user.email = email.lowercased()
        }
        if let firstName = firstName {
            user.firstName = firstName.lowercased()
        }
        if let middleName = middleName {
            user.middleName = middleName.lowercased()
        }
        if let lastName = lastName {
            user.lastName = lastName.lowercased()
        }
        if let type = type {
            user.type = type
        }
        if let roles = roles {
            user.roles = roles
        }
        if let dob = dob {
            user.dob = dob
        }
        if let gender = gender {
            user.gender = gender.lowercased()
        }
        if let ethnicity = ethnicity {
            user.ethnicity = ethnicity.lowercased()
        }
        if let sexualIdentity = sexualIdentity {
            user.sexualIdentity = sexualIdentity.lowercased()
        }
        if let genderIdentity = genderIdentity {
            user.genderIdentity = genderIdentity.lowercased()
        }
        if let pronouns = pronouns {
            user.pronouns = pronouns.lowercased()
        }
        if let lang = lang {
            user.lang = lang.lowercased()
        }
        if let referredBy = referredBy {
            user.referredBy = referredBy.lowercased()
        }
        if let lastAt = lastAt {
            user.lastAt = lastAt
        }
        if let status = status {
            user.status = status.lowercased()
        }
        if let score = score {
            user.score = score
        }
        if let refId = refId {
            user.refId = refId
        }
        if let pregnancyStatus = pregnancyStatus {
            user.pregnancyStatus = pregnancyStatus
        }
        if let deliveryDate = deliveryDate {
            user.deliveryDate = deliveryDate
        }
        if let military = military {
            user.military = military
        }
        if let enrolledOn = enrolledOn {
            user.enrolledOn = enrolledOn
        }
        if let unenrolledDate = unenrolledDate {
            user.unenrolledDate = unenrolledDate
        }
        if let homeless = homeless {
            user.homeless = homeless
        }
        if let lastContact = lastContact {
            user.lastContact = lastContact
        }
        if let meta = meta {
            user.meta = meta
        }
        return user
    }
    
    func hasPhone() -> Bool {
        return self.phone != nil
    }
    
    func hasSchool() -> Bool {
        return self.school != nil
    }
    
    func hasPet() -> Bool {
        return self.pet != nil
    }
}


struct MembersCreateInput: Content {
    static var param = "memberID"
    //    var email:      String? ///need to update auth as well
    var orgID:              String
    var email:              String
    var firstName:          String
    var middleName:         String?
    var lastName:           String
    var type:               String
    var roles:             [String]
    /// Date format: yyyy-MM-dd
    var dob:                String
    var gender:             String?
    var ethnicity:          String?
    var sexualIdentity:     String?
    var genderIdentity:     String?
    var pronouns:           String?
    var lang:               String?
    var referredBy:         String?
    var lastAt:             String?
    var status:             String?
    var score:              String?
    var phone:              String?
    var refId:              String?
    var pregnancyStatus:    String?
    /// Date format: yyyy-MM-dd
    var deliveryDate:       String?
    var school:             SchoolInput?
    var pet:                AnimalInput?
    var military:           String?
    var enrolledOn:         String?
    var unenrolledDate:   	String?
    var homeless:           Bool?
    var lastContact:        String?
    var memberStatus:       MemberStatusInput?
    var meta:               MetaData?
    var tags:              [TagInput]?
    
    
    var allTags: [Tag] {
        tags?.compactMap({$0.tag()}) ?? []
    }
    
    func cleanUp() -> MembersCreateInput {
        var input = self
        input.email = self.email.lowercased()
        input.firstName = self.firstName.lowercased()
        input.lastName = self.lastName.lowercased()
        input.middleName = self.middleName?.lowercased()
        input.phone = self.phone?.stripPhone()
        input.gender = self.gender?.lowercased()
        input.ethnicity = self.ethnicity?.lowercased()
        input.sexualIdentity = self.sexualIdentity?.lowercased()
        input.genderIdentity = self.genderIdentity?.lowercased()
        input.pronouns = self.pronouns?.lowercased()
        input.lang = self.lang?.lowercased()
        input.referredBy = self.referredBy?.lowercased()
        input.refId = self.refId?.lowercased()
        input.homeless = self.homeless
        input.enrolledOn = self.enrolledOn
        input.unenrolledDate = self.unenrolledDate
        input.lastContact = self.lastContact
        return input
    }
    
    
    func returnUpdatedModel(user:Member) -> Member {
        user.firstName  = firstName
        user.lastName   = lastName
        user.roles      = roles
        user.dob        = dob
        
        if let mn = middleName {
            user.middleName = mn
        }
        
        return user
    }
    
    func member() -> Member {
        return Member(
            email:              self.email,
            firstName:          self.firstName,
            middleName:         self.middleName,
            lastName:           self.lastName,
            type:               self.type,
            roles:              self.roles,
            dob:                self.dob,
            color:              profileColor(),
            gender:             self.gender,
            ethnicity:          self.ethnicity,
            sexualIdentity:     self.sexualIdentity,
            genderIdentity:     self.genderIdentity,
            pronouns:           self.pronouns,
            lang:               self.lang,
            referredBy:         self.referredBy,
            lastAt:             self.lastAt,
            status:             self.status,
            score:              self.score,
            refId:              self.refId,
            pregnancyStatus:    self.pregnancyStatus,
            deliveryDate:       self.deliveryDate,
            military:           self.military,
            enrolledOn:         self.enrolledOn,
            unenrolledDate:     self.unenrolledDate,
            lastContact:        self.lastContact,
            homeless:           self.homeless,
            meta:               self.meta
        )
    }
    
    func hasSchool() -> Bool {
        return self.school != nil
    }
    
    func hasPet() -> Bool {
        return self.pet != nil
    }
}
