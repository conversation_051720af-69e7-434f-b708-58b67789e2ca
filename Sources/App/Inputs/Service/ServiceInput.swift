//
//  File.swift
//  
//
//  Created by <PERSON> on 2/4/23.
//

import Foundation
import Vapor
import Fluent

struct ServiceRuleInput: Content {
    var  title:      String?
    var  subtitle:   String?
    var  msg:        String?
    
    func returnUpdatedModel(rule:ServiceRule) -> ServiceRule {
        
        if let title = title {
            rule.title = title
        }
        if let subtitle = subtitle {
            rule.subtitle = subtitle
        }
        if let msg = msg {
            rule.msg = msg
        }
        
        return rule
    }
    
    func serviceRule() -> ServiceRule {
        return ServiceRule(title: self.title ?? "", msg: self.msg ?? "")
    }
}

struct ServiceCreateInput: Content {
    var orgID:          String?
    var name:           String?
    var desc:           String?
    var type:           String?
    var service:        String?
    var status:        String?
    
    var rules:        [ServiceRuleInput]?
            
    func returnUpdatedModel(service:Service) -> Service {
        
        if let name = name {
            service.name = name
        }
        if let desc = desc {
            service.desc = desc
        }
        if let type = type {
            service.type = type
        }
        
        return service
    }
    
    func serviceModel() -> Service {
        return Service(name: self.name ?? "", desc: desc ?? "", service: service ?? "", type: self.type ?? service ?? "", status: status ?? "accepting")
    }
}
