//
//  File.swift
//  
//
//  Created by <PERSON> on 2/5/23.
//

import Foundation
import Vapor
import Fluent

struct TimelineItemInput: Content {
    var status:     String
    var desc:       String
    var services:  [String]?
    var title:      String? = nil
    var memberId:   String? = nil
    var visible:	Bool? = nil
    var meta:	    MetaData? = nil
    
//    func model(cpID:String) -> TimelineItem {
//        return TimelineItem(carepackageID: cpID, status: status, desc: desc, title: title, memberId: UUID(uuidString: memberId ?? ""), visible: visible, meta: meta)
//    }
}

struct EndCarePackageInput: Content {
    var reason: String
    var msg:    String
    var creatorID: String
    
    func model() -> Reason {
        return Reason(reason: reason, msg: msg)
    }
}


struct ScheudlerUpdateInput: Content {
    let provider: String?
    let providerId: String?
    let orgId: String
    let serviceId: String
    let service: String
    let appointmentId: String
    
    func toJson() -> [String: String]? {
        do {
            let data = try JSONEnco<PERSON>().encode(self)
            let dictionary = try J<PERSON><PERSON>ecoder().decode([String: String].self, from: data)
            return dictionary
        } catch {
            print("Error converting struct to dictionary: \(error)")
            return nil
        }
    }
}

struct CarePackageItemInput: Content {
    var networkID: String?
    var creatorID: String? //user
    var creatorMemID: String? //member
    var type:      String
    var item: TimelineItemInput?
    var appointmentEpoc: Int?
    var rate: String?
    var scheduler: ScheudlerUpdateInput?
    
    func isAppointment() -> Bool {
        if let appointmentEpoc {
            return appointmentEpoc > 0
        } else {
            return false
        }
    }
    
    func formatted(status: String) -> String {
        if isAppointment() {
            return "appointment \(status)"
        } else  if status.lowercased() == "noshow" {
            return "no show"
        } else {
            return status
        }        
    }
}

struct DeleteNetworksInput: Content {
    var networks: [String]
}

struct CarePackageInput: Content {
    var orgID:      String?
    var title:      String?
    var status:     String?
    var type:       String?
    var startedAt:  String? //yyy-mm-dd
    var endedAt:    String? //yyy-mm-dd
    
    var creator:    String? //user or member since members can now book
    var reciever:   String? //member
    
    
    var item:       CarePackageItemInput?
    
    
    func returnUpdatedModel(package:CarePackage) -> CarePackage {
        
        if let title = title {
            package.title = title
        }
        
        if let status = status {
            package.status = status
            if status.lowercased() == "active" && package.status != "active" {
                package.startedAt = Date()
            }
        }
        
        if let type = type {
            package.type = type
        }
        
        if let startedAt = startedAt {
            package.startedAt = Date.yearMonthDayDateWithDashFromString(dateString: startedAt)
        }
        
        if let endedAt = endedAt {
            package.endedAt = Date.yearMonthDayDateWithDashFromString(dateString: endedAt)
        }
        
        
        return package
    }
    
    func package() -> CarePackage {
        return CarePackage(title: title ?? "", 
                           status: status ?? "",
                           type: type ?? "", 
                           startedAt: Date())
    }
}
