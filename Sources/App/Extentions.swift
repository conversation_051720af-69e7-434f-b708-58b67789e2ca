//
//  File.swift
//  
//
//  Created by <PERSON> on 1/29/23.
//

import Foundation
import Vapor

func profileColor() -> String {
    return ["B4D8D8","BAD8B4", "D8CDB4", "D8B4C4","B4CED8","C0B4D8", "C0B4D8"].randomElement() ?? "B4D8D8"
}

extension String {
    func stripPhone() -> String {
        var phone = self.replacingOccurrences(of: "(", with: "")
        phone = phone.replacingOccurrences(of: ")", with: "")
        phone = phone.replacingOccurrences(of: " ", with: "")
        phone = phone.replacingOccurrences(of: "-", with: "")
        phone = phone.trimmingCharacters(in: .whitespacesAndNewlines)
        return phone
    }

    /// Format phone number to E.164 format for SMS/international use
    func formatToE164() -> String? {
        // Remove all non-digit characters except +
        let cleaned = self.replacingOccurrences(of: "[^\\d+]", with: "", options: .regularExpression)

        // If it already starts with +, return as is (assuming it's already in E.164)
        if cleaned.hasPrefix("+") {
            return cleaned
        }

        // If it doesn't start with +, assume US number and add +1
        if cleaned.count == 10 {
            return "+1" + cleaned
        } else if cleaned.count == 11 && cleaned.hasPrefix("1") {
            return "+" + cleaned
        }

        // For other cases, return the cleaned number (may need manual formatting)
        return cleaned.isEmpty ? nil : cleaned
    }
    
    func sanitizeForCSV() -> String {
        var cleanText = self
        if cleanText.isEmpty {
            return ""
        } else if cleanText.contains(",") || cleanText.contains("\"") || cleanText.contains("\n") || cleanText.contains("(") || cleanText.contains(")") {
            cleanText = cleanText.replacingOccurrences(of: "\"", with: "\"\"") // Escape quotes
            cleanText = "\"\(cleanText)\"" // Enclose in quotes
            return cleanText
        } else {
            return cleanText
        }
        
    }
    
    func removingUnwantedCharacters() -> String {
        // Define the characters to be removed
        let unwantedCharacters = CharacterSet(charactersIn: "\n\r\t\u{00A0}") // Add more unwanted characters if necessary
        // Remove unwanted characters and trim whitespace
        return self.unicodeScalars.filter { !unwantedCharacters.contains($0) }.map { String($0) }.joined().trimmingCharacters(in: .whitespacesAndNewlines)
    }
    
    func cleanText() -> String {
        // Define characters to be replaced or removed
        let replacements: [String: String] = [
            "☒": "",            // Remove checkbox symbol
            "“": "\"",          // Replace smart quotes with straight quotes
            "”": "\"",
            "’": "'",
            "–": "-",
            ",": "",
            "\n,": "\\n",
            "\r,":"\\r",
            "\r":"\\r",
            "\n": "" // Replace en dash with hyphen
        ]
        
        var cleanedText = self
        
        for (specialChar, replacement) in replacements {
            cleanedText = cleanedText.replacingOccurrences(of: specialChar, with: replacement)
        }
        
        return cleanedText
    }
    
    func replacingSpecialCharacters(with replacement: String = "") -> String {
        let allowedCharacterSet = CharacterSet.alphanumerics
        return self.unicodeScalars.map { allowedCharacterSet.contains($0) ? String($0) : replacement }.joined()
    }
    
    func clean() -> String {
        return self.replacingOccurrences(of: "\n", with: "\\n").replacingOccurrences(of: ",", with: "")
    }
    
    static func trimStringToMaxCharacters(_ string: String, maxCharacters: Int) -> String {
        if string.count <= maxCharacters {
            return string
        } else {
            let trimmedString = string.prefix(maxCharacters)
            return String(trimmedString)
        }
    }
}

extension Date {
      
    
    /// Used for sending E-Perscribing sent at
    /// - Parameter date: Date
    /// - Returns: 2021-04-22T15:02:28
    static func sentTime(date: Date) -> String {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss"
        return dateFormatter.string(from: date)
    }
    
    static func createdAtString(date: Date?) -> String {
        guard let date = date else { return "" }
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss"
        return dateFormatter.string(from: date)
    }
    
    ///Date String Type: "2021-10-21"
    static func yearMonthDayDateWithDashFromDate(date: Date) -> String? {
        let formatter  = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        let dateString = formatter.string(from: date)
        return dateString
    }
    
    static func yearMonthDayString(date: Date?) -> String {
        //        2017-12-11",
        guard let dt = date else { return "" }
        let formatter = DateFormatter()
        formatter.locale = Locale.current
        formatter.dateFormat = "yyyy-MM-d"
        return formatter.string(from: dt)
    }
    
    static func yearMonthDayDateWithDashFromString(dateString: String) -> Date? {
        let formatter  = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        let date = formatter.date(from: dateString)
        return date
    }
    
    var startOfDay: Date {
          return Calendar.current.startOfDay(for: self)
      }

      var endOfDay: Date {
          var components = DateComponents()
          components.day = 1
          components.second = -1
          return Calendar.current.date(byAdding: components, to: startOfDay)!
      }

      var startOfMonth: Date {
          let components = Calendar.current.dateComponents([.year, .month], from: startOfDay)
          return Calendar.current.date(from: components)!
      }

      var endOfMonth: Date {
          var components = DateComponents()
          components.month = 1
          components.second = -1
          return Calendar.current.date(byAdding: components, to: startOfMonth)!
      }
}



func dateFromYearMonthDayString(date:String) -> Date? {
    let dateFormatter:DateFormatter = DateFormatter()
    dateFormatter.dateFormat = "yyyy-MM-dd"
    return dateFormatter.date(from: date)
}


extension DateFormatter {
    ///Mar 1, 2024 - 12:00 PM",
    static func taskDueFullDateWithTime(date: Date) -> String {
        let formatter = DateFormatter()
        formatter.locale = Locale.current
        formatter.dateFormat = "MMM d, yyyy - h:mm a"
        return formatter.string(from: date)
    }
    
    ///Mar 1, 2024",
    static func taskDueFullDate(date: Date) -> String {
        let formatter = DateFormatter()
        formatter.locale = Locale.current
        formatter.dateFormat = "MMM d, yyyy"
        return formatter.string(from: date)
    }
    
    static func dateFromMultipleFormats(fromString dateString: String) -> Date? {
        return DateFormatter().dateFromMultipleFormats(fromString: dateString)
    }
    
    func dateFromMultipleFormats(fromString dateString: String) -> Date? {
        let formats: [String] = [
            "yyyy-MM-dd HH:mm:ss.SSSSSZ",
            "yyyy-MM-dd'T'HH:mm:ssZZZZZ",
            "yyyy-MM-dd'T'HH:mm:ss",
            "yyyy-MM-dd",
            "MM/dd/yyyy",
            "yyyy-MM-dd",
            "yyyy-dd-MM",
            "yyyy-MM-d",
            "yyyy-d-MM"
        ]
        for format in formats {
            self.dateFormat = format
            if let date = self.date(from: dateString) {
                return date
            }
        }
        return nil
    }
}

extension Request {
    func ipAddress() -> String {
        return self.headers.forwarded.first?.for ?? self.headers["X-Forwarded-For"].first ?? self.remoteAddress?.description ?? ""
    }
}

extension Optional where Wrapped == String {
    var nonEmptyValue: String? {
        guard let value = self, !value.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            return nil
        }
        return value
    }
}


extension String {
    // Convert ISO date string to start and end epoch times
    func toEpochTimes(durationMinutes: Int) -> (start: Int, end: Int)? {
        let dateFormatter = ISO8601DateFormatter()
        
        guard let date = dateFormatter.date(from: self) else {
            return nil
        }
        
        let startEpoch = Int(date.timeIntervalSince1970)
        let endEpoch = startEpoch + (durationMinutes * 60)
        
        return (start: startEpoch, end: endEpoch)
    }
}

extension Date {
    // Convert Date to epoch time
    var epochTime: Int {
        return Int(self.timeIntervalSince1970)
    }
    
    // Calculate end epoch time based on duration
    func endEpochTime(durationMinutes: Int) -> Int {
        return self.epochTime + (durationMinutes * 60)
    }
}
