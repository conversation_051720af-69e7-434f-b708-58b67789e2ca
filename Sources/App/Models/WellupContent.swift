//
//  File.swift
//  
//
//  Created by <PERSON> on 3/7/24.
//

import Foundation
import Fluent
import Vapor

final class WellupContent: Model, Content, @unchecked Sendable {
    static let schema = "content"
    
    @ID var id: UUID?
    
    @OptionalField(key: "title")
    var title: String?
    
    @OptionalField(key: "desc")
    var desc: String?
    
    @OptionalField(key: "kind")
    var kind: String? //podcast article service
    
    //active | inactive
    @OptionalField(key: "state")
    var state: String?
        
    @Children(for: \.$content)
    var tags: [Tag] //medicare, medicate, diabetes
    
    @OptionalField(key: "ref") //url
    var ref: String?
    
    @OptionalField(key: "markdown") //
    var markdown: String?
        
    @Children(for: \.$content)
    var attachments: [Attachment]
    
    @Timestamp(key: "created_at", on: .create)
    var createdAt: Date?
    
    // When this Planet was last updated.
    @Timestamp(key: "updated_at", on: .update)
    var updatedAt: Date?
    
    init() { }
    
    init(id:           UUID? = nil,
         title:        String?,
         desc:         String?,
         kind:        String?,
         state:        String?,
         markdown:      String?,
         ref:          String?
    ) {
        self.id         = id
        self.title      = title
        self.desc       = desc
        self.kind       = kind
        self.state      = state
        self.markdown   = markdown
        self.ref        = ref
    }
}

struct WellupContentMigration: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(WellupContent.schema)
            .id()
            .field("title",         .string)
            .field("desc",          .string)
            .field("kind",         .string)
            .field("state",         .string)
            .field("markdown",       .string)
            .field("ref",           .string)                    
            .field("created_at",   .datetime)
            .field("updated_at",   .datetime)
            .create()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(WellupContent.schema).delete()
    }
}
