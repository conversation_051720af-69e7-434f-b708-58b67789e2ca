//
//  TaskDetail.swift
//
//
//  Created by <PERSON> on 11/29/23.
//

import Foundation
import Fluent
import Vapor

struct TaskDetailInput: Content {
    var title: String?
    var type: String?
    var kind: String?
    var meta: MetaData?
}

final class TaskDetail: Model, Content, @unchecked Sendable {
    static let schema = "task_details"
    
    @ID var id: UUID?
        
    @Field(key: "title")
    var title: String
    
    @Field(key: "type")
    var type: String
    
    @Field(key: "kind")
    var kind: String
    
    @OptionalField(key: "meta")
    var meta: MetaData?
    
    @OptionalParent(key: "task_id")
    var task: TaskModel?
        
    @Timestamp(key: "created_at", on: .create)
    var createdAt: Date?
    
    // When this Planet was last updated.
    @Timestamp(key: "updated_at", on: .update)
    var updatedAt: Date?
    
    init() { }
    
    init(id:      UUID?    = nil,
         title:   String,
         type:    String,
         kind:    String,
         meta:    MetaData?
         ) {
        self.id         = id
        self.title      = title
        self.type       = type
        self.kind       = kind
        self.meta       = meta
    }
}

struct TaskDetailMigration: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(TaskDetail.schema)
            .id()
            .field("title",     .string, .required)
            .field("type",      .string, .required)
            .field("kind",      .string, .required)
            .field("meta",      .json)
            .field("task_id",      .uuid,   .references(TaskModel.schema,   "id", onDelete: .cascade))
            .field("created_at", .datetime)
            .field("updated_at", .datetime)
            .create()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(TaskDetail.schema).delete()
    }
}
