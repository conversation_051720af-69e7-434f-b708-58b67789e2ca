//
//  File.swift
//  
//
//  Created by <PERSON> on 1/28/23.
//
import Foundation
import Fluent
import Vapor

final class Address: Model, Content, @unchecked Sendable {
    static let schema = "address"
    
    @ID var id: UUID?
        
    @Field(key: "street")
    var street: String
    
    @OptionalField(key: "street2")
    var street2: String?
    
    @Field(key: "city")
    var city: String
    
    @Field(key: "state")
    var state: String
    
    @Field(key: "zip")
    var zip: String
    
    @Field(key: "country")
    var country: String
    
    @Field(key: "county")
    var county: String
    
    ///main | mobile | housing
    @Field(key: "kind")
    var kind: String
    
    @OptionalField(key: "lat")
    var lat: String?
    
    @OptionalField(key: "lon")
    var lon: String?
    
    @OptionalField(key: "note")
    var note: String?
    
        
    @OptionalParent(key: "org_id")
    var org: Organization?
    
    @OptionalParent(key: "household_id")
    var household: Household?
    
    @OptionalParent(key: "member_id")
    var member: Member?
    
    @OptionalParent(key: "team_id")
    var team: Team?
    
    @OptionalParent(key: "school_id")
    var school: School?
    
    @OptionalParent(key: "network_id")
    var network: Network?
    
    @OptionalParent(key: "policy_id")
    var policy: InsurancePolicy?
    
    @OptionalParent(key: "task_id")
    public var location: TaskModel?    
    
    @OptionalParent(key: "member_status_id")
    public var memberStatus: MemberStatus?
    
    @Timestamp(key: "created_at", on: .create)
    var createdAt: Date?
    
    // When this Planet was last updated.
    @Timestamp(key: "updated_at", on: .update)
    var updatedAt: Date?
    
    init() { }
    
    init(id:        UUID?   = nil,
         street:    String,
         street2:   String? = nil,
         city:      String,
         state:     String,
         zip:       String,
         country:   String,
         county:    String,
         kind:      String,
         lat:       String? = nil,
         lon:       String? = nil,
         note:      String? = nil
    ) {
        self.id       = id
        self.street   = street
        self.street2  = street2
        self.city     = city
        self.state    = state
        self.zip      = zip
        self.country  = country
        self.county   = county
        self.kind     = kind
        self.lat      = lat
        self.lon      = lon
        self.note     = note
    }
    
    func fullAddress() -> String {
        return "\(street) \(city) \(state) \(zip)"
    }
}

struct AddressMigration: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(Address.schema)
            .id()
            .field("street",     .string, .required)
            .field("street2",    .string)
            .field("city",       .string, .required)
            .field("state",      .string, .required)
            .field("zip",        .string, .required)
            .field("country",    .string, .required)
            .field("county",     .string, .required)
            .field("kind",       .string, .required)
            .field("lat",        .string)
            .field("lon",        .string)
            .field("note",       .string)
        
            .field("network_id",   .uuid, .references(Network.schema,    "id", onDelete: .cascade))
            .field("org_id",       .uuid, .references(Organization.schema,    "id", onDelete: .cascade))
            .field("school_id",    .uuid, .references(School.schema,    "id", onDelete: .cascade))
            .field("team_id",      .uuid, .references(Team.schema,      "id", onDelete: .cascade))
            .field("member_id",    .uuid, .references(Member.schema,    "id", onDelete: .cascade))
            .field("household_id", .uuid, .references(Household.schema, "id", onDelete: .cascade))
        
            .field("created_at", .datetime)
            .field("updated_at", .datetime)
            .create()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(Address.schema).delete()
    }
}


struct AddressUpdateMigration: AsyncMigration {
    
    func prepare(on database: Database) async throws {
        try await database.schema(Address.schema)
            .field("task_id", .uuid, .references(TaskModel.schema, "id", onDelete: .cascade))
            .update()
    }
    
    func revert(on database: Database) async throws {
        try await database.schema(Address.schema).update()
    }
}

struct AddressPolicyUpdateMigration: AsyncMigration {
    
    func prepare(on database: Database) async throws {
        try await database.schema(Address.schema)
            .field("policy_id", .uuid, .references(InsurancePolicy.schema, "id", onDelete: .cascade))
            .update()
    }
    
    func revert(on database: Database) async throws {
        try await database.schema(Address.schema).update()
    }
}

struct AddressMemberStatusUpdateMigration: AsyncMigration {
    
    func prepare(on database: Database) async throws {
        try await database.schema(Address.schema)
            .field("member_status_id", .uuid, .references(MemberStatus.schema, "id", onDelete: .cascade))
            .update()
    }
    
    func revert(on database: Database) async throws {
        try await database.schema(Address.schema).update()
    }
}



