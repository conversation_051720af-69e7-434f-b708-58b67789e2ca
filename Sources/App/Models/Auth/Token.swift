//
//  File.swift
//  
//
//  Created by <PERSON> on 1/30/23.
//

import Foundation
import Vapor
import Fluent

enum SessionSource: Int, Content {
  case signup
  case login
}

final class Token: Model, Content, @unchecked Sendable {
  static let schema = "tokens"
  
  @ID var id: UUID?
  
  @Parent(key: "user_id")
  var user: AuthUser
  
  @Field(key: "value")
  var value: String
  
  @Field(key: "source")
  var source: SessionSource
  
  @Field(key: "expires_at")
  var expiresAt: Date?
  
  @Timestamp(key: "created_at", on: .create)
  var createdAt: Date?
  
  init() {}
  
  init(id: UUID? = nil, userId: AuthUser.IDValue, token: String,
       source: SessionSource, expiresAt: Date?) {
    self.id = id
    self.$user.id = userId
    self.value = token
    self.source = source
    self.expiresAt = expiresAt
  }
    
    func isExpired() -> Bool {
        guard let expiryDate = self.expiresAt else { return  false }
        return expiryDate > Date()
    }
}


struct CreateTokensMigration: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        database.schema(Token.schema)
            .id()
            .field("user_id",    .uuid,     .references(AuthUser.schema, "id"))
            .field("value",      .string,   .required).unique(on: "value")
            .field("source",     .int,     .required)
            .field("created_at", .datetime, .required)
            .field("expires_at", .datetime)
            .create()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        database.schema(Token.schema).delete()
    }
}


//
extension Token: ModelTokenAuthenticatable {
  static let valueKey = \Token.$value
  static let userKey = \Token.$user

  var isValid: Bool {
    guard let expiryDate = expiresAt else {
      return true
    }

    return expiryDate > Date()
  }
}
