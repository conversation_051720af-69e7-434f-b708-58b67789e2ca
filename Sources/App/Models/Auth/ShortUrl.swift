//
//  ShortUrl.swift
//  
//
//  Created by <PERSON> on 6/29/25.
//

import Foundation
import Fluent
import Vapor

// MARK: - Short URL Model
final class ShortUrl: Model, Content, @unchecked Sendable {
    static let schema = "short_urls"
    
    @ID var id: UUID?
    
    @Field(key: "short_code")
    var shortCode: String
    
    @Field(key: "original_url")
    var originalUrl: String
    
    @Field(key: "reset_token")
    var resetToken: String
    
    @OptionalField(key: "user_id")
    var userId: UUID?
    
    @Field(key: "click_count")
    var clickCount: Int
    
    @Timestamp(key: "expires_at", on: .none)
    var expiresAt: Date?
    
    @Timestamp(key: "created_at", on: .create)
    var createdAt: Date?
    
    @Timestamp(key: "updated_at", on: .update)
    var updatedAt: Date?
    
    init() {}
    
    init(id: UUID? = nil, 
         shortCode: String, 
         originalUrl: String, 
         resetToken: String,
         userId: UUID? = nil,
         expirationMinutes: Int = 30) {
        self.id = id
        self.shortCode = shortCode
        self.originalUrl = originalUrl
        self.resetToken = resetToken
        self.userId = userId
        self.clickCount = 0
        self.expiresAt = Calendar.current.date(byAdding: .minute, value: expirationMinutes, to: Date())
    }
}

// MARK: - Short URL Extensions
extension ShortUrl {
    /// Generate a random short code (6 characters, URL-safe)
    static func generateShortCode() -> String {
        let characters = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
        return String((0..<6).map { _ in characters.randomElement()! })
    }
    
    /// Check if the short URL is still valid (not expired)
    func isValid() -> Bool {
        guard let expiresAt = expiresAt else { return false }
        return expiresAt > Date()
    }
    
    /// Increment click count for analytics
    func recordClick(on database: Database) async throws {
        self.clickCount += 1
        try await self.save(on: database)
    }
    
    /// Create a short URL for password reset
    static func createForPasswordReset(
        resetToken: String,
        userId: UUID?,
        expirationMinutes: Int = 30,
        on database: Database
    ) async throws -> ShortUrl {
        var shortCode: String
        var attempts = 0
        let maxAttempts = 10
        
        // Ensure unique short code
        repeat {
            shortCode = generateShortCode()
            attempts += 1
            
            if attempts > maxAttempts {
                throw Abort(.internalServerError, reason: "Unable to generate unique short code")
            }
            
            let existing = try await ShortUrl.query(on: database)
                .filter(\.$shortCode == shortCode)
                .first()
            
            if existing == nil {
                break
            }
        } while attempts <= maxAttempts
        
        let originalUrl = "/password-reset/verify?token=\(resetToken)"
        
        let shortUrl = ShortUrl(
            shortCode: shortCode,
            originalUrl: originalUrl,
            resetToken: resetToken,
            userId: userId,
            expirationMinutes: expirationMinutes
        )
        
        try await shortUrl.save(on: database)
        return shortUrl
    }
}

// MARK: - Migration
struct CreateShortUrlMigration: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        database.schema(ShortUrl.schema)
            .id()
            .field("short_code", .string, .required)
            .field("original_url", .string, .required)
            .field("reset_token", .string, .required)
            .field("user_id", .uuid)
            .field("click_count", .int, .required)
            .field("expires_at", .datetime)
            .field("created_at", .datetime, .required)
            .field("updated_at", .datetime, .required)
            .unique(on: "short_code")
            .create()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        database.schema(ShortUrl.schema).delete()
    }
}
