//
//  File.swift
//  
//
//  Created by <PERSON> on 1/29/23.
//

import Foundation
import Fluent
import Vapor

final class Team: Model, Content, @unchecked Sendable {
    static let schema = "teams"
    
    @ID var id: UUID?
    
    @Field(key: "name")
    var name: String
    
    @Children(for: \.$team)
    var address: [Address]
    
    @Siblings(through: UserTeams.self, from: \.$team, to: \.$user)
    public var leads: [User]
    
    @Siblings(through: UserTeams.self, from: \.$team, to: \.$user)
    public var navigators: [User]
    
    
    @Siblings(through: HouseholdTeams.self, from: \.$team, to: \.$household)
    public var households: [Household]
    
    @OptionalParent(key: "org_id")
    var org: Organization?
    
    @Timestamp(key: "created_at", on: .create)
    var createdAt: Date?
    
    // When this Planet was last updated.
    @Timestamp(key: "updated_at", on: .update)
    var updatedAt: Date?
    
    init() { }
    
    init(id:        UUID?    = nil,
         name:      String
         ) {
        self.id      = id
        self.name    = name
    }
}

struct TeamMigration: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(Team.schema)
            .id()
            .field("name",       .string, .required)
            .field("org_id",     .uuid,   .references(Organization.schema,   "id", onDelete: .cascade))
            .field("created_at", .datetime)
            .field("updated_at", .datetime)
            .create()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(Team.schema).delete()
    }
}
