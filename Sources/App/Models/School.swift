//
//  File.swift
//  
//
//  Created by <PERSON> on 1/29/23.
//
import Foundation
import Fluent
import Vapor

final class School: Model, Content, @unchecked Sendable {
    static let schema = "schools"
    
    @ID var id: UUID?
    
    @Field(key: "name")
    var name: String
    
    @Children(for: \.$school)
    var address: [Address]
    
    @Field(key: "grade")
    var grade: String
        
    
    @OptionalParent(key: "member_id")
    var member: Member?
    
    @Timestamp(key: "created_at", on: .create)
    var createdAt: Date?
    
    // When this Planet was last updated.
    @Timestamp(key: "updated_at", on: .update)
    var updatedAt: Date?
    
    init() { }
    
    init(id:         UUID?    = nil,
         name:      String,
         grade:        String
         ) {
        self.id          = id
        self.name        = name
        self.grade       = grade
    }
}

struct SchoolMigration: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(School.schema)
            .id()
            .field("name",       .string, .required)
            .field("grade",      .string, .required)
            .field("member_id",  .uuid,   .references(Member.schema,   "id", onDelete: .cascade))
            .field("created_at", .datetime)
            .field("updated_at", .datetime)
            .create()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(School.schema).delete()
    }
}
