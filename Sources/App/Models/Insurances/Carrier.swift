//
//  File.swift
//  
//
//  Created by <PERSON> on 1/6/24.
//

import Foundation
import Fluent
import Vapor

final class Carrier: Model, Content, @unchecked Sendable {
    static let schema = "carriers"
    
    @ID var id: UUID?
    
    @Field(key: "name")
    public var name: String
    
    @Children(for: \.$carrier)
    var tags: [Tag]
    
    @OptionalField(key: "contact_number")
    var contactNumber: String?
    
    @OptionalField(key: "url")
    var url: String?
    
    @OptionalField(key: "address")
    var address: String?
    
    @Siblings(through: NetworkCarriers.self, from: \.$carrier, to: \.$network)
    public var networks: [Network]
    
    @Timestamp(key: "created_at", on: .create)
    var createdAt: Date?
    
    // When this Planet was last updated.
    @Timestamp(key: "updated_at", on: .update)
    var updatedAt: Date?
    
    init() { }
    
    init(id: UUID? = nil,
         name: String,
         contactNumber: String? = nil,
         url: String? = nil,
         address: String? = nil) {
        self.id          = id
        self.name = name
        self.contactNumber = contactNumber
        self.url = url
        self.address = address
    }
}

struct CarrierMigration: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(Carrier.schema)
            .id()
            .field("name", .string,   .required)
            .field("contact_number", .string)
            .field("url", .string)
            .field("address", .string)
        
            .field("created_at", .datetime)
            .field("updated_at", .datetime)
            .create()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(Carrier.schema).delete()
    }
}
