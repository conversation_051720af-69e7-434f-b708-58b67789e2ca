//
//  File.swift
//  
//
//  Created by <PERSON> on 1/6/24.
//

import Foundation
import Vapor
import Fluent

final class InsuranceCard: Model, Content, @unchecked Sendable {
    static let schema = "insurance_cards"

    @ID var id: UUID?
    
    @Field(key: "policyholder_name")
    var policyholderName: String

    @Field(key: "date_of_birth")
    var dateOfBirth: String

    @Field(key: "policyholder_id")
    var policyholderID: String

    @Field(key: "effective_date")
    var effectiveDate: String

    @OptionalField(key: "expiration_date")
    var expirationDate: String?
    
    @OptionalField(key: "group_number")
    var groupNumber: String?
    
    @OptionalParent(key: "policy_id")
    var policy: InsurancePolicy?
    
    @Children(for: \.$insuranceCard)
    var attachments: [Attachment]

    @Timestamp(key: "created_at", on: .create)
    var createdAt: Date?
    
    // When this Planet was last updated.
    @Timestamp(key: "updated_at", on: .update)
    var updatedAt: Date?

    init() { }

    init(id: UUID? = nil, policyholderName: String, dateOfBirth: String, policyholderID: String, effectiveDate: String, expirationDate: String? = nil, groupNumber: String? = nil) {
        self.id = id
        self.policyholderName = policyholderName
        self.dateOfBirth = dateOfBirth
        self.policyholderID = policyholderID
        self.effectiveDate = effectiveDate
        self.expirationDate = expirationDate
        self.groupNumber = groupNumber
    }
}


struct CreateInsuranceCard: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(InsuranceCard.schema)
            .id()
            .field("policyholder_name", .string, .required)
            .field("date_of_birth", .string, .required)
            .field("policyholder_id", .string, .required)
            .field("effective_date", .string, .required)
            .field("expiration_date", .string)
            .field("group_number", .string)
            .field("policy_id",  .uuid,   .references(InsurancePolicy.schema,   "id", onDelete: .cascade))
            // Add additional fields for insurance card details
            .field("created_at", .datetime)
            .field("updated_at", .datetime)
            .create()
    }

    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(InsuranceCard.schema).delete()
    }
}
