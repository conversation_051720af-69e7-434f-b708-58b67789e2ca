//
//  File.swift
//  
//
//  Created by <PERSON> on 1/6/24.
//

import Foundation
import Fluent
import Vapor

final class InsurancePolicy: Model, Content, @unchecked Sendable {
    static let schema = "insurance_policy"
        
    @ID var id: UUID?
    
    @Field(key: "policy_number")
    var policyNumber: String
    
    @Field(key: "start_date")
    var startDate: String
    
    @OptionalField(key: "end_date")
    var endDate: String?
    
    @Field(key: "user_id")
    var userId: UUID
    
    @Field(key: "plan_type")
    var planType: String
    
    @Field(key: "plan_name")
    var planName: String
    
    @Children(for: \.$policy)
    var address: [Address]
    
    @Children(for: \.$policy)
    var phones: [PhoneNumber]
    
    @Children(for: \.$policy)
    var cards: [InsuranceCard]
        
    @Children(for: \.$policy)
    var plans: [InsurancePlan]
        
    @Field(key: "carrier_id")
    var carrierId: UUID
    
    @Timestamp(key: "created_at", on: .create)
    var createdAt: Date?
    
    // When this Planet was last updated.
    @Timestamp(key: "updated_at", on: .update)
    var updatedAt: Date?
    
    init() { }
    
    init(id: UUID? = nil,
         policyNumber: String,
         startDate: String,
         endDate: String? = nil,
         userId: UUID,
         planType: String,
         planName: String,
         carrierId: UUID) {
        self.id          = id
        self.policyNumber =  policyNumber
        self.startDate =  startDate
        self.endDate =  endDate
        self.userId =  userId
        self.planType =  planType
        self.planName =  planName
        self.carrierId = carrierId
    }
}

struct InsurancePolicyMigration: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(InsurancePolicy.schema)
            .id()
            .field("policy_number", .string, .required)
            .field("start_date", .string, .required)
            .field("end_date", .string)
            .field("user_id", .uuid, .required)
            .field("plan_type", .string, .required)
            .field("plan_name", .string, .required)
            .field("carrier_id", .uuid, .required)
        
            .field("created_at", .datetime)
            .field("updated_at", .datetime)
            .create()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(InsurancePolicy.schema).delete()
    }
}
