//
//  File.swift
//  
//
//  Created by <PERSON> on 1/21/24.
//

import Foundation
import Fluent
import Vapor

final class InsurancePlan: Model, @unchecked Sendable {
    
    static let schema = "insurance_plans"

    @ID var id: UUID?

    @Field(key: "name")
    var name: String

    @Field(key: "number")
    var number: String

    @Field(key: "entitlement_date")
    var entitlementDate: String
    
    @OptionalField(key: "end_date")
    var endDate: String?
    
    @OptionalField(key: "group_number")
    var groupNumber: String?
    
    @OptionalField(key: "coverage")
    var coverage: String? //individual plan has one member, or just one person covered by the plan. Family plans cover two or more members

    @Field(key: "type")
    var type: String //part a part b etc...
     
    @Field(key: "issuer")
    var issuer: String

    @OptionalField(key: "contact")
    var contact: String? //policy holder
    
    @Field(key: "enrolled")
    var enrolled: Bool
    
    @OptionalParent(key: "policy_id")
    var policy: InsurancePolicy?
    
    @Timestamp(key: "created_at", on: .create)
    var createdAt: Date?
    
    // When this Planet was last updated.
    @Timestamp(key: "updated_at", on: .update)
    var updatedAt: Date?
    
    init() {}
    
    init(id: UUID? = nil, name: String, number: String, entitlementDate: String, endDate: String? = nil, groupNumber: String? = nil, type: String, issuer: String, contact: String? = nil, enrolled: Bool, coverage: String? = nil) {
        self.id = id
        self.name = name
        self.number = number
        self.entitlementDate = entitlementDate
        self.endDate = endDate
        self.groupNumber = groupNumber
        self.type = type
        self.issuer = issuer
        self.contact = contact
        self.enrolled = enrolled
        self.coverage = coverage
    }
}


struct InsurancePlanMigration: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(InsurancePlan.schema)
            .id()
            .field("name", .string, .required)
            .field("number", .string, .required)
            .field("entitlement_date", .string, .required)
            .field("end_date", .string)
            .field("group_number", .string)
            .field("coverage", .string)
            .field("type", .string, .required)
            .field("issuer", .string, .required)
            .field("contact", .string)
            .field("enrolled", .bool, .required)
            
            .field("policy_id",    .uuid,   .references(InsurancePolicy.schema, "id", onDelete: .cascade))
            .field("created_at", .datetime)
            .field("updated_at", .datetime)
            .create()
    }

    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(InsurancePlan.schema).delete()
    }
}

