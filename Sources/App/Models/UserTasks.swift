//
//  File.swift
//  
//
//  Created by <PERSON> on 11/29/23.
//

import Foundation
import Fluent
import Vapor


final class UserTasks: Model, @unchecked Sendable {
    static let schema = "user_tasks"
    
    @ID(key: .id)
    var id: UUID?
    
    @Parent(key: "user_id")
    var user: User
    
    @Parent(key: "task_id")
    var task: TaskModel
    
    @Timestamp(key: "created_at", on: .create)
    var createdAt: Date?
    
    // When this Planet was last updated.
    @Timestamp(key: "updated_at", on: .update)
    var updatedAt: Date?
    
    init() { }
    
    init(id: UUID? = nil, user: User, team: Team) throws {
        self.id = id
        self.$user.id = try user.requireID()
        self.$task.id = try team.requireID()
    }
}


struct UserTasksMigration: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(UserTasks.schema)
            .id()
            .field("user_id", .uuid, .required)
            .field("task_id", .uuid, .required)

            .field("created_at", .datetime)
            .field("updated_at", .datetime)
            .create()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(UserTasks.schema).delete()
    }
}
