//
//  File.swift
//  
//
//  Created by <PERSON> on 1/15/24.
//

import Foundation
import Fluent
import Vapor


final class ItemAppointments: Model, @unchecked Sendable {
    static let schema = "item_appointments"
    
    @ID var id: UUID?
    
    @Parent(key: "appt_id")
    var appointment: Appointment
    
    @Parent(key: "cp_id")
    var item: CarePackageItem
    
    @Timestamp(key: "created_at", on: .create)
    var createdAt: Date?
    
    // When this Planet was last updated.
    @Timestamp(key: "updated_at", on: .update)
    var updatedAt: Date?
    
    init() { }
    
    init(id: UUID? = nil, appointment: Appointment, item: CarePackageItem) throws {
        self.id = id
        self.$appointment.id = try appointment.requireID()
        self.$item.id = try item.requireID()
    }
}


struct ItemAppointmentsMigration: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(ItemAppointments.schema)
            .id()
            .field("appt_id", .uuid, .required)
            .field("cp_id", .uuid, .required)

            .field("created_at", .datetime)
            .field("updated_at", .datetime)
            .create()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(ItemAppointments.schema).delete()
    }
}
