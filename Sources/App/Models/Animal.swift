//
//  File.swift
//  
//
//  Created by <PERSON> on 1/29/23.
//
import Foundation
import Fluent
import Vapor

final class Animal: Model, Content, @unchecked Sendable {
    static let schema = "animals"
    
    @ID var id: UUID?
    
    @Field(key: "name")
    var name: String
    
    ////dog / cat / chiecken / canine
    @Field(key: "type")
    var type: String
    
    ////King corsoue
    @OptionalField(key: "kind")
    var kind: String?
    
    @OptionalField(key: "age")
    var age: String?
    
    @Children(for: \.$animal)
    var attachments: [Attachment]
    
    @OptionalParent(key: "household_id")
    var household: Household?
    
    @Timestamp(key: "created_at", on: .create)
    var createdAt: Date?
    
    // When this Planet was last updated.
    @Timestamp(key: "updated_at", on: .update)
    var updatedAt: Date?
    
    init() { }
    
    init(id:       UUID?    = nil,
         name:     String,
         type:     String,
         kind:     String? = nil,
         age:      String? = nil
         ) {
        self.id          = id
        self.name        = name
        self.type        = type
        self.kind        = kind
        self.age         = age
    }
}

struct AnimalMigration: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(Animal.schema)
            .id()
            .field("name",       .string, .required)
            .field("type",       .string, .required)
            .field("kind",       .string)
            .field("age",        .string)
            .field("household_id",      .uuid,   .references(Household.schema,   "id", onDelete: .cascade))
        
            .field("created_at", .datetime)
            .field("updated_at", .datetime)
            .create()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(Animal.schema).delete()
    }
}

