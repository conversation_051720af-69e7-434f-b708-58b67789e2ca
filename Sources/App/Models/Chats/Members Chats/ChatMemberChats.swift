//
//  File.swift
//  
//
//  Created by <PERSON> on 3/30/24.
//

import Foundation

import Foundation
import Fluent
import Vapor

// pivot model.
final class ChatMemberChats: Model, @unchecked Sendable {
    static let schema = "chat_member_chats"
    
    @ID var id: UUID?
    
    @Parent(key: "chat_member_id")
    var chatMember: ChatMember
    
    @Parent(key: "chat_id")
    var chat: MemberChat
    
    @Timestamp(key: "created_at", on: .create)
    var createdAt: Date?
    
    // When this Planet was last updated.
    @Timestamp(key: "updated_at", on: .update)
    var updatedAt: Date?
    
    init() { }
    
    init(id: UUID? = nil, chatMember: ChatMember, chat: MemberChat) throws {
        self.id = id
        self.$chatMember.id = try chatMember.requireID()
        self.$chat.id = try chat.requireID()
    }
}


struct ChatMemberChatsMigration: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
//        print(database)
        return database.schema(ChatMemberChats.schema)
            .id()
            .field("chat_member_id", .uuid, .required)
            .field("chat_id", .uuid, .required)

            .field("created_at", .datetime)
            .field("updated_at", .datetime)
            .create()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(ChatMemberChats.schema).delete()
    }
}
