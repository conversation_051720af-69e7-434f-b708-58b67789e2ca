//
//  MemberChat.swift
//
//
//  Created by <PERSON> on 3/30/24.
//

import Foundation

import Foundation
import Fluent
import Vapor

final class MemberChat: Model, Content, @unchecked Sendable {
    static let schema = "member_chat"
    
    @ID var id: UUID?
        
    @Field(key: "creatorID")
    var creatorID: UUID
    
    @Field(key: "chatServiceSid")
    var chatServiceSid: String
    
    @Field(key: "conversationSid")
    var conversationSid: String
    
    @Field(key: "conversationFriendlyName")
    var conversationFriendlyName: String
    
    @Field(key: "conversationState")
    var conversationState: String
    
    @OptionalField(key: "latestMessage")
    var latestMessage: String?
    
    @OptionalParent(key: "sender_id")
    public var latestMessageSender: ChatMember?
        
    @OptionalParent(key: "chat_member_id")
    public var creator: ChatMember?
    
        
    @Siblings(through: ChatMemberChats.self, from: \.$chat, to: \.$chatMember)
    public var participants: [ChatMember]
        
    @OptionalParent(key: "org_id")
    var org: Organization?
    
    @OptionalField(key: "pushEnabled")
    var pushEnabled: Bool?
    
    @Timestamp(key: "created_at", on: .create)
    var createdAt: Date?
    
    // When this Planet was last updated.
    @Timestamp(key: "updated_at", on: .update)
    var updatedAt: Date?
    
    init() { }
    
    init(id:UUID?    = nil,
         creatorID:                 UUID,
         chatServiceSid:            String,
         conversationSid:           String,
         conversationFriendlyName:  String,
         conversationState:         String,
         pushEnabled:               Bool,
         latestMessage:             String?)
    {
        self.id      = id
        self.creatorID  = creatorID
        self.chatServiceSid = chatServiceSid
        self.conversationSid    = conversationSid
        self.conversationFriendlyName   = conversationFriendlyName
        self.conversationState  = conversationState
        self.latestMessage  = latestMessage
        self.pushEnabled  = pushEnabled
    }
}

struct MemberChatMigration: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(MemberChat.schema)
            .id()
            .field("creatorID",                .uuid, .required)
            .field("chatServiceSid",           .string, .required)
            .field("conversationSid",          .string, .required)
            .field("conversationFriendlyName", .string, .required)
            .field("conversationState",        .string, .required)
            .field("latestMessage",            .string)
            .field("pushEnabled",              .bool)
        
            .field("org_id",           .uuid,   .references(Organization.schema, "id", onDelete: .cascade))
            .field("chat_member_id",   .uuid,   .references(ChatMember.schema,   "id", onDelete: .cascade))
            .field("sender_id",        .uuid,   .references(ChatMember.schema,   "id", onDelete: .cascade))
        
            .field("created_at", .datetime)
            .field("updated_at", .datetime)
            .create()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(MemberChat.schema).delete()
    }
}


