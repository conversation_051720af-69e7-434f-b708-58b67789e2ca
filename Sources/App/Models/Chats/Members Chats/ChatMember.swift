//
//  File.swift
//  
//
//  Created by <PERSON> on 3/30/24.
//

import Foundation
import Fluent
import Vapor

final class ChatMember: Model, Content, @unchecked Sendable {
    static let schema = "chat_member"
    
    @ID var id: UUID?
        
    @Field(key: "person_id")
    var personId: UUID //UUID of navigator or member
    
    @Field(key: "person_name")
    var personName: String
    
    @Field(key: "profile")
    var profile: String
    
    @Field(key: "role")
    var role: String
    
    @OptionalChild(for: \.$creator)
    var chatCreator: MemberChat?
    
    @OptionalChild(for: \.$latestMessageSender)
    var chatSender: MemberChat?
        
    @Siblings(through: ChatMemberChats.self, from: \.$chatMember, to: \.$chat)
    public var chats: [MemberChat]
    
    @Timestamp(key: "created_at", on: .create)
    var createdAt: Date?
    
    // When this Planet was last updated.
    @Timestamp(key: "updated_at", on: .update)
    var updatedAt: Date?
    
    init() { }
    
    init(id:UUID? = nil,
         personId:      UUID,
         personName:    String,
         profile: String,
         role:          String) {
        self.id      = id
        self.personId  = personId
        self.personName = personName
        self.profile = profile
        self.role = role
    }
}


struct ChatMemberMigration: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(ChatMember.schema)
            .id()
            .field("person_id",         .uuid, .required)
            .field("person_name",       .string, .required)
            .field("role",              .string, .required)
            .field("profile",           .string, .required)
        
            .field("created_at", .datetime)
            .field("updated_at", .datetime)
            .create()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(ChatMember.schema).delete()
    }
}

extension ChatMember {
    static func from(navigator: User) -> ChatMember {
        return ChatMember(personId: navigator.id!, personName: "\(navigator.firstName) \(navigator.lastName)", profile:navigator.profile ?? "", role: "navigator")
    }
    
    static func from(member: Member) -> ChatMember {
        return ChatMember(personId: member.id!, personName: "\(member.firstName) \(member.lastName)", profile:member.profileURL(), role: "member")
    }
    
    func memberInfo() -> (fullName: String, identity: String) {
        return (personName, personName)
    }
}
