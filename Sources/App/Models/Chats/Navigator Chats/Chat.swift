//
//  File.swift
//  
//
//  Created by <PERSON> on 3/29/23.
//

import Foundation
import Fluent
import Vapor

final class Chat: Model, Content, @unchecked Sendable {
    static let schema = "chat"
    
    @ID var id: UUID?
        
    @Field(key: "creatorID")
    var creatorID: String
    
    @Field(key: "chatServiceSid")
    var chatServiceSid: String
    
    @Field(key: "conversationSid")
    var conversationSid: String
    
    @Field(key: "conversationFriendlyName")
    var conversationFriendlyName: String
    
    @Field(key: "conversationState")
    var conversationState: String
    
    @OptionalField(key: "latestMessage")
    var latestMessage: String?
    
    @OptionalParent(key: "sender_id")
    public var latestMessageSender: User?
        
    @OptionalParent(key: "user_id")
    public var creator: User?
    
        
    @Siblings(through: UserChats.self, from: \.$chat, to: \.$user)
    public var participants: [User]
        
    @OptionalParent(key: "org_id")
    var org: Organization?
    
    @OptionalField(key: "pushEnabled")
    var pushEnabled: Bool?
    
    @OptionalField(key: "title")
    var title: String?
    
    @Timestamp(key: "created_at", on: .create)
    var createdAt: Date?
    
    // When this Planet was last updated.
    @Timestamp(key: "updated_at", on: .update)
    var updatedAt: Date?
    
    init() { }
    
    init(id:UUID?    = nil,
         creatorID:                 String,
         chatServiceSid:            String,
         conversationSid:           String,
         conversationFriendlyName:  String,
         conversationState:         String,
         pushEnabled:               Bool,
         latestMessage:             String?,
         title:                     String? = nil)
    {
        self.id      = id
        self.creatorID  = creatorID
        self.chatServiceSid = chatServiceSid
        self.conversationSid    = conversationSid
        self.conversationFriendlyName   = conversationFriendlyName
        self.conversationState  = conversationState
        self.latestMessage  = latestMessage
        self.pushEnabled  = pushEnabled
        self.title  = title
    }
}

struct ChatMigration: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(Chat.schema)
            .id()
            .field("creatorID",                .string, .required)
            .field("chatServiceSid",           .string, .required)
            .field("conversationSid",          .string, .required)
            .field("conversationFriendlyName", .string, .required)
            .field("conversationState",        .string, .required)
            .field("latestMessage",            .string)
            .field("pushEnabled",              .bool)
        
            .field("org_id",     .uuid,   .references(Organization.schema,   "id", onDelete: .cascade))
            .field("user_id",    .uuid,   .references(User.schema,   "id", onDelete: .cascade))
            .field("sender_id",    .uuid, .references(User.schema,   "id", onDelete: .cascade))
        
            .field("created_at", .datetime)
            .field("updated_at", .datetime)
            .create()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(Chat.schema).delete()
    }
}

struct ChatUpdateMigration: AsyncMigration {
    
    func prepare(on database: Database) async throws {
        try await database.schema(Chat.schema)
            .field("title",  .string)
            .update()
    }
    
    func revert(on database: Database) async throws {
        try await database.schema(Chat.schema).update()
    }
}
