//
//  File.swift
//  
//
//  Created by <PERSON> on 3/29/23.
//

import Foundation
import Fluent
import Vapor

// pivot model.
final class UserChats: Model, @unchecked Sendable {
    static let schema = "user_chats"
    
    @ID var id: UUID?
    
    @Parent(key: "user_id")
    var user: User
    
    @Parent(key: "chat_id")
    var chat: Chat
    
    @Timestamp(key: "created_at", on: .create)
    var createdAt: Date?
    
    // When this Planet was last updated.
    @Timestamp(key: "updated_at", on: .update)
    var updatedAt: Date?
    
    init() { }
    
    init(id: UUID? = nil, user: User, chat: Chat) throws {
        self.id = id
        self.$user.id = try user.requireID()
        self.$chat.id = try chat.requireID()
    }
}


struct UserChatsMigration: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
//        print(database)
        return database.schema(UserChats.schema)
            .id()
            .field("user_id", .uuid, .required)
            .field("chat_id", .uuid, .required)
//            .unique(on: "user_id", "team_id")
            .field("created_at", .datetime)
            .field("updated_at", .datetime)
            .create()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(UserChats.schema).delete()
    }
}
