//
//  OrgTasksStatus.swift
//
//
//  Created by <PERSON> on 12/1/23.
//

import Foundation
import Fluent
import Vapor

final class OrgTaskStatus: Model, Content, @unchecked Sendable {
    static let schema = "org_task_status"
    
    @ID var id: UUID?
    
    @Field(key: "org_id")
    var orgID: UUID
    
    @Field(key: "status")
    var status: [String]
    
    @Field(key: "intialStatus")
    var intialStatus: String
        
    
    @Timestamp(key: "created_at", on: .create)
    var createdAt: Date?
    
    // When this Planet was last updated.
    @Timestamp(key: "updated_at", on: .update)
    var updatedAt: Date?
    
    init() { }
    
    init(id:         UUID?    = nil,
         orgID:        UUID,
         status:       [String],
         intialStatus: String
         ) {
        self.id          = id
        self.orgID  = orgID
        self.status = status
        self.intialStatus   = intialStatus
    }
}

struct OrgTaskStatusMigration: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(OrgTaskStatus.schema)
            .id()
            .field("org_id",        .uuid,   .required)
            .field("status",       .array(of: .string), .required)
            .field("intialStatus", .string,   .required)
            .field("created_at", .datetime)
            .field("updated_at", .datetime)
            .create()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(OrgTaskStatus.schema).delete()
    }
}
