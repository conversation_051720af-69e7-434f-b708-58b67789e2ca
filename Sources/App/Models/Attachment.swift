//
//  File.swift
//  
//
//  Created by <PERSON> on 1/28/23.
//
import Foundation
import Fluent
import Vapor

final class Attachment: Model, Content, @unchecked Sendable {
    static let schema = "attachments"
    
    @ID var id: UUID?
    
    @OptionalField(key: "name")
    var name: String?
    
    ///banner | visual | tile
    @Field(key: "kind")
    var kind: String
    
    ///"video" | "image"
    @Field(key: "type")
    var type: String
    
    @Field(key: "url")
    var url: String
    
    @OptionalParent(key: "animal_id")
    var animal: Animal?
    
    @OptionalParent(key: "household_id")
    var household: Household?
    
    @OptionalParent(key: "member_id")
    var member: Member?
    
    @OptionalParent(key: "note_id")
    var note: Note?
    
    @OptionalParent(key: "user_id")
    var user: User?
    
    @OptionalParent(key: "survey_id")
    var survey: Survey?
    
    @OptionalParent(key: "org_id")
    var org: Organization?
    
    @OptionalParent(key: "task_id")
    var task: TaskModel?
    
    @OptionalParent(key: "insurance_card_id")
    var insuranceCard: InsuranceCard?
    
    @OptionalParent(key: "wellup_content")
    var content: WellupContent?
    
    ///Team commniications etc...
    @OptionalField(key: "category")
    var category: String?
    
    @OptionalField(key: "ref_id")
    var refID: String?    
    
    @Timestamp(key: "created_at", on: .create)
    var createdAt: Date?
    
    // When this Planet was last updated.
    @Timestamp(key: "updated_at", on: .update)
    var updatedAt: Date?
    
    init() { }
    
    init(id:         UUID? = nil,
         name:       String?,
         kind:       String,
         type:       String,
         url:        String,
         category:   String? = nil,
         refID:       String? = nil
    ) {
        self.id       = id
        self.name     = name
        self.kind     = kind
        self.type     = type
        self.url      = url
        self.category = category
        self.refID    = refID
    }
}

struct AttachmentMigration: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(Attachment.schema)
            .id()
            .field("name",         .string)
            .field("kind",         .string, .required)
            .field("type",         .string, .required)
            .field("url",          .string, .required)
        
            .field("org_id",       .uuid,   .references(Organization.schema,   "id", onDelete: .cascade))
            .field("survey_id",    .uuid,   .references(Survey.schema, "id", onDelete: .cascade))
            .field("user_id",      .uuid,   .references(User.schema,   "id", onDelete: .cascade))
            .field("note_id",      .uuid,   .references(Note.schema,   "id", onDelete: .cascade))
            .field("member_id",    .uuid,   .references(Member.schema, "id", onDelete: .cascade))
            .field("household_id", .uuid,   .references(Household.schema, "id", onDelete: .cascade))
            .field("animal_id",    .uuid,   .references(Animal.schema, "id", onDelete: .cascade))
        
            .field("category",     .string)
            .field("created_at",   .datetime)
            .field("updated_at",   .datetime)
            .create()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(Attachment.schema).delete()
    }
}



struct AttachmentUpdateMigration: AsyncMigration {
    
    func prepare(on database: Database) async throws {
        try await database.schema(Attachment.schema)
            .field("ref_id",  .string)
            .update()
    }
    
    func revert(on database: Database) async throws {
        try await database.schema(Attachment.schema).update()
    }
    
}

struct AttachmentUpdateTaskMigration: AsyncMigration {
    
    func prepare(on database: Database) async throws {
        try await database.schema(Attachment.schema)
            .field("task_id",    .uuid,   .references(TaskModel.schema, "id", onDelete: .cascade))
            .update()
    }
    
    func revert(on database: Database) async throws {
        try await database.schema(Attachment.schema).update()
    }
    
}

struct AttachmentUpdateInsuranceCardMigration: AsyncMigration {
    
    func prepare(on database: Database) async throws {
        try await database.schema(Attachment.schema)
            .field("insurance_card_id",    .uuid,   .references(InsuranceCard.schema, "id", onDelete: .cascade))
            .update()
    }
    
    func revert(on database: Database) async throws {
        try await database.schema(Attachment.schema).update()
    }
    
}


struct AttachmentUpdateWellupContentMigration: AsyncMigration {
    
    func prepare(on database: Database) async throws {
        try await database.schema(Attachment.schema)
            .field("wellup_content",    .uuid,   .references(WellupContent.schema, "id", onDelete: .cascade))
            .update()
    }
    
    func revert(on database: Database) async throws {
        try await database.schema(Attachment.schema).update()
    }
    
}
