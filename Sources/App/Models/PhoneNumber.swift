import Fluent
import Vapor

final class PhoneNumber: Model, Content, @unchecked Sendable {
    static let schema = "phones"
    
    @ID var id: UUID?
    
    ///main | mobile | fax
    @Field(key: "label")
    var label: String
    
    @Field(key: "number")
    var number: String
    
    @OptionalParent(key: "member_id")
    var member: Member?
    
    @OptionalParent(key: "user_id")
    var user: User?
    
    @OptionalParent(key: "network_id")
    var network: Network?
    
    @OptionalParent(key: "org_id")
    var org: Organization?
    
    @OptionalParent(key: "insurance_policy_id")
    var policy: InsurancePolicy?
    
    @Timestamp(key: "created_at", on: .create)
    var createdAt: Date?
    
    // When this Planet was last updated.
    @Timestamp(key: "updated_at", on: .update)
    var updatedAt: Date?
    
    init() { }
    
    init(id:         UUID?    = nil,
         label:      String,
         number:     String
         ) {
        self.id          = id
        self.label       = label
        self.number       = number
    }
}

struct PhoneNumberMigration: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(PhoneNumber.schema)
            .id()
            .field("label",      .string, .required)
            .field("number",     .string, .required)
        
            .field("org_id",     .uuid,   .references(Organization.schema,   "id", onDelete: .cascade))
            .field("member_id",  .uuid,   .references(Member.schema,   "id", onDelete: .cascade))
            .field("network_id", .uuid,   .references(Network.schema,   "id", onDelete: .cascade))
            .field("created_at", .datetime)
            .field("updated_at", .datetime)
            .create()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(PhoneNumber.schema).delete()
    }
}

extension PhoneNumber {
    func smsNumber() -> String? {
        var num = self.number
        num = num.replacingOccurrences(of: "(", with: "")
        num = num.replacingOccurrences(of: ")", with: "")
        num = num.replacingOccurrences(of: "-", with: "")
        num = num.replacingOccurrences(of: " ", with: "")
        num = num.trimmingCharacters(in: .whitespacesAndNewlines)

        // Format to E.164 format for SMS compatibility
        return formatToE164(num)
    }

    private func formatToE164(_ phoneNumber: String) -> String? {
        // Remove all non-digit characters except +
        let cleaned = phoneNumber.replacingOccurrences(of: "[^\\d+]", with: "", options: .regularExpression)

        // If it already starts with +, return as is (assuming it's already in E.164)
        if cleaned.hasPrefix("+") {
            return cleaned
        }

        // If it doesn't start with +, assume US number and add +1
        if cleaned.count == 10 {
            return "+1" + cleaned
        } else if cleaned.count == 11 && cleaned.hasPrefix("1") {
            return "+" + cleaned
        }

        // For other cases, return the cleaned number (may need manual formatting)
        return cleaned.isEmpty ? nil : cleaned
    }
}

struct PhoneNumberUpdateMigration: AsyncMigration {
    
    func prepare(on database: Database) async throws {
        try await database.schema(PhoneNumber.schema)
            .field("user_id",  .uuid,   .references(User.schema,   "id", onDelete: .cascade))
            .update()
    }
    
    func revert(on database: Database) async throws {
        try await database.schema(PhoneNumber.schema).update()
    }
    
}

struct PhonePolicyNumberUpdateMigration: AsyncMigration {
    func prepare(on database: Database) async throws {
        try await database.schema(PhoneNumber.schema)
            .field("insurance_policy_id",  .uuid,   .references(InsurancePolicy.schema,   "id", onDelete: .cascade))
            .update()
    }
    
    func revert(on database: Database) async throws {
        try await database.schema(PhoneNumber.schema).update()
    }
}
