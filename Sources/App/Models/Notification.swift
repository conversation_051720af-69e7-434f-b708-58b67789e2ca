//
//  File.swift
//  
//
//  Created by <PERSON> on 1/2/24.
//

import Foundation
import Fluent
import Vapor

final class UserNotification: Model, Content, @unchecked Sendable {
    static let schema = "notifications"
    
    @ID var id: UUID?
    
    @Field(key: "title")
    var title: String
    
    /// standard | chat | urgent
    @Field(key: "kind")
    var kind: String
    
    @Field(key: "message")
    var message: String
    
    @Field(key: "read")
    var read: Bool
    
    @Field(key: "user_id")
    var userID: UUID
    
    @OptionalField(key: "meta")
    var meta: MetaData?
    
    @Timestamp(key: "created_at", on: .create)
    var createdAt: Date?
    
    // When this Planet was last updated.
    @Timestamp(key: "updated_at", on: .update)
    var updatedAt: Date?
    
    init() { }
    
    init(id:        UUID?    = nil,
         title:     String,
         kind:      String,
         message:   String,
         read:      Bool,
         userID:    UUID,
         meta:      MetaData? = nil
         ) {
        self.id          = id
        self.title       = title
        self.kind        = kind
        self.message     = message
        self.read        = read
        self.userID      = userID
        self.meta        = meta
    }
}


struct NotificationMigration: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(UserNotification.schema)
            .id()
            .field("title",       .string, .required)
            .field("kind",        .string, .required)
            .field("message",     .string, .required)
            .field("read",        .bool,   .required, .sql(.default(false)))
            .field("user_id",     .uuid, .required)
            .field("meta",        .json)
                    
            .field("created_at", .datetime)
            .field("updated_at", .datetime)
            .create()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(UserNotification.schema).delete()
    }
}
