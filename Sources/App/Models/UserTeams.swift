//
//  File.swift
//  
//
//  Created by <PERSON> on 1/29/23.
//

import Foundation
import Foundation
import Fluent
import Vapor

// pivot model.
final class UserTeams: Model, @unchecked Sendable {
    static let schema = "user_teams"
    
    @ID var id: UUID?
    
    @Parent(key: "user_id")
    var user: User
    
    @Parent(key: "team_id")
    var team: Team
    
    @Timestamp(key: "created_at", on: .create)
    var createdAt: Date?
    
    // When this Planet was last updated.
    @Timestamp(key: "updated_at", on: .update)
    var updatedAt: Date?
    
    init() { }
    
    init(id: UUID? = nil, user: User, team: Team) throws {
        self.id = id
        self.$user.id = try user.requireID()
        self.$team.id = try team.requireID()
    }
}


struct UserTeamsMigration: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
//        print(database)
        return database.schema(UserTeams.schema)
            .id()
            .field("user_id", .uuid, .required)
            .field("team_id", .uuid, .required)
//            .unique(on: "user_id", "team_id")
            .field("created_at", .datetime)
            .field("updated_at", .datetime)
            .create()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(UserTeams.schema).delete()
    }
}
