//
//  AssociatedPerson.swift
//  
//
//  Created by Augment Agent on 6/30/25.
//

import Foundation
import Fluent
import Vapor

final class AssociatedPerson: Model, @unchecked Sendable {
    static let schema = "associated_persons"
    
    @ID var id: UUID?
    
    @Field(key: "full_name")
    var fullName: String
    
    @Field(key: "relationship")
    var relationship: String
    
    @Enum(key: "role")
    var role: AssociatedPersonRole
    
    @OptionalField(key: "phone")
    var phone: String?
    
    @OptionalField(key: "email")
    var email: String?
    
    @Parent(key: "member_id")
    var member: Member
    
    @OptionalField(key: "notes")
    var notes: String?
    
    @OptionalField(key: "is_primary_contact")
    var isPrimaryContact: Bool?
    
    @OptionalField(key: "is_emergency_contact")
    var isEmergencyContact: Bool?
    
    @Timestamp(key: "created_at", on: .create)
    var createdAt: Date?
    
    @Timestamp(key: "updated_at", on: .update)
    var updatedAt: Date?
    
    init() { }
    
    init(id: UUID? = nil,
         fullName: String,
         relationship: String,
         role: AssociatedPersonRole,
         phone: String? = nil,
         email: String? = nil,
         notes: String? = nil,
         isPrimaryContact: Bool? = nil,
         isEmergencyContact: Bool? = nil) {
        self.id = id
        self.fullName = fullName
        self.relationship = relationship
        self.role = role
        self.phone = phone
        self.email = email
        self.notes = notes
        self.isPrimaryContact = isPrimaryContact
        self.isEmergencyContact = isEmergencyContact
    }
}

// MARK: - Content Conformance
extension AssociatedPerson: Content {
    enum CodingKeys: String, CodingKey {
        case id
        case fullName = "full_name"
        case relationship
        case role
        case phone
        case email
        case notes
        case isPrimaryContact = "is_primary_contact"
        case isEmergencyContact = "is_emergency_contact"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
        // Note: member is excluded from coding keys to prevent decoding issues
    }
}

// MARK: - Migration
struct AssociatedPersonMigration: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(AssociatedPerson.schema)
            .id()
            .field("full_name", .string, .required)
            .field("relationship", .string, .required)
            .field("role", .string, .required)
            .field("phone", .string)
            .field("email", .string)
            .field("notes", .string)
            .field("is_primary_contact", .bool)
            .field("is_emergency_contact", .bool)
            .field("member_id", .uuid, .required, .references(Member.schema, "id", onDelete: .cascade))
            .field("created_at", .datetime)
            .field("updated_at", .datetime)
            .create()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(AssociatedPerson.schema).delete()
    }
}

// MARK: - Input/Output DTOs
struct AssociatedPersonCreateInput: Content {
    let fullName: String
    let relationship: String
    let role: AssociatedPersonRole
    let phone: String?
    let email: String?
    let notes: String?
    let isPrimaryContact: Bool?
    let isEmergencyContact: Bool?
    
    enum CodingKeys: String, CodingKey {
        case fullName = "full_name"
        case relationship
        case role
        case phone
        case email
        case notes
        case isPrimaryContact = "is_primary_contact"
        case isEmergencyContact = "is_emergency_contact"
    }
}

struct AssociatedPersonUpdateInput: Content {
    let fullName: String?
    let relationship: String?
    let role: AssociatedPersonRole?
    let phone: String?
    let email: String?
    let notes: String?
    let isPrimaryContact: Bool?
    let isEmergencyContact: Bool?
    
    enum CodingKeys: String, CodingKey {
        case fullName = "full_name"
        case relationship
        case role
        case phone
        case email
        case notes
        case isPrimaryContact = "is_primary_contact"
        case isEmergencyContact = "is_emergency_contact"
    }
}
