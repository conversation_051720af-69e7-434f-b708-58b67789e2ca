//
//  File.swift
//  
//
//  Created by <PERSON> on 1/29/23.
//
import Foundation
import Fluent
import Vapor

// pivot model.
final class HouseholdTeams: Model, @unchecked Sendable {
    static let schema = "household_teams"
    
    @ID var id: UUID?
    
    @Parent(key: "household_id")
    var household: Household
    
    @Parent(key: "team_id")
    var team: Team
    
    @Timestamp(key: "created_at", on: .create)
    var createdAt: Date?
    
    // When this Planet was last updated.
    @Timestamp(key: "updated_at", on: .update)
    var updatedAt: Date?
    
    init() { }
    
    init(id: UUID? = nil, household: Household, team: Team) throws {
        self.id = id
        self.$household.id = try household.requireID()
        self.$team.id = try team.requireID()
    }
}


struct HouseholdTeamsMigration: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
//        print(database)
        return database.schema(HouseholdTeams.schema)
            .id()
            .field("household_id", .uuid, .required)
            .field("team_id", .uuid, .required)
//            .unique(on: "user_id", "team_id")
            .field("created_at", .datetime)
            .field("updated_at", .datetime)
            .create()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(HouseholdTeams.schema).delete()
    }
}
