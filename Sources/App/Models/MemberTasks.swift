//
//  File.swift
//  
//
//  Created by <PERSON> on 11/29/23.
//

import Foundation
import Fluent
import Vapor


final class MemberTasks: Model, @unchecked Sendable {
    static let schema = "member_tasks"
    
    @ID var id: UUID?
    
    @Parent(key: "member_id")
    var member: Member
    
    @Parent(key: "task_id")
    var task: TaskModel
    
    @Timestamp(key: "created_at", on: .create)
    var createdAt: Date?
    
    // When this Planet was last updated.
    @Timestamp(key: "updated_at", on: .update)
    var updatedAt: Date?
    
    init() { }
    
    init(id: UUID? = nil, user: User, team: Team) throws {
        self.id = id
        self.$member.id = try user.requireID()
        self.$task.id = try team.requireID()
    }
}


struct MemberTasksMigration: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(MemberTasks.schema)
            .id()
            .field("member_id", .uuid, .required)
            .field("task_id", .uuid, .required)

            .field("created_at", .datetime)
            .field("updated_at", .datetime)
            .create()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(MemberTasks.schema).delete()
    }
}
