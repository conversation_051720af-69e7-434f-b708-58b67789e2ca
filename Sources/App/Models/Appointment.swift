//
//  File.swift
//  
//
//  Created by <PERSON> on 8/6/23.
//

import Foundation
import Fluent
import Vapor

final class Appointment: Model, Content, @unchecked Sendable {
    static let schema = "appointments"
    
    @ID var id: UUID?
        
    @Field(key: "creatorID")
    var creatorID: String
    
    @Field(key: "memberID")
    var memberID: String
    
    @Field(key: "title")
    var title: String
    
    @Field(key: "status")
    var status: String
    
    @Field(key: "kind")
    var kind: String
    
    @Field(key: "desc")
    var desc: String
    
    @OptionalField(key: "schedule_at")
    var scheduleAt: String?
    
    @OptionalField(key: "schedule_epoc")
    var scheduleEpoc: Int?
    
    @OptionalField(key: "time_zone")
    var timeZone: String?
    
    @Field(key: "duration")
    var duration: String
    
    @OptionalField(key: "host_link")
    var hostLink: String?
    
    @OptionalField(key: "meeting_link")
    var meetingLink: String?
        
    @OptionalParent(key: "user_id")
    public var host: User?
        
    @OptionalParent(key: "member_id")
    public var member: Member?
    
    @OptionalField(key: "member_book")
    var memberBook: Bool?
    
    @OptionalField(key: "network_id")
    var networkId: UUID?
    
    @OptionalField(key: "task_id")
    var taskId: UUID?
            
    @OptionalParent(key: "org_id")
    var org: Organization?
    
    @OptionalField(key: "rate")
    var rate: String?
    
    @OptionalField(key: "meta")
    var meta: MetaData?
    
    
    @Timestamp(key: "created_at", on: .create)
    var createdAt: Date?
    
    // When this Planet was last updated.
    @Timestamp(key: "updated_at", on: .update)
    var updatedAt: Date?
    
    init() { }
    
    init(id:UUID?    = nil,
         creatorID:    String,
         memberID:     String,
         title:        String,
         status:       String,
         kind:         String,
         desc:         String,
         scheduleAt:   String? = nil,
         scheduleEpoc: Int? = nil,
         timeZone:     String?,
         duration:     String,
         hostLink:     String? = nil,
         meetingLink:  String? = nil,
         meta:         MetaData? = nil,
         memberBook: Bool?,
         networkId: UUID? = nil,
         taskId: UUID? = nil,
         rate: String? = nil)
    {
        self.creatorID    = creatorID.lowercased()
        self.memberID     = memberID.lowercased()
        self.title        = title
        self.status       = status
        self.kind         = kind
        self.desc         = desc
        self.scheduleAt   = scheduleAt
        self.scheduleEpoc = scheduleEpoc
        self.timeZone     = timeZone
        self.duration     = duration
        self.hostLink     = hostLink
        self.meetingLink  = meetingLink
        self.meta         = meta
        self.memberBook   = memberBook
        self.networkId    = networkId
        self.taskId    = taskId
        self.rate       = rate
    }
}

struct AppointmentMigration: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(Appointment.schema)
            .id()
            .field("creatorID",                .string, .required)
            .field("memberID",                 .string, .required)
            .field("title",                    .string, .required)
            .field("status",                   .string, .required)
            .field("kind",                     .string, .required)
            .field("desc",                     .string, .required)
            .field("schedule_at",              .string)
            .field("schedule_epoc",            .int)
            .field("time_zone",                .string)
            .field("duration",                 .string, .required)
            .field("host_link",                .string)
            .field("meeting_link",             .string)
            .field("member_book", .bool)
        
            .field("org_id",     .uuid,  .references(Organization.schema,   "id", onDelete: .cascade))
            .field("member_id",  .uuid,  .references(Member.schema,         "id", onDelete: .cascade))
            .field("user_id",    .uuid,  .references(User.schema,           "id", onDelete: .cascade))
            .field("network_id", .uuid)
        
            .field("meta",       .json)
            
            .field("created_at", .datetime)
            .field("updated_at", .datetime)
            .create()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(Appointment.schema).delete()
    }
}

extension Appointment {
    func formattedDateAndTime() -> String? {
        guard let startDate = self.scheduleAt else { return nil }
        guard let date = DateFormatter.dateFromMultipleFormats(fromString: startDate) else { return nil }
        let formatter = DateFormatter()
        formatter.timeStyle = .medium
        formatter.dateStyle = .medium
        return formatter.string(from: date)
    }
    
    
    func formatScheduleEpoch() -> (dateString: String, timeString: String)? {
        guard let epoch = self.scheduleEpoc else { return nil }
        let date = Date(timeIntervalSince1970: Double(epoch))
        let dateFormatter = DateFormatter()

        // Format the date part
        dateFormatter.dateStyle = .medium
        dateFormatter.timeStyle = .none
        let dateString = dateFormatter.string(from: date)

        // Format the time part
        dateFormatter.dateStyle = .none
        dateFormatter.timeStyle = .short
        let timeString = dateFormatter.string(from: date)

        return (dateString, timeString)
    }
    
    func isVirtual() -> Bool {
        return self.kind.lowercased() == "virtual"
    }
}

extension Int {
    func formatScheduleEpoch() -> (dateString: String, timeString: String)? {
        let date = Date(timeIntervalSince1970: Double(self))
        let dateFormatter = DateFormatter()
        
        // Format the date part
        dateFormatter.dateStyle = .medium
        dateFormatter.timeStyle = .none
        let dateString = dateFormatter.string(from: date)
        
        // Format the time part
        dateFormatter.dateStyle = .none
        dateFormatter.timeStyle = .short
        let timeString = dateFormatter.string(from: date)
        
        return (dateString, timeString)
    }
}



struct AppointmentUpdateMigration: AsyncMigration {
    
    func prepare(on database: Database) async throws {
        try await database.schema(Appointment.schema)
            .field("task_id",    .uuid)
            .update()
    }
    
    func revert(on database: Database) async throws {
        try await database.schema(Appointment.schema).update()
    }
    
}


struct AppointmentRateUpdateMigration: AsyncMigration {
    
    func prepare(on database: Database) async throws {
        try await database.schema(Appointment.schema)
            .field("rate",    .string)
            .update()
    }
    
    func revert(on database: Database) async throws {
        try await database.schema(Appointment.schema).update()
    }
    
}

struct AppointmentscheduleEpocUpdateMigration: AsyncMigration {
    
    func prepare(on database: Database) async throws {
        try await database.schema(Appointment.schema)
            .field("schedule_epoc",   .int)
            .update()
    }
    
    func revert(on database: Database) async throws {
        try await database.schema(Appointment.schema).update()
    }
    
}

struct AppointmentMemberBookUpdateMigration: AsyncMigration {
    
    func prepare(on database: Database) async throws {
        try await database.schema(Appointment.schema)
            .field("member_book",   .bool)
            .update()
    }
    
    func revert(on database: Database) async throws {
        try await database.schema(Appointment.schema).update()
    }
    
}


struct AppointmentNetworkUpdateMigration: AsyncMigration {
    
    func prepare(on database: Database) async throws {
        try await database.schema(Appointment.schema)
            .field("network_id",   .uuid)
            .update()
    }
    
    func revert(on database: Database) async throws {
        try await database.schema(Appointment.schema).update()
    }
    
}

