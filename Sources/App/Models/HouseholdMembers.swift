//
//  File.swift
//  
//
//  Created by <PERSON> on 1/29/23.
//

import Foundation
import Fluent
import Vapor

// pivot model.
final class HouseholdMembers: Model, @unchecked Sendable {
    static let schema = "household_members"
    
    @ID var id: UUID?
    
    @Parent(key: "member_id")
    var member: Member
    
    @Parent(key: "household_id")
    var household: Household
    
    @Timestamp(key: "created_at", on: .create)
    var createdAt: Date?
    
    // When this Planet was last updated.
    @Timestamp(key: "updated_at", on: .update)
    var updatedAt: Date?
    
    init() { }
    
    init(id: UUID? = nil, member: Member, household: Household) throws {
        self.id = id
        self.$member.id = try member.requireID()
        self.$household.id = try household.requireID()
        
    }
}


struct HouseholdMembersMigration: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
//        print(database)
        return database.schema(HouseholdMembers.schema)
            .id()
            .field("member_id",    .uuid, .required)
            .field("household_id", .uuid, .required)
            
//            .unique(on: "user_id", "team_id")
            .field("created_at", .datetime)
            .field("updated_at", .datetime)
            .create()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(HouseholdMembers.schema).delete()
    }
}
