//
//  File.swift
//
//
//  Created by <PERSON> on 7/1/21.
//

import Foundation
import Fluent
import Vapor

final class QuestionInput: Content {
    var sectionID:String?
    var title:String?
    var message:String?
    var level:Int?
    var value:Int?
    var type:ServiceType?
    var questions:[QuestionInput]?
    
    func returnUpdatedModel(section:Question) -> Question {
        if let title = title {
            section.title = title
        }
    
        if let message = message {
            section.message = message
        }
        
        if let value = value {
            section.value = value
        }
        
        if let level = level {
            section.level = level
        }
        
        if let type = type {
            section.type = type
        }
        return section
    }
}

final class Question: Model, Content, @unchecked Sendable {
    static let schema = "questions"
    
    @ID var id: UUID?
    
    @OptionalParent(key: "section_id")
    var section: Section?
    
    @OptionalParent(key: "question_id")
    var question: Question?
    
    @OptionalField(key: "title")
    var title: String?
    
    @Field(key: "message")
    var message: String
    
    @Field(key: "level")
    var level: Int
    
    @OptionalEnum(key: "type")
    var type: ServiceType?
    
    @Field(key: "value")
    var value: Int
    
    @Field(key: "score")
    var score: Int?
    
    @Children(for: \.$question)
    var questions: [Question]
    
    @Timestamp(key: "created_at", on: .create)
    var createdAt: Date?
        
    @Timestamp(key: "updated_at", on: .update)
    var updatedAt: Date?
    
    init() {}
    
    init(id: UUID? = nil,
         title: String?,
         message: String,
         level:Int,
         value:Int,
         score:Int?,
         sectionID:Section.IDValue?,
         questionID:Question.IDValue?,
         type:ServiceType?) {
            self.id           = id
            self.title        = title
            self.message      = message
            self.level        = level
            self.value        = value
            self.score        = score
            self.$section.id  = sectionID
            self.$question.id = questionID
            self.type         = type
        }
}


struct QuestionMigration: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(Question.schema)
            .id()
            .field("title",        .string)
            .field("message",      .string, .required)
            .field("level",        .int,    .required)
            .field("value",        .int)
            .field("score",        .int)
            .field("type",         .string)
            .field("created_at",   .datetime)
            .field("updated_at",   .datetime)
            .field("section_id",  .uuid, .references("sections", "id", onDelete: .cascade))
            .field("question_id", .uuid, .references("questions", "id", onDelete: .cascade))
            .create()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(Question.schema).delete()
    }
}
