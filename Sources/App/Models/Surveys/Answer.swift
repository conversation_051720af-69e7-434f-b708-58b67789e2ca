//
//  File.swift
//
//
//  Created by <PERSON> on 7/1/21.
//
import Foundation
import Fluent
import Vapor


final class Answer: Model, Content, @unchecked Sendable {
    static let schema = "answers"
    
    @ID var id: UUID?
    
    @Field(key: "survey_id")
    var surveyID: String?
    
    @Field(key: "taker_id")
    var takerID: String?
    
    @Field(key: "giver_id")
    var giverID: String?
    
    @Field(key: "answer")
    var answer: String
    
    @Field(key: "parent_id")
    var parentID: String?
    
    @Field(key: "question_id")
    var questionID: String?
    
    @Field(key: "value")
    var value: String
    
    @Field(key: "score")
    var score: String
    
    @Timestamp(key: "created_at", on: .create)
    var createdAt: Date?
        
    @Timestamp(key: "updated_at", on: .update)
    var updatedAt: Date?
    
    @OptionalField(key: "org_id")
    var orgID: String?
    
    @OptionalField(key: "key")
    var key: String?
    
    init() { }
    
    init(id: UUID? = nil,
         surveyID:String?,
         parentID:String?,
         takerID:String?,
         giverID:String?,
         answer:String,
         questionID:String?,
         value:String,
         score:String,
         orgID:String?,
         key:String?) {
        self.id         = id
        self.parentID   = parentID?.lowercased()
        self.surveyID   = surveyID?.lowercased()
        self.takerID    = takerID?.lowercased()
        self.giverID    = giverID?.lowercased()
        self.answer     = answer.removingUnwantedCharacters()
        self.questionID = questionID?.lowercased()
        self.value      = value
        self.score      = score
        self.orgID      = orgID?.lowercased()
        self.key        = key
    }
}

struct AnswerMigration: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(Answer.schema)
            .id()
            .field("survey_id",    .string, .required)
            .field("taker_id",     .string, .required)
            .field("giver_id",     .string, .required)
            .field("answer",       .string, .required)
            .field("question_id",  .string, .required)
            .field("value",        .string, .required)
            .field("score",        .string, .required)
            .field("parent_id",    .string)
            .field("created_at",   .datetime)
            .field("updated_at",   .datetime)
            .create()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(Answer.schema).delete()
    }
}


struct AnswerMigrationUpdate: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(Answer.schema)
            .field("org_id",        .string)
            .update()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(Answer.schema).delete()
    }
}

struct AnswerAddKeyMigrationUpdate: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(Answer.schema)
            .field("key",        .string)
            .update()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(Answer.schema).delete()
    }
}
