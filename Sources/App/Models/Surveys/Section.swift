//
//  File.swift
//
//
//  Created by <PERSON> on 7/1/21.
//

import Foundation
import Fluent
import Vapor

final class SectionInput:  Content {
    var surveyID:String?
    var title:String?
    var complete:Bool?
    var score:String?
    var type:String?
    
    func returnUpdatedModel(section:Section) -> Section {
        if let title = title {
            section.title = title
        }
        
        if let complete = complete {
            section.complete = complete
        }
        
        if let score = score {
            section.score = score
        }
        
        if let type = type {
            section.type = type
        }
        
        return section
    }
}

final class Section: Model, Content, @unchecked Sendable {
    static let schema = "sections"
    
    @ID var id: UUID?
    
    @OptionalParent(key: "survey_id")
    var survey: Survey?
    
    @Field(key: "title")
    var title: String
    
    @OptionalField(key: "score")
    var score: String?
        
    @OptionalField(key: "type")
    var type: String?
    
    @OptionalField(key: "kind")
    var kind: String? //income / nutrtion / housing
    
    @Children(for: \.$section)
    var questions: [Question]
    
    @Field(key: "complete")
    var complete:Bool
    
    @Timestamp(key: "created_at", on: .create)
    var createdAt: Date?
        
    @Timestamp(key: "updated_at", on: .update)
    var updatedAt: Date?
    
    init() { }
    
    init(id: UUID? = nil,
         title: String,
         score:String?,
         complete:Bool,
         surveyID:Survey.IDValue?,
         type:String?) {
        self.id         = id
        self.title      = title
        self.complete   = complete
        self.$survey.id = surveyID
        self.score      = score
        self.type       = type
    }
    
    func isHousehold() -> Bool {
        return self.type?.lowercased() == "household"
    }
}

struct SectionMigration: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(Section.schema)
            .id()
            .field("title",        .string, .required)
            .field("complete",     .bool,   .required)
            .field("score",        .string)
            .field("type",         .string)
            .field("kind",         .string)
            .field("created_at",   .datetime)
            .field("updated_at",   .datetime)
            .field("survey_id",    .uuid,   .references("surveys", "id", onDelete: .cascade))
            .create()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(Section.schema).delete()
    }
}
