//
//  JSONB.swift
//  hmbl-core
//
//  Created by <PERSON> on 3/15/25.
//

import Foundation
import Fluent
import Vapor
import PostgresNIO
import FluentPostgresDriver


struct JSONB: Codable {
    let value: Any
    
    init(_ value: Any) {
        self.value = value
    }
    
    init(from decoder: Decoder) throws {
        let container = try decoder.singleValueContainer()
        
        if container.decodeNil() {
            self.value = NSNull()
        } else if let bool = try? container.decode(Bool.self) {
            self.value = bool
        } else if let int = try? container.decode(Int.self) {
            self.value = int
        } else if let double = try? container.decode(Double.self) {
            self.value = double
        } else if let string = try? container.decode(String.self) {
            self.value = string
        } else if let array = try? container.decode([JSONB].self) {
            self.value = array.map { $0.value }
        } else if let dictionary = try? container.decode([String: JSONB].self) {
            self.value = dictionary.mapValues { $0.value }
        } else {
            throw Decoding<PERSON>rro<PERSON>.dataCorruptedError(
                in: container,
                debugDescription: "Cannot decode value"
            )
        }
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.singleValueContainer()
        
        switch self.value {
        case is NSNull:
            try container.encodeNil()
        case let bool as Bool:
            try container.encode(bool)
        case let int as Int:
            try container.encode(int)
        case let double as Double:
            try container.encode(double)
        case let string as String:
            try container.encode(string)
        case let array as [Any]:
            try container.encode(array.map { JSONB($0) })
        case let dictionary as [String: Any]:
            try container.encode(dictionary.mapValues { JSONB($0) })
        default:
            throw EncodingError.invalidValue(
                self.value,
                EncodingError.Context(
                    codingPath: container.codingPath,
                    debugDescription: "Value cannot be encoded"
                )
            )
        }
    }
}
