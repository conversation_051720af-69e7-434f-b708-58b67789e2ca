//
//  JsonWrapper.swift
//  hmbl-core
//
//  Created by <PERSON> on 3/15/25.
//

import Foundation
import PostgresNIO
import FluentPostgresDriver

struct JsonWrapper: Codable, PostgresEncodable, PostgresDecodable {
    let template: String

    static var postgresDataType: PostgresDataType {
        .jsonb
    }

    init(_ template: String) {
        self.template = template
    }

    init(from decoder: Decoder) throws {
        let container = try decoder.singleValueContainer()
        if let string = try? container.decode(String.self) {
            self.template = string
        } else {
            let data = try JSONEncoder().encode(container.decode(JSONB.self))
            self.template = String(data: data, encoding: .utf8)!
        }
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.singleValueContainer()
        try container.encode(template)
    }

    init?(postgresData: PostgresData) {
        guard let data = postgresData.jsonb,
              let payload = String(data: data, encoding: .utf8) else {
            return nil
        }
        self.template = payload
    }

    var postgresData: PostgresData? {
        guard let jsonString = self.template.data(using: .utf8) else { return nil }
        return .init(jsonb: jsonString)
    }
}
