//
//  File.swift
//
//
//  Created by <PERSON> on 4/19/23.
//

import Foundation
import Fluent
import Vapor

extension MetaData {
    func toCSVRow() -> [String] {
        return [
            csvEscape(dataAsJSONString()),
            csvEscape(contactsAsJSONString()),
            csvEscape(accessAsJSONString())
        ]
    }
    
    func asJSONString() -> String {
        guard let data = try? JSONEncoder().encode(self),
              let json = String(data: data, encoding: .utf8) else {
            return ""
        }
        return json
    }

    private func dataAsJSONString() -> String {
        return encodeToJSONString(data)
    }

    private func contactsAsJSONString() -> String {
        return encodeToJSONString(contacts ?? [])
    }

    private func accessAsJSONString() -> String {
        return encodeToJSONString(access ?? [])
    }

    private func encodeToJSONString<T: Encodable>(_ object: T) -> String {
        if let data = try? JSONEncoder().encode(object),
           let string = String(data: data, encoding: .utf8) {
            return string
        } else {
            return ""
        }
    }
}

struct MetaData: Codable {
    var data: [String:String]
    var contacts: [[String: String]]? = nil
    var access: [Accesskeys]? = nil
    var schedulerMemberId: String? = nil
    var schedulerAppointment: [String: String]? = nil
    var providerNpi: String? = nil
    
    static let `default`: MetaData = MetaData(data: [:])
    static let defaultAccess: MetaData = MetaData(
        data: ["main-member-table": "[\"firstName\",\"dob\",\"status\",\"household\",\"teams\",\"score\"]"],
        access: Accesskeys.default)
}

final class Consent: Model, Content, @unchecked Sendable {
    static let schema = "consent"

    @ID var id: UUID?

    @Field(key: "name")
    var name: String
    
    @OptionalField(key: "member_id")
    var memberId: UUID?
    
    @Field(key: "type")
    var type: String

    @Field(key: "url")
    var url: String
    
    @Timestamp(key: "created_at", on: .create)
    var createdAt: Date?

    @Timestamp(key: "updated_at", on: .update)
    var updatedAt: Date?

    init() { }

    init(id: UUID? = nil,
         name: String,
         memberId: UUID? = nil,
         type:String,
         url:String) {
        self.id        = id
        self.name      = name
        self.memberId = memberId
        self.type       = type
        self.url        = url
    }
}


struct ConsentMigration: Migration {

    func prepare(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(Consent.schema)
            .id()
            .field("name",          .string, .required)
            .field("type",          .string, .required)
            .field("url",           .string, .required)
            .field("member_id",      .uuid)
            
            .field("created_at",   .datetime)
            .field("updated_at",   .datetime)
            .create()
    }

    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(Consent.schema).delete()
    }
}
