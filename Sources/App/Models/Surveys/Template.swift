//
//  Template.swift
//
//
//  Created by <PERSON> on 2/8/23.
//

import Foundation
import Fluent
import Vapor
    
struct TemplateSelection: Content {
    let title: String
    let key: String
}

final class Template: Model, Content, @unchecked Sendable {
    static let schema = "templates"
    
    @ID var id: UUID?
    
    @Field(key: "name")
    var name: String
    
    @Field(key: "org_id")
    var orgID: String
    
    @Field(key: "status")
    var status: String
    
    @Field(key: "key")
    var key: String
    
    @Field(key: "kind")
    var kind: String
    
    @Field(key: "language")
    var language: String
    
    @Field(key: "template")
    var template: JsonWrapper
    
    @Field(key: "scored")
    var scored: Bool
    
    @Timestamp(key: "created_at", on: .create)
    var createdAt: Date?
    

    @Timestamp(key: "updated_at", on: .update)
    var updatedAt: Date?
    
    init() { }
    
    init(id: UUID? = nil, name: String, orgID:String, status:String, key:String, kind: String, language: String, template:<PERSON><PERSON><PERSON><PERSON><PERSON>, scored: <PERSON>ol) {
        self.id        = id
        self.name      = name
        self.orgID     = orgID.lowercased()
        self.status    = status
        self.template  = template
        self.key       = key
        self.kind      = kind
        self.language  = language
        self.scored    = scored
    }
    
    //MARK: - Computed Property
    
    func isRapidHRP() -> Bool {
        return self.key == "rapid_hrp"
    }
}


struct TemplateMigration: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
//        print(database)
        return database.schema(Template.schema)
            .id()
            .field("name",       .string, .required)
            .field("org_id",     .string, .required)
            .field("status",     .string, .required)
            .field("key",        .string, .required)
            .field("template",   .json,   .required)
            .field("created_at", .datetime)
            .field("updated_at", .datetime)
            .create()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(Template.schema).delete()
    }
}

struct TemplateUpdateMigration: AsyncMigration {
    
    func prepare(on database: Database) async throws {
        try await database.schema(Template.schema)
            .field("kind",  .string)
            .update()
    }
    
    func revert(on database: Database) async throws {
        try await database.schema(Template.schema).update()
    }
}


struct TemplateLangUpdateMigration: AsyncMigration {
    
    func prepare(on database: Database) async throws {
        try await database.schema(Template.schema)
            .field("language",  .string)
            .update()
    }
    
    func revert(on database: Database) async throws {
        try await database.schema(Template.schema).update()
    }
    
}


struct TemplateScoreUpdateMigration: AsyncMigration {
    
    func prepare(on database: Database) async throws {
        try await database.schema(Template.schema)
            .field("scored",  .bool)
            .update()
    }
    
    func revert(on database: Database) async throws {
        try await database.schema(Template.schema).update()
    }
}
