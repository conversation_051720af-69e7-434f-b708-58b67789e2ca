//
//  File.swift
//
//
//  Created by <PERSON> on 7/1/21.
//

import Foundation
import Fluent
import Vapor

final class SurveyAnswers: Content {
    var survey: Survey
    var answers: [Answer]
    var scored: Bool
    var questionAnswers:[QuestionAnswer]
    
    init(survey: Survey, answers: [Answer], scored: Bool, questionAnswers:[QuestionAnswer]) {
        self.survey = survey
        self.answers = answers
        self.scored = scored
        self.questionAnswers = questionAnswers
    }
}

final class Survey: Model, Content, @unchecked Sendable {
    static let schema = "surveys"
    
    @ID var id: UUID?
    
    @Field(key: "name")
    var name: String
    
    @Field(key: "member_id")
    var memberID: UUID?
    
    @OptionalParent(key: "taker_id")
    public var taker: User?
    
    @Field(key: "status")
    var status: String
    
    @Field(key: "score")
    var score: String?
    
    @Field(key: "key")
    var key: String
    
    @OptionalField(key: "first_survey")
    var firstSurvey: Int?
    
    @OptionalField(key: "last_survey")
    var lastSurvey: Int?
    
    @Children(for: \.$survey)
    var sections: [Section]
        
    @Children(for: \.$survey)
    var attachments: [Attachment]
    
    @Timestamp(key: "started_at", on: .none)
    var startedAt: Date?
    
    @Timestamp(key: "ended_at", on: .none)
    var endedAt: Date?
    
    @Timestamp(key: "created_at", on: .create)
    var createdAt: Date?
        
    @Timestamp(key: "updated_at", on: .update)
    var updatedAt: Date?
    
    @OptionalField(key: "org_id")
    var orgID: String?
    
    @OptionalParent(key: "task_id")
    var task: TaskModel?
    
    init() { }
    
    init(id: UUID? = nil, name: String, memberID:UUID?, status:String, key:String, score:String, startedAt:Date?, endedAt:Date?, firstSurvey:Int?, lastSurvey:Int?, orgID:String?) {
        self.id        = id
        self.name      = name
        self.memberID  = memberID
        self.status    = status
        self.score     = score
        self.key       = key
        self.startedAt = startedAt
        self.endedAt   = endedAt
        self.firstSurvey     = firstSurvey
        self.lastSurvey      = lastSurvey
        self.orgID     = orgID?.lowercased()
        
    }
    
    func isComplete() -> Bool {
        return self.status.lowercased() == "complete"
    }
    
    func isBaseline() -> Bool {
        return self.key.lowercased() == "rapid_hrp"
    }
    
    func isFullHRP() -> Bool {
        return self.key.lowercased() == "full_hrp"
    }
    
    func isScoreable() -> Bool {
        return self.key.lowercased() == "rapid_hrp" || 
        self.key.lowercased() == "full_hrp" ||
        self.key.lowercased() == "empowered_hra"
    }
}


struct SurveyMigration: Migration {
    
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(Survey.schema)
            .id()
            .field("name",         .string, .required)
            .field("member_id",    .uuid,   .required)
            .field("status",       .string, .required)
            .field("key",          .string, .required)
            .field("score",        .string)
            .field("first_survey",        .int)
            .field("last_survey",         .int)
            .field("started_at",   .datetime)
            .field("ended_at",     .datetime)
        
            .field("taker_id",  .uuid, .references(User.schema, "id"))
        
            .field("created_at",   .datetime)
            .field("updated_at",   .datetime)
            .create()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(Survey.schema).delete()
    }
}



struct SurveyTaskMigrationUpdate: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(Survey.schema)
            .field("task_id",    .uuid,   .references(TaskModel.schema, "id", onDelete: .cascade))
            .update()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(Survey.schema).delete()
    }
}

struct SurveyMigrationUpdate: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(Survey.schema)
            .field("org_id",        .string)            
            .update()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(Survey.schema).delete()
    }
}
