//
//  File.swift
//  
//
//  Created by <PERSON> on 1/29/23.
//

import Foundation
import Fluent
import Vapor

final class Household: Model, Content, @unchecked Sendable {
    static let schema = "households"
    
    @ID var id: UUID?
    
    @Field(key: "title")
    var title: String
    
    @Field(key: "type")
    var type: String
    
    @Field(key: "kind")
    var kind: String
    
    @OptionalField(key: "lastVisit")
    var lastVisit: String?
    
    @OptionalField(key: "householdScore")
    var householdScore: String?
    
    @Children(for: \.$household)
    var attachments: [Attachment]
    
    @Children(for: \.$household)
    var address: [Address]
    
    @Children(for: \.$household)
    var pets: [Animal]
    
    @Siblings(through: HouseholdTeams.self, from: \.$household, to: \.$team)
    public var teams: [Team]
    
    @OptionalParent(key: "member_id")
    public var headOfHouse: Member?
    
    @Siblings(through: HouseholdMembers.self, from: \.$household, to: \.$member)
    public var members: [Member]
        
    @OptionalParent(key: "org_id")
    var org: Organization?
    
    @Timestamp(key: "created_at", on: .create)
    var createdAt: Date?
    
    // When this Planet was last updated.
    @Timestamp(key: "updated_at", on: .update)
    var updatedAt: Date?
    
    init() { }
    
    init(id:              UUID?    = nil,
         title:           String,
         type:            String,
         kind:            String,
         lastVisit:       String? = nil,
         householdScore:  String? = nil
    ) {
        self.id             = id
        self.title          = title
        self.type           = type
        self.kind           = kind
        self.lastVisit      = lastVisit
        self.householdScore = householdScore
    }
}

struct HouseholdMigration: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(Household.schema)
            .id()
            .field("title",             .string, .required)
            .field("type",              .string, .required)
            .field("kind",              .string, .required)
            .field("lastVisit",         .string)
            .field("householdScore",    .string)
        
        
            .field("member_id",  .uuid, .references(Member.schema, "id"))
        
            .field("created_at", .datetime)
            .field("updated_at", .datetime)
            .create()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(Household.schema).delete()
    }
}


struct HouseholdMigrationUpdate: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(Household.schema)
            .field("org_id",     .uuid, .references(Organization.schema,   "id", onDelete: .cascade))
            .update()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(Household.schema).delete()
    }
}
