//
//  File.swift
//  
//
//  Created by <PERSON> on 6/2/24.
//

import Foundation
import Fluent
import Vapor

final class MemberStatus: Model, Content, @unchecked Sendable {
    static let schema = "member_status"
    
    @ID var id: UUID?
    
    @OptionalField(key: "name")
    var name: String?
    
    @OptionalField(key: "state")
    var state: String?
    
    @OptionalField(key: "kind")
    var kind: String?
            
    @OptionalParent(key: "member_id")
    var member: Member?
    
    @OptionalField(key: "location")
    var location: String?
    
    @Children(for: \.$memberStatus)
    var address: [Address]
    
    @OptionalField(key: "start_date")
    var startDate: String?
    
    @OptionalField(key: "end_date")
    var endDate: String?
    
    @Timestamp(key: "created_at", on: .create)
    var createdAt: Date?
    
    // When this Planet was last updated.
    @Timestamp(key: "updated_at", on: .update)
    var updatedAt: Date?
    
    init() { }
    
    init(id:         UUID?    = nil,
         name:       String?,
         state:      String?,
         kind:       String?,
         location:   String?,
         startDate:  String?,
         endDate:    String?) {
        self.id        = id
        self.name      = name
        self.state     = state
        self.kind      = kind
        self.location  = location
        self.startDate = startDate
        self.endDate   = endDate
    }
    
    func isStarted() -> Bool {
        return !isCompleted()
    }
    
    func isCompleted() -> Bool {
        return endDate != "" && endDate != nil
    }
}

struct MemberStatusMigration: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(MemberStatus.schema)
            .id()
            .field("name",       .string)
            .field("state",      .string)
            .field("kind",       .string)
            .field("location",   .string)
            .field("start_date",  .string)
            .field("end_date",    .string)
            .field("member_id",  .uuid, .references(Member.schema, "id", onDelete: .cascade))
        
            .field("created_at", .datetime)
            .field("updated_at", .datetime)
            .create()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(MemberStatus.schema).delete()
    }
}
