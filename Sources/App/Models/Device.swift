//
//  File.swift
//  
//
//  Created by <PERSON> on 5/8/23.
//

import Foundation
import Fluent
import Vapor
import SotoSNS

final class Device: Model, Content, @unchecked Sendable {
    static let schema = "devices"
    
    @ID var id: UUID?
    
    @Field(key: "deviceID")
    var deviceID: String
    
    @Field(key: "userID")
    var userID: String
    
    @OptionalField(key: "model")
    var model: String?
    
    @OptionalField(key: "arn")
    var arn: String?
    
    @OptionalField(key: "subscription_arn")
    var subscriptionArn: String?
    
    @Timestamp(key: "created_at", on: .create)
    var createdAt: Date?
    
    @Timestamp(key: "updated_at", on: .update)
    var updatedAt: Date?
    
    init() { }
    
    init(id:        UUID? = nil,
         deviceID:  String,
         userID:    String,
         model:     String? = nil,
         arn:       String? = nil,
         subArn:     String? = nil) {
        self.id        = id
        self.deviceID  = deviceID
        self.userID    = userID
        self.model     = model
        self.arn       = arn
        self.subscriptionArn  = subArn
    }
}

struct DeviceMigration: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(Device.schema)
            .id()
            .field("deviceID",    .string, .required).unique(on: "deviceID")
            .field("userID",      .string, .required)
            .field("model",       .string)
            .field("created_at",  .datetime)
            .field("updated_at",  .datetime)
            .create()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(Device.schema).delete()
    }
}

struct DeviceSubIdUpdateMigration: AsyncMigration {
    func prepare(on database: Database) async throws {
        try await database.schema(Device.schema)
            .field("subscription_arn", .string)
            .update()
    }
    
    func revert(on database: Database) async throws {
        try await database.schema(Device.schema).update()
    }
}

struct DeviceUpdateMigration: AsyncMigration {
    
    func prepare(on database: Database) async throws {
        try await database.schema(Device.schema)
            .field("arn", .string)
            .update()
    }
    
    func revert(on database: Database) async throws {
        try await database.schema(Device.schema).update()
    }
    
}
