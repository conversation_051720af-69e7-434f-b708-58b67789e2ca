//
//  File.swift
//  
//
//  Created by <PERSON> on 2/7/23.
//

import Foundation
import Fluent
import Vapor


final class CarePackageItem: Model, Content, @unchecked Sendable {
    static let schema = "carepackage_item"
    
    @ID var id: UUID?
    
    @OptionalParent(key: "network_id")
    var network: Network?
    
    ///"housing | transportation"
    @Field(key: "type")
    var type: String
    
    ///pending | booked | complete | other
    @Field(key: "status")
    var status: String
    
    @OptionalField(key: "desc")
    var desc: String?
    
    @OptionalParent(key: "carepackage_id")
    var carepackage: CarePackage?
    
    @Children(for: \.$item)
    var items: [TimelineItem]
        
    @Siblings(through: ItemAppointments.self, from: \.$item, to: \.$appointment)
    public var appointments: [Appointment]
    
    @Timestamp(key: "created_at", on: .create)
    var createdAt: Date?
    
    // When this Planet was last updated.
    @Timestamp(key: "updated_at", on: .update)
    var updatedAt: Date?
    
    init() { }
    
    init(id:      UUID?    = nil,
         type:    String,
         status:  String,
         desc:  String?
         ) {
        self.id         = id
        self.type       = type
        self.status     = status
        self.desc       = desc
    }
        
}

struct CarePackageItemMigration: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(CarePackageItem.schema)
            .id()
            .field("type",       .string, .required)
            .field("status",     .string, .required)
            .field("desc",       .string)
        
            .field("network_id",      .uuid,   .references(Network.schema,"id", onDelete: .cascade))
            .field("carepackage_id",  .uuid,   .references(CarePackage.schema,"id", onDelete: .cascade))
            .field("created_at",  .datetime)
            .field("updated_at",  .datetime)
            .create()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(CarePackageItem.schema).delete()
    }
}
