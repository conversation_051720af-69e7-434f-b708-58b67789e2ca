//
//  File.swift
//  
//
//  Created by <PERSON> on 5/15/24.
//

import Foundation
import Fluent
import Vapor


// pivot model.
final class NetworkCarriers: Model, @unchecked Sendable {
    static let schema = "network_carriers"
    
    @ID var id: UUID?
    
    @Parent(key: "network_id")
    var network: Network
    
    @Parent(key: "carrier_id")
    var carrier: Carrier
    
    @Timestamp(key: "created_at", on: .create)
    var createdAt: Date?
    
    // When this Planet was last updated.
    @Timestamp(key: "updated_at", on: .update)
    var updatedAt: Date?
    
    init() { }
    
    init(id: UUID? = nil, network: Network, carrier: Carrier) throws {
        self.id = id
        self.$network.id = try network.requireID()
        self.$carrier.id = try carrier.requireID()
    }
}

struct NetworkCarriersMigration: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
//        print(database)
        return database.schema(NetworkCarriers.schema)
            .id()
            .field("network_id", .uuid, .required)
            .field("carrier_id", .uuid, .required)
            .unique(on: "network_id", "carrier_id")
            .field("created_at", .datetime)
            .field("updated_at", .datetime)
            
            .create()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(NetworkCarriers.schema).delete()
    }
}
