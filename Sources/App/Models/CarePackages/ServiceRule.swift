//
//  File.swift
//  
//
//  Created by <PERSON> on 1/29/23.
//

import Foundation
import Fluent
import Vapor

final class ServiceRule: Model, Content, @unchecked Sendable {
    static let schema = "service_rules"
    
    @ID var id: UUID?
    
    @Field(key: "title")
    var title: String
    
    @OptionalField(key: "subtitle")
    var subtitle: String?
    
    @Field(key: "msg")
    var msg: String
    
    @OptionalParent(key: "service_id")
    var service: Service?
    
    @Timestamp(key: "created_at", on: .create)
    var createdAt: Date?
    
    // When this Planet was last updated.
    @Timestamp(key: "updated_at", on: .update)
    var updatedAt: Date?
    
    init() { }
    
    init(id:         UUID?    = nil,
         title:      String,
         subtitle:   String?  = nil,
         msg:        String
         ) {
        self.id          = id
        self.title       = title
        self.subtitle    = subtitle
        self.msg         = msg
    }
}

struct ServiceRuleMigration: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(ServiceRule.schema)
            .id()
            .field("title",       .string, .required)
            .field("subtitle",    .string)
            .field("msg",         .string, .required)
            .field("service_id",  .uuid,   .references(Service.schema,   "id", onDelete: .cascade))
            .field("created_at",  .datetime)
            .field("updated_at",  .datetime)            
            .create()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(ServiceRule.schema).delete()
    }
}
