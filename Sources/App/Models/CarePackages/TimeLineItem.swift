//
//  File.swift
//  
//
//  Created by <PERSON> on 1/29/23.
//

import Foundation
import Fluent
import Vapor

final class TimelineItem: Model, Content, @unchecked Sendable {
    static let schema = "timeline_item"
    
    @ID var id: UUID?
          
    @Field(key: "cp_id")
    var carepackageID: String
    
    @OptionalField(key: "title")
    var title: String?
    
    @Field(key: "status")
    var status: String
    
    @Field(key: "desc")
    var desc: String
    
    @OptionalField(key: "visible")
    var visible: Bool?
    
    @OptionalParent(key: "member_id")
    var creator: User?
    
    @OptionalField(key: "person_id")
    var memberId: UUID?
    
    @OptionalField(key: "meta")
    var meta: MetaData?
        
    @OptionalParent(key: "carepackage_item_id")
    var item: CarePackageItem?

    @OptionalParent(key: "care_plan_id")
    var carePlan: CarePlan?

    @OptionalParent(key: "program_id")
    var program: Program?

    @Siblings(through: TimelineItemService.self, from: \.$item, to: \.$service)
    public var services: [Service]
    
        
    @Timestamp(key: "created_at", on: .create)
    var createdAt: Date?
    
    // When this Planet was last updated.
    @Timestamp(key: "updated_at", on: .update)
    var updatedAt: Date?
    
    init() { }
    
    init(id:                 UUID?    = nil,
         carepackageID:      String,
         status:             String,
         desc:               String,
         title:              String?,
         memberId:           UUID?,
         visible:            Bool?,
         meta:               MetaData?) {
        self.id                = id
        self.carepackageID     = carepackageID.lowercased()
        self.status            = status
        self.desc              = desc
        self.title             = title
        self.memberId          = memberId
        self.visible           = visible
        self.meta              = meta
    }
}

struct TimelineItemMigration: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(TimelineItem.schema)
            .id()
            .field("status",         .string, .required)
            .field("desc",           .string, .required)
            .field("cp_id",           .string, .required)
                    
            .field("member_id",      .uuid, .references(User.schema,"id", onDelete: .cascade))
            .field("carepackage_item_id", .uuid, .references(CarePackageItem.schema, "id", onDelete: .cascade))
        
            .field("created_at",  .datetime)
            .field("updated_at",  .datetime)
            .create()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(TimelineItem.schema).delete()
    }
}

struct TimelineItemUpdateMigration: AsyncMigration {

    func prepare(on database: Database) async throws {
        try await database.schema(TimelineItem.schema)
            .field("person_id", .uuid)
            .field("title", .string)
            .field("visible", .bool)
            .field("meta", .json)
            .update()
    }

    func revert(on database: Database) async throws {
        try await database.schema(TimelineItem.schema).update()
    }
}

struct TimelineItemCarePlanMigration: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        database.schema(TimelineItem.schema)
            .field("care_plan_id", .uuid, .references("care_plans", "id", onDelete: .noAction))
            .update()
    }

    func revert(on database: Database) -> EventLoopFuture<Void> {
        database.schema(TimelineItem.schema)
            .deleteField("care_plan_id")
            .update()
    }
}

struct TimelineItemProgramMigration: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        database.schema(TimelineItem.schema)
            .field("program_id", .uuid, .references("programs", "id", onDelete: .cascade))
            .update()
    }

    func revert(on database: Database) -> EventLoopFuture<Void> {
        database.schema(TimelineItem.schema)
            .deleteField("program_id")
            .update()
    }
}

struct TimelineMessageChange {
    var homelessChange: Bool
    var pregnacyChange: Bool
}

enum TimeLineItemMessage {    
    
    case carepackage(status: String,
                     package: CarePackage,
                     user: User,
                     network: Network)
    
    case updateCarePackage(status: String,
                           package: CarePackage,
                           input: CarePackageItemInput)
    
    case endCarepackage(package: CarePackage, 
                        user: User,
                        reason: Reason)
    
    case homeless(member: Member, isHomeless: Bool)
    
    case generalMemberUpdate(member: Member, 
                             title: String,
                             desc: String,
                             status: String,
                             visible: Bool = true,
                             meta: MetaData? = nil)
    
    case general(memberId: UUID,
                 title: String,
                 desc: String,
                 status: String,
                 visible: Bool = true,
                 meta: MetaData? = nil)
        
    func toTimelineItem() -> TimelineItem {
        switch self {
        case .carepackage(let status, let package, _, let network):
            let statusMsg = status == "removed" ? "removed from" : "added to"
            return TimelineItem(carepackageID: package.id?.uuidString ?? "",
                                status: status,
                                desc: "\(network.name) as been \(statusMsg) social plan \(package.title).",
                                title: "\(network.name) \(statusMsg) \(package.title)",
                                memberId: UUID(uuidString: package.reciever ?? ""),
                                visible: true,
                                meta: MetaData(data: [
                                    "carepackage_id" : package.id?.uuidString.lowercased() ?? ""
                                ]))
            
        case .endCarepackage(let package, _, let reason):
            let reasonMsg = reason.reason.lowercased()
            let memberId = UUID(uuidString: package.reciever ?? "")
            return TimelineItem(carepackageID: package.id?.uuidString ?? "",
                                status: "ended_\(reasonMsg)",
                                desc: "\(package.title) plan has been ended because \(reasonMsg) \(reason.msg).",
                                title: "\(package.title) ended \(reasonMsg)",
                                memberId: memberId,
                                visible: true,
                                meta: MetaData(data: [
                                    "carepackage_id" : package.id?.uuidString.lowercased() ?? "",
                                    "reason_id": reason.id?.uuidString.lowercased() ?? ""
                                ]))
        case .updateCarePackage(let status, let package, let input):
            let isNoShow = status.lowercased() == "noshow"
            let isCancelled = status.lowercased() == "cancel"
            let title = input.formatted(status: status)
            
            var msg = ""
            if let desc = input.item?.desc, !desc.isEmpty {
                msg = desc
            } else if isNoShow {
                msg = "member did not show up for appointment."
            } else if isCancelled {
//                appointment was canceled under Amanda’s Care Package plan.
                msg = "appointment was cancelled under \(package.title)"
            } else {
                msg = "\(package.title) plan has been \(status)."
            }
            var meta = MetaData(data: ["appointment" : "\(input.appointmentEpoc ?? 0)"])
            if let services = input.item?.services, !services.isEmpty {
                meta.data["services"] = services.joined(separator: ",").lowercased()
            }
            return TimelineItem(carepackageID: package.id?.uuidString ?? "",
                                status: status,
                                desc: msg,
                                title: title,
                                memberId: UUID(uuidString: package.reciever ?? ""),
                                visible: true,
                                meta: meta)
        case .homeless(let member, let isHomeless):
            let msg = isHomeless ? "member has been flagged as “currently Homeless”" : "member has been unflagged as “currently homeless”"
            let title = isHomeless ? "Currently Homeless" : "Homeless Status Removed"
            let status = isHomeless ? "homeless" : "homeless_removed"
            return TimelineItem(carepackageID: "",
                                status: status,
                                desc: msg,
                                title: title,
                                memberId: member.id,
                                visible: true,
                                meta: nil)
            
        case .generalMemberUpdate(let member, let title, let desc, let status, let visible, let meta):
            return TimelineItem(carepackageID: "",
                                status: status,
                                desc: desc,
                                title: title,
                                memberId: member.id,
                                visible: visible,
                                meta: meta)
            
        case .general(memberId: let memberId,
                      title: let title,
                      desc: let desc,
                      status: let status,
                      visible: let visible,
                      meta: let meta):
            
            return .init(carepackageID: "",
                         status: status,
                         desc: desc,
                         title: title,
                         memberId: memberId,
                         visible: visible,
                         meta: meta)
        }
    }
}
