//
//  File.swift
//  
//
//  Created by <PERSON> on 2/7/23.
//

import Foundation
import Fluent
import Vapor


// pivot model.
final class TimelineItemService: Model, @unchecked Sendable {
    static let schema = "timeline_item_services"
    
    @ID var id: UUID?
    
    @Parent(key: "item_id")
    var item: TimelineItem
    
    @Parent(key: "service_id")
    var service: Service
    
    @Timestamp(key: "created_at", on: .create)
    var createdAt: Date?
    
    // When this Planet was last updated.
    @Timestamp(key: "updated_at", on: .update)
    var updatedAt: Date?
    
    init() { }
    
    init(id: UUID? = nil, item: TimelineItem, service: Service) throws {
        self.id = id
        self.$item.id = try item.requireID()
        self.$service.id = try service.requireID()
    }
}

struct TimelineItemServiceMigration: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
//        print(database)
        return database.schema(TimelineItemService.schema)
            .id()
            .field("item_id", .uuid, .required)
            .field("service_id", .uuid, .required)
            .unique(on: "item_id", "service_id")
            .field("created_at", .datetime)
            .field("updated_at", .datetime)
            
            .create()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(TimelineItemService.schema).delete()
    }
}
