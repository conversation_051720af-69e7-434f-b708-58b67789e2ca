//
//  File.swift
//  
//
//  Created by <PERSON> on 1/29/23.
//

import Foundation
import Fluent
import Vapor

final class Reason: Model, Content, @unchecked Sendable {
    static let schema = "reasons"
    
    @ID var id: UUID?
    
    @Field(key: "reason")
    var reason: String
    
    @Field(key: "msg")
    var msg: String
    
    @OptionalChild(for: \.$reason)
    var package: CarePackage?
    
    @OptionalChild(for: \.$reason)
    var task: TaskModel?
    
    @OptionalParent(key: "user_id")
    var creator: User?
    
    @Timestamp(key: "created_at", on: .create)
    var createdAt: Date?
    
    // When this Planet was last updated.
    @Timestamp(key: "updated_at", on: .update)
    var updatedAt: Date?
    
    init() { }
    
    init(id:         UUID?    = nil,
         reason:      String,
         msg:     String
         ) {
        self.id          = id
        self.reason       = reason
        self.msg      = msg
    }
}

struct ReasonMigration: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(Reason.schema)
            .id()
            .field("reason",       .string, .required)
            .field("msg",         .string, .required)
        
            .field("user_id",      .uuid,    .references(User.schema,   "id", onDelete: .cascade))
        
            .field("created_at",  .datetime)
            .field("updated_at",  .datetime)
            .create()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(Reason.schema).delete()
    }
}
