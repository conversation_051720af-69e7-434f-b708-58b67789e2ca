//
//  File.swift
//  
//
//  Created by <PERSON> on 1/29/23.
//

import Foundation
import Fluent
import Vapor

final class Network: Model, Content, @unchecked Sendable {
    static let schema = "network"
    
    @ID var id: UUID?
    
    @Field(key: "name")
    var name: String
            
    @Field(key: "types")
    var types: [String]
        
    @Field(key: "status")
    var status: String
        
    @Children(for: \.$network)
    var address: [Address]
            
    @Children(for: \.$network)
    var phones: [PhoneNumber]
    
    @OptionalField(key: "contact")
    var contact: String?
                
    @OptionalField(key: "website")
    var website: String?
    
    @OptionalField(key: "member_book")
    var memberBook: Bool?
    
    @Siblings(through: NetworkService.self, from: \.$network, to: \.$service)
    public var services: [Service]
    
    @Siblings(through: NetworkCarriers.self, from: \.$network, to: \.$carrier)
    public var carriers: [Carrier]
    
    @OptionalParent(key: "org_id")
    var org: Organization?
    
    @OptionalField(key: "scheduler_id")
    var schedulerId: String?
    
//    @OptionalParent(key: "package_item_id")
//    var item: CarePackageItem?
    
    @Timestamp(key: "created_at", on: .create)
    var createdAt: Date?
    
    // When this Planet was last updated.
    @Timestamp(key: "updated_at", on: .update)
    var updatedAt: Date?
    
    init() { }
    
    init(id: UUID? = nil,
         name: String,
         types:[String],
         status:String,
         contact:String? = nil,
         website:String? = nil, 
         memberBook: Bool? = nil,
         schedulerId: String? = nil) {
        self.id       = id
        self.name     = name
        self.types     = types
        self.status   = status
        self.contact  = contact
        self.website  = website
        self.memberBook = memberBook
        self.schedulerId = schedulerId
    }
}


struct NetworkMigration: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(Network.schema)
            .id()
            .field("name",         .string, .required)
            .field("types",       .array(of: .string), .required)
            .field("status",       .string, .required)
            .field("website",      .string)
            .field("contact",      .string)
        
//            .field("package_item_id",     .uuid,   .references(CarePackageItem.schema,   "id", onDelete: .cascade))
            .field("org_id",              .uuid,   .references(Organization.schema,   "id", onDelete: .cascade))
            .field("created_at",   .datetime)
            .field("updated_at",   .datetime)
            .create()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(Network.schema).delete()
    }
}



struct NetworkUpdateSchedulerMigration: AsyncMigration {
    
    func prepare(on database: Database) async throws {
        try await database.schema(Network.schema)
            .field("scheduler_id",  .string)
            .update()
    }
    
    func revert(on database: Database) async throws {
        try await database.schema(Network.schema).update()
    }
    
}



struct NetworkUpdateMigration: AsyncMigration {
    
    func prepare(on database: Database) async throws {
        try await database.schema(Network.schema)
            .field("member_book",  .bool)
            .update()
    }
    
    func revert(on database: Database) async throws {
        try await database.schema(Network.schema).update()
    }
    
}



extension Network: Hashable {
    static func == (lhs: Network, rhs: Network) -> Bool {
        return lhs.id == rhs.id
    }

    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }
}
