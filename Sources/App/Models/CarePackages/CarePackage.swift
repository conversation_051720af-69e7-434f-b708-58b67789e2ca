//
//  File.swift
//  
//
//  Created by <PERSON> on 1/29/23.
//

import Foundation
import Fluent
import Vapor

final class CarePackage: Model, Content, @unchecked Sendable {
    static let schema = "care_packages"
    
    @ID var id: UUID?
    
    @Field(key: "title")
    var title: String
       
    @Field(key: "status")
    var status: String
    
    @Field(key: "type")
    var type: String
    
    @Children(for: \.$carepackage)
    var items: [CarePackageItem]
    
    //navigator
    @OptionalField(key: "user_id")
    public var creator: String?
    
    @OptionalField(key: "member_id")
    public var reciever: String?
        
    @OptionalParent(key: "reason_id")
    var reason: Reason?
        
    
    @Timestamp(key: "started_at", on: .none)
    var startedAt: Date?
    
    @Timestamp(key: "ended_at", on: .none)
    var endedAt: Date?
    
    @OptionalParent(key: "org_id")
    var org: Organization?
    
    
    @Timestamp(key: "created_at", on: .create)
    var createdAt: Date?
        
    @Timestamp(key: "updated_at", on: .update)
    var updatedAt: Date?
    
    init() { }
    
    init(id: UUID? = nil, 
         title: String,
         status:String,
         type:String,
         creator: String? = nil,
         reciever: String? = nil,
         startedAt:Date? = nil,
         endedAt:Date? = nil) {
        self.id        = id
        self.title     = title
        self.status    = status
        self.type      = type
        self.creator   = creator?.lowercased()
        self.reciever  = reciever?.lowercased()
        self.startedAt = startedAt
        self.endedAt   = endedAt
    }
}



struct CarePackageMigration: Migration {
    
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(CarePackage.schema)
            .id()
            .field("title",        .string, .required)
            .field("status",       .string, .required)
            .field("type",         .string, .required)
        
        
            .field("user_id",      .string)
            .field("member_id",    .string)
        
            .field("reason_id",    .uuid,   .references(Reason.schema,   "id", onDelete: .cascade))
            .field("org_id",       .uuid,   .references(Organization.schema,   "id", onDelete: .cascade))            
            
        
            .field("started_at",   .datetime)
            .field("ended_at",     .datetime)
            .field("created_at",   .datetime)
            .field("updated_at",   .datetime)
            .create()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(CarePackage.schema).delete()
    }
}
