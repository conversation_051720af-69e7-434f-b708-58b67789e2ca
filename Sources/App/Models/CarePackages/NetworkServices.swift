//
//  File.swift
//  
//
//  Created by <PERSON> on 1/29/23.
//

import Foundation
import Fluent
import Vapor


// pivot model.
final class NetworkService: Model, @unchecked Sendable {
    static let schema = "network_services"
    
    @ID var id: UUID?
    
    @Parent(key: "network_id")
    var network: Network
    
    @Parent(key: "service_id")
    var service: Service
    
    @Timestamp(key: "created_at", on: .create)
    var createdAt: Date?
    
    // When this Planet was last updated.
    @Timestamp(key: "updated_at", on: .update)
    var updatedAt: Date?
    
    init() { }
    
    init(id: UUID? = nil, network: Network, service: Service) throws {
        self.id = id
        self.$network.id = try network.requireID()
        self.$service.id = try service.requireID()
    }
}

struct NetworkServicesMigration: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
//        print(database)
        return database.schema(NetworkService.schema)
            .id()
            .field("network_id", .uuid, .required)
            .field("service_id", .uuid, .required)
            .unique(on: "network_id", "service_id")
            .field("created_at", .datetime)
            .field("updated_at", .datetime)
            
            .create()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(NetworkService.schema).delete()
    }
}
