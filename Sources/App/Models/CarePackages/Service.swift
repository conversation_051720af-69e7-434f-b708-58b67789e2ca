//
//  File.swift
//  
//
//  Created by <PERSON> on 1/29/23.
//

import Foundation
import Fluent
import Vapor

final class Service: Model, Content, @unchecked Sendable {
    static let schema = "service"
    
    @ID var id: UUID?
    
    @Field(key: "name")
    var name: String
    
    @Field(key: "desc")
    var desc: String?
    
    //template | asigned
    @Field(key: "type")
    var type: String
    
    ///draft | booked | pending | followup | complete
    @Field(key: "status")
    var status: String
    
    //list of services they provide
    @OptionalField(key: "service")
    var service: String?
    
    @Children(for: \.$service)
    var rules: [ServiceRule]
        
    @OptionalChild(for: \.$service)
    var note: Note?
    
    @Siblings(through: NetworkService.self, from: \.$service, to: \.$network)
    public var networks: [Network]
    
    @Siblings(through: TimelineItemService.self, from: \.$service, to: \.$item)
    public var items: [TimelineItem]
    
    @OptionalParent(key: "org_id")
    var org: Organization?
    
    @Timestamp(key: "created_at", on: .create)
    var createdAt: Date?
    
    // When this Planet was last updated.
    @Timestamp(key: "updated_at", on: .update)
    var updatedAt: Date?
    
    init() { }
    
    init(id: UUID? = nil, name: String, desc: String? = nil, service: String? = nil, type:String, status:String) {
        self.id    = id
        self.name  = name
        self.desc  = desc
        self.type  = type
        self.service = service
        self.status = status
    }
    
    func copy() -> Service {
        return Service(name: self.name, desc:self.desc, type: self.type, status: self.status)
    }
}

struct ServiceMigration: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(Service.schema)
            .id()
            .field("name",      .string, .required)
            .field("desc",      .string, .required)
            .field("type",      .string, .required)
            .field("status",    .string, .required)
            .field("service",   .string)
        
            .field("org_id",     .uuid,   .references(Organization.schema,   "id", onDelete: .cascade))
            .field("created_at", .datetime)
            .field("updated_at", .datetime)
            .create()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(Service.schema).delete()
    }
}
