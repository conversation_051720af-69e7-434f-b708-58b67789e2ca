//
//  File.swift
//  
//
//  Created by <PERSON> on 11/29/23.
//

import Foundation
import Fluent
import Vapor


final class TaskModel: Model, Content, @unchecked Sendable {
    static let schema = "tasks"
    
    @ID var id: UUID?
    
    @Field(key: "title")
    var title: String
    
    @Field(key: "type")
    var type: String
    
    @Field(key: "status")
    var status: String    
    
    @Field(key: "remote")
    var remote: Bool
    
    @OptionalChild(for: \.$location)
    var location: Address?
    
    @OptionalField(key: "dueAtEpoc")
    var dueAtEpoc: Int?
    
    @OptionalField(key: "desc")
    var desc: String?
    
    @Children(for: \.$task)
    var attachments: [Attachment]
    
    @Children(for: \.$task)
    var assessments: [Survey]
    
    @Siblings(through: UserTasks.self, from: \.$task, to: \.$user)
    public var completedBy: [User]
    
    @Siblings(through: MemberTasks.self, from: \.$task, to: \.$member)
    public var receivers: [Member]
    
    @OptionalParent(key: "org_id")
    var org: Organization?
    
    @OptionalParent(key: "user_id")
    public var creator: User?
    
    @OptionalParent(key: "assignee_id")
    public var assignee: User?
    
    @OptionalField(key: "meta")
    var meta: MetaData?
    
    @OptionalChild(for: \.$task)
    var taskDetail: TaskDetail?
        
    @OptionalParent(key: "reason_id")
    var reason: Reason?
    
    @OptionalField(key: "urgent")
    var urgent: Bool?
    
    @Timestamp(key: "created_at", on: .create)
    var createdAt: Date?
    
    // When this Planet was last updated.
    @Timestamp(key: "updated_at", on: .update)
    var updatedAt: Date?
    
    init() { }
    
    init(id:     UUID?    = nil,
         title:  String,
         type:   String,
         status: String,
         remote: Bool,
         dueAtEpoc:  Int?,
         desc:   String?,
         meta:  MetaData? = nil,
         urgent: Bool?
         ) {
        self.id      = id
        self.title    = title
        self.type    = type
        self.status = status
        self.remote = remote
        self.dueAtEpoc  = dueAtEpoc
        self.desc   = desc
        self.meta = meta
        self.urgent = urgent
    }
    
    func isAppointmentTypeTask() -> Bool {
        return true
//        self.type.lowercased() == "visit" || self.type.lowercased() == "assessment"
    }
    
    
    func isArchive() -> Bool {
        return status.lowercased() == "archive"
    }
    
    func isComplete() -> Bool {
        return status.lowercased() == "completed"
    }
    
    func sendPush() -> Bool {
        if isArchive() || isComplete() {
            return false
        } else {
            return true
        }
    }
}

struct TaskModelMigration: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(TaskModel.schema)
            .id()
            .field("title", .string, .required)
            .field("type", .string, .required)
            .field("status", .string, .required)
            .field("remote", .bool, .required)
            .field("dueAtEpoc", .int)
            .field("desc", .string)
            .field("meta", .json)
        
            .field("org_id",     .uuid,   .references(Organization.schema,   "id", onDelete: .cascade))
            .field("user_id",     .uuid,   .references(User.schema,   "id", onDelete: .cascade))            
        
            .field("created_at", .datetime)
            .field("updated_at", .datetime)
            .create()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(TaskModel.schema).delete()
    }
}



struct TaskModelUpdateMigration: AsyncMigration {
    
    func prepare(on database: Database) async throws {
        try await database.schema(TaskModel.schema)
            .field("assignee_id",     .uuid,   .references(User.schema,   "id", onDelete: .cascade))
            .update()
    }
    
    func revert(on database: Database) async throws {
        try await database.schema(TaskModel.schema).update()
    }
    
}

struct TaskModelUpdate2Migration: AsyncMigration {
    func prepare(on database: Database) async throws {
        try await database.schema(TaskModel.schema)
            .field("reason_id",    .uuid,   .references(Reason.schema,   "id", onDelete: .cascade))
            .update()
    }
    
    func revert(on database: Database) async throws {
        try await database.schema(TaskModel.schema).update()
    }
}

struct TaskModelUpdateTasksMigration: AsyncMigration {
    func prepare(on database: Database) async throws {
        try await database.schema(TaskModel.schema)
            .field("urgent",    .bool)
            .update()
    }
    
    func revert(on database: Database) async throws {
        try await database.schema(TaskModel.schema).update()
    }
}
