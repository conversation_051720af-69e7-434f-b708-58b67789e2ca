//
//  File.swift
//  
//
//  Created by <PERSON> on 1/30/23.
//

import Foundation
import Fluent
import Vapor


struct OrgMetaData: Codable {
    var constants: [String:[PickerItem]]
    var features: [String]
    var billing: [String: String]? = nil
}


final class Organization: Model, Content, @unchecked Sendable {
    static let schema = "organizations"
    
    @ID var id: UUID?
    
    @Field(key: "title")
    var title: String
    
    /// mobile | standard | hospital
    @Field(key: "type")
    var type: String
    
    @OptionalField(key: "desc")
    var desc: String?
    
    @OptionalField(key: "url")
    var url: String?
    
    @Children(for: \.$org)
    var address: [Address]
    
    @Children(for: \.$org)
    var phones: [PhoneNumber]
    
    @Children(for: \.$org)
    var attachments: [Attachment]
    
    @Children(for: \.$org)
    var members: [Member]
    
    @Children(for: \.$org)
    var users: [User]
    
    @Children(for: \.$org)
    var networks: [Network]
    
    @Children(for: \.$org)
    var services: [Service]
    
    @Children(for: \.$org)
    var packages: [CarePackage]
    
    @Children(for: \.$org)
    var teams: [Team]
    
    @Children(for: \.$org)
    var chats: [Chat]
    
    @Children(for: \.$org)
    var memberChats: [MemberChat]
    
    @Children(for: \.$org)
    var appointments: [Appointment]
    
    @Children(for: \.$org)
    var households: [Household]
    
    @Children(for: \.$org)
    var tasks: [TaskModel]
    
    @OptionalField(key: "meta")
    var meta: OrgMetaData?
    
    @Timestamp(key: "created_at", on: .create)
    var createdAt: Date?
    
    // When this Planet was last updated.
    @Timestamp(key: "updated_at", on: .update)
    var updatedAt: Date?
    
    init() { }
    
    init(id:        UUID?    = nil,
         title:     String,
         type:      String,
         desc:      String? = nil,
         url:       String? = nil,
         meta:      OrgMetaData? = nil
         ) {
        self.id         = id
        self.title      = title
        self.type       = type
        self.desc       = desc
        self.url        = url
        self.meta       = meta
    }
}

struct OrganizationMigration: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(Organization.schema)
            .id()
            .field("title",      .string, .required)
            .field("type",       .string, .required)
            .field("desc",       .string)
            .field("url",        .string)
        
            .field("created_at", .datetime)
            .field("updated_at", .datetime)
            .create()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(Organization.schema).delete()
    }
}



struct OrganizationUpdateMigration: AsyncMigration {
    
    func prepare(on database: Database) async throws {
        try await database.schema(Organization.schema)
            .field("roles",       .array(of: .string))
            .update()
    }
    
    func revert(on database: Database) async throws {
        try await database.schema(Organization.schema).update()
    }
    
}


struct OrganizationMetaUpdateMigration: AsyncMigration {
    
    func prepare(on database: Database) async throws {
        try await database.schema(Organization.schema)
            .field("meta",       .json)
            .update()
    }
    
    func revert(on database: Database) async throws {
        try await database.schema(Organization.schema).update()
    }
}
