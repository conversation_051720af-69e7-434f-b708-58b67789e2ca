//
//  File.swift
//  
//
//  Created by <PERSON> on 3/18/23.
//

import Foundation
import Fluent
import Vapor

final class Tag: Model, Content, @unchecked Sendable {
    static let schema = "tags"
    
    @ID var id: UUID?
    
    @Field(key: "name")
    var name: String
    
    @Field(key: "key")
    var key: String
    
    @Field(key: "color")
    var color: String
    
    @OptionalParent(key: "note_id")
    var note: Note?
    
    @OptionalParent(key: "carrier_id")
    var carrier: Carrier?
    
    @OptionalParent(key: "member_id")
    var member: Member?
    
    @OptionalParent(key: "content")
    var content: WellupContent?    
        
    @Timestamp(key: "created_at", on: .create)
    var createdAt: Date?
    
    // When this Planet was last updated.
    @Timestamp(key: "updated_at", on: .update)
    var updatedAt: Date?
    
    init() { }
    
    init(id:       UUID?    = nil,
         name:     String,
         key:      String,
         color:    String
         ) {
        self.id          = id
        self.name        = name
        self.key         = key
        self.color       = color
    }
}

struct TagMigration: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(Tag.schema)
            .id()
            .field("name",       .string, .required)
            .field("key",        .string, .required)
            .field("color",      .string, .required)
            
            .field("note_id",      .uuid,   .references(Note.schema,   "id", onDelete: .cascade))
            .field("created_at", .datetime)
            .field("updated_at", .datetime)
            .create()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(Tag.schema).delete()
    }
}

struct TagMemberUpdateMigration: AsyncMigration {
    
    func prepare(on database: Database) async throws {
        try await database.schema(Tag.schema)
            .field("member_id",      .uuid,   .references(Member.schema,   "id", onDelete: .cascade))
            .update()
    }
    
    func revert(on database: Database) async throws {
        try await database.schema(Tag.schema).update()
    }
}


struct TagUpdateMigration: AsyncMigration {
    
    func prepare(on database: Database) async throws {
        try await database.schema(Tag.schema)
            .field("carrier_id",      .uuid,   .references(Tag.schema,   "id", onDelete: .cascade))
            .update()
    }
    
    func revert(on database: Database) async throws {
        try await database.schema(Tag.schema).update()
    }
}


struct TagUpdateWellupContentMigration: AsyncMigration {
    
    func prepare(on database: Database) async throws {
        try await database.schema(Tag.schema)
            .field("content",    .uuid,   .references(WellupContent.schema, "id", onDelete: .cascade))
            .update()
    }
    
    func revert(on database: Database) async throws {
        try await database.schema(Tag.schema).update()
    }
    
}
