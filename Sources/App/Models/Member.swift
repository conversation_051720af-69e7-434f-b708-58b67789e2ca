//
//  File.swift
//  
//
//  Created by <PERSON> on 1/29/23.
//

import Foundation
import Fluent
import Vapor

final class Member: Model, Content, @unchecked Sendable {
    static let schema = "members"
    
    @ID var id: UUID?

    @OptionalField(key: "auth")
    var auth: String?
    
    @Field(key: "email")
    var email: String
    
    @Field(key: "firstName")
    var firstName: String
    
    @OptionalField(key: "middleName")
    var middleName: String?
    
    @Field(key: "lastName")
    var lastName: String
            
    @Field(key: "type")
    var type: String
    
    @OptionalField(key: "color")
    var color: String?
        
    @Field(key: "roles")
    var roles: [String]
    
    @Field(key: "dob")
    var dob: String
    
    @OptionalField(key: "gender")
    var gender: String?
    
    @OptionalField(key: "ethnicity")
    var ethnicity: String?
    
    @OptionalField(key: "sexualIdentity")
    var sexualIdentity: String?
    
    @OptionalField(key: "genderIdentity")
    var genderIdentity: String?
    
    @OptionalField(key: "pronouns")
    var pronouns: String?
    
    @OptionalField(key: "lang")
    var lang: String?
    
    @OptionalField(key: "referredBy")
    var referredBy: String?
    
    @OptionalField(key: "lastAt")
    var lastAt: String?
    
    @OptionalField(key: "status")
    var status: String?
    
    @OptionalField(key: "score")
    var score: String?
    
    @Children(for: \.$member)
    var attachments: [Attachment]
    
    @Children(for: \.$member)
    var address: [Address]
    
    @Children(for: \.$member)
    var phones: [PhoneNumber]
    
    @Children(for: \.$member)
    var notes: [Note]
    
    @Children(for: \.$member)
    var schools: [School]
    
    @OptionalChild(for: \.$headOfHouse)
    var headOfHouse: Household?
    
    @OptionalChild(for: \.$member)
    var note: Note?            
            
    @OptionalChild(for: \.$member)
    var appointment: Appointment?
    
    @OptionalParent(key: "org_id")
    var org: Organization?
    
    @Siblings(through: HouseholdMembers.self, from: \.$member, to: \.$household)
    public var households: [Household]
    
    @OptionalField(key: "ref_id")
    var refId: String?
    
    @OptionalField(key: "pregnancy_status")
    var pregnancyStatus: String?
    
    @OptionalField(key: "delivery_date")
    var deliveryDate: String?
    
    @OptionalField(key: "military")
    var military: String?
    
    @Children(for: \.$member)
    var tags: [Tag]
    
    @OptionalField(key: "enrolled_on")
    var enrolledOn: String?
    
    @OptionalField(key: "unenrolled_date")
    var unenrolledDate: String?
    
    @OptionalField(key: "last_contact_on")
    var lastContact: String?
    
    @OptionalField(key: "homeless")
    var homeless: Bool?
    
    @Children(for: \.$member)
    var memberStatus: [MemberStatus]

    @Children(for: \.$member)
    var diagnoses: [Diagnosis]

    @Children(for: \.$member)
    var medications: [Medication]

    @Siblings(through: MemberProblems.self, from: \.$member, to: \.$problem)
    var problems: [Problem]

    @Children(for: \.$member)
    var programs: [Program]

    @Children(for: \.$member)
    var associatedPersons: [AssociatedPerson]

    @OptionalField(key: "meta")
    var meta: MetaData?
    
    @Timestamp(key: "created_at", on: .create)
    var createdAt: Date?
    
    // When this Planet was last updated.
    @Timestamp(key: "updated_at", on: .update)
    var updatedAt: Date?
    
    init() { }
    
    init(id:               UUID?      = nil,
         auth:             String?    = nil,
         email:            String,
         firstName:        String,
         middleName:       String?    = nil,
         lastName:         String,
         type:             String,
         roles:           [String],
         dob:              String,
         color:            String?    = nil,
         gender:           String?    = nil,
         ethnicity:        String?    = nil,
         sexualIdentity:   String?    = nil,
         genderIdentity:   String?    = nil,
         pronouns:         String?    = nil,
         lang:             String?    = nil,
         referredBy:       String?    = nil,
         lastAt:           String?    = nil,
         status:           String?    = nil,
         score:            String?    = nil,
         refId:            String?    = nil,
         pregnancyStatus:  String?    = nil,
         deliveryDate:     String?    = nil,
         military:         String?    = nil,
         enrolledOn:       String?    = nil,
         unenrolledDate:   String?    = nil,
         lastContact:      String?    = nil,
         homeless:         Bool?      = nil,
         meta:             MetaData?  = nil
         ) {
        self.id              = id
        self.auth            = auth
        self.email           = email
        self.firstName       = firstName
        self.middleName      = middleName
        self.lastName        = lastName
        self.type            = type
        self.roles           = roles
        self.dob             = dob
        self.color           = color
        self.gender          = gender
        self.ethnicity       = ethnicity
        self.sexualIdentity  = sexualIdentity
        self.genderIdentity  = genderIdentity
        self.pronouns        = pronouns
        self.lang            = lang
        self.referredBy      = referredBy
        self.lastAt          = lastAt
        self.status          = status
        self.score           = score
        self.refId           = refId?.lowercased()
        self.pregnancyStatus = pregnancyStatus
        self.deliveryDate    = deliveryDate
        self.military        = military
        self.enrolledOn      = enrolledOn
        self.unenrolledDate  = unenrolledDate
        self.lastContact     = lastContact
        self.homeless        = homeless
        self.meta            = meta
    }
    
    func hasProfile() -> Bool {
        return !self.profileURL().isEmpty
    }
    
    func profileURL() -> String {
        let defaultImg = "https://res.cloudinary.com/cg1-solutions/image/upload/ar_1:1,b_rgb:262c35,bo_5px_solid_rgb:e7e7e7,c_fill,g_auto,r_max,w_1000/v1683330922/donahealth/staging/profile/profile1683330922.369087.jpg"
        
        let attachment = attachments.filter({$0.kind.lowercased() == "profile"}).last
        let url = attachment?.url ?? defaultImg
        return url
    }
    
    func pregnacyStatusChanged(status: String) -> Bool {
        return pregnancyStatus != status
    }
    
    func homelessStatusChanged(status: Bool) -> Bool {
        return status != self.homeless
    }
    
    var schedulerMemberId: String? {
        meta?.schedulerMemberId
    }
    
    var hasSchedulerMemberId: Bool {
        schedulerMemberId != nil
    }
}

struct MemberMigration: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(Member.schema)
            .id()
            .field("auth",            .string)
            .field("email",           .string, .required)
            .field("firstName",       .string, .required)
            .field("middleName",      .string)
            .field("lastName",        .string, .required)
            .field("type",            .string, .required)
            .field("roles",           .array(of: .string), .required)
            .field("dob",             .string, .required)
            .field("color",           .string)
            .field("gender",          .string)
            .field("ethnicity",       .string)
            .field("sexualIdentity",  .string)
            .field("genderIdentity",  .string)
            .field("pronouns",        .string)
            .field("lang",            .string)
            .field("referredBy",      .string)
            .field("lastAt",          .string)
            .field("status",          .string)
            .field("score",           .string)
        
        
            .field("org_id",     .uuid,   .references(Organization.schema,   "id", onDelete: .cascade))
        
            .field("created_at",      .datetime)
            .field("updated_at",      .datetime)
            .create()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(Member.schema).delete()
    }
}


// MARK: - Diagnosis Model
final class Diagnosis: Model, @unchecked Sendable {
    static let schema = "diagnoses"

    @ID var id: UUID?
    @Parent(key: "member_id") var member: Member

    @Field(key: "icd_code") var icdCode: String?
    @Field(key: "description") var description: String
    @Field(key: "clinical_note") var clinicalNote: String?
    @Field(key: "status") var status: String // active | resolved | inactive
    @Field(key: "date_identified") var dateIdentified: Date
    @Field(key: "source") var source: String // EHR import | self-reported | care team
    @Field(key: "confirmed_by") var confirmedBy: String?

    @Timestamp(key: "created_at", on: .create) var createdAt: Date?
    @Timestamp(key: "updated_at", on: .update) var updatedAt: Date?
}

// MARK: - Diagnosis Content Conformance
extension Diagnosis: Content {
    enum CodingKeys: String, CodingKey {
        case id
        case icdCode = "icd_code"
        case description
        case clinicalNote = "clinical_note"
        case status
        case dateIdentified = "date_identified"
        case source
        case confirmedBy = "confirmed_by"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
        // Note: member is excluded from coding keys to prevent decoding issues
    }
}

struct CreateDiagnosis: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        database.schema("diagnoses")
            .id()
            .field("member_id", .uuid, .required, .references("members", "id", onDelete: .cascade))
            .field("icd_code", .string)
            .field("description", .string, .required)
            .field("clinical_note", .string)
            .field("status", .string, .required, .sql(.default("active")))
            .field("date_identified", .date, .required)
            .field("source", .string, .required)
            .field("confirmed_by", .string)
            .field("created_at", .datetime)
            .field("updated_at", .datetime)
            .create()
    }

    func revert(on database: Database) -> EventLoopFuture<Void> {
        database.schema("diagnoses").delete()
    }
}

// MARK: - Medication Model
final class Medication: Model, @unchecked Sendable {
    static let schema = "medications"

    @ID var id: UUID?
    @Parent(key: "member_id") var member: Member

    @Field(key: "medication_name") var medicationName: String
    @Field(key: "rx_norm_code") var rxNormCode: String?
    @Field(key: "dosage") var dosage: String
    @Field(key: "route") var route: String
    @Field(key: "frequency") var frequency: String
    @Field(key: "start_date") var startDate: Date
    @Field(key: "end_date") var endDate: Date?
    @Field(key: "prescribed_by") var prescribedBy: String
    @Field(key: "status") var status: String // active | discontinued
    @Field(key: "adherence_notes") var adherenceNotes: String?
    @Field(key: "source") var source: String // EHR import | self-reported | care team
    @Field(key: "medication_type") var medicationType: String // prescribed | OTC | supplement

    @Timestamp(key: "created_at", on: .create) var createdAt: Date?
    @Timestamp(key: "updated_at", on: .update) var updatedAt: Date?
}

// MARK: - Medication Content Conformance
extension Medication: Content {
    enum CodingKeys: String, CodingKey {
        case id
        case medicationName = "medication_name"
        case rxNormCode = "rx_norm_code"
        case dosage
        case route
        case frequency
        case startDate = "start_date"
        case endDate = "end_date"
        case prescribedBy = "prescribed_by"
        case status
        case adherenceNotes = "adherence_notes"
        case source
        case medicationType = "medication_type"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
        // Note: member is excluded from coding keys to prevent decoding issues
    }
}

struct CreateMedication: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        database.schema("medications")
            .id()
            .field("member_id", .uuid, .required, .references("members", "id", onDelete: .cascade))
            .field("medication_name", .string, .required)
            .field("rx_norm_code", .string)
            .field("dosage", .string, .required)
            .field("route", .string, .required)
            .field("frequency", .string, .required)
            .field("start_date", .date, .required)
            .field("end_date", .date)
            .field("prescribed_by", .string, .required)
            .field("status", .string, .required, .sql(.default("active")))
            .field("adherence_notes", .string)
            .field("source", .string, .required)
            .field("medication_type", .string, .required, .sql(.default("prescribed")))
            .field("created_at", .datetime)
            .field("updated_at", .datetime)
            .create()
    }

    func revert(on database: Database) -> EventLoopFuture<Void> {
        database.schema("medications").delete()
    }
}

extension Member {
    func smsPhone() -> String? {
        guard let phone = self.phones.filter({$0.label.lowercased() == "main"}).last else { return nil }
        return phone.smsNumber()
    }

    func fullName() -> String {
        return "\(firstName) \(lastName)"
    }
}


struct MemberMigrationUpdate: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(Member.schema)
            .field("ref_id",        .string)
            .update()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(Member.schema).delete()
    }
}

struct MemberDeliveryMigrationUpdate: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(Member.schema)
            .field("delivery_date",        .string)
            .update()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(Member.schema).delete()
    }
}


struct MemberPregnancyStatusMigrationUpdate: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(Member.schema)
            .field("pregnancy_status", .string)
            .update()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(Member.schema).delete()
    }
}


struct MillitaryStatusMigrationUpdate: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(Member.schema)
            .field("military", .string)
            .update()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(Member.schema).delete()
    }
}

struct EmrollmentStatusMigrationUpdate: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(Member.schema)
            .field("enrolled_on", .string)
            .field("unenrolled_date", .string)
            .update()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(Member.schema).delete()
    }
}


struct LastContactMigrationUpdate: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(Member.schema)
            .field("last_contact_on", .string)
            .field("homeless", .bool)
            .update()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(Member.schema).delete()
    }
}


struct MetaMigrationUpdate: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(Member.schema)
            .field("meta", .json)
            .update()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(Member.schema).delete()
    }
}
