//
//  AddTitleToCarePlan.swift
//
//
//  Created by Augment Agent on 7/9/25.
//

import Foundation
import Fluent
import Vapor
import SQLKit

struct AddTitleToCarePlan: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        // Step 1: Add title field as optional
        return database.schema("care_plans")
            .field("title", .string)
            .update()
            .flatMap { (_: Void) -> EventLoopFuture<Void> in
                // Step 2: Update existing records with default title
                guard let sql = database as? SQLDatabase else {
                    return database.eventLoop.makeFailedFuture(Abort(.internalServerError, reason: "Database does not support SQL"))
                }

                return sql.raw("UPDATE care_plans SET title = 'Care Plan' WHERE title IS NULL")
                    .run()
            }
            .flatMap { (_: Void) -> EventLoopFuture<Void> in
                // Step 3: Make title field required
                guard let sql = database as? SQLDatabase else {
                    return database.eventLoop.makeFailedFuture(Abort(.internalServerError, reason: "Database does not support SQL"))
                }

                return sql.raw("ALTER TABLE care_plans ALTER COLUMN title SET NOT NULL")
                    .run()
            }
    }

    func revert(on database: Database) -> EventLoopFuture<Void> {
        database.schema("care_plans")
            .deleteField("title")
            .update()
    }
}
