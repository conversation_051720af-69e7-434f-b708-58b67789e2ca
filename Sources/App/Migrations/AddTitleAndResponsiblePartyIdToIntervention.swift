//
//  AddTitleAndResponsiblePartyIdToIntervention.swift
//
//
//  Created by Augment Agent on 7/18/25.
//

import Foundation
import Fluent
import Vapor

struct AddTitleAndResponsiblePartyIdToIntervention: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        return database.schema("interventions")
            .field("title", .string)
            .field("responsible_party_id", .string)
            .field("status", .string)
            .update()
    }

    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema("interventions")
            .deleteField("title")
            .deleteField("responsible_party_id")
            .deleteField("status")
            .update()
    }
}
