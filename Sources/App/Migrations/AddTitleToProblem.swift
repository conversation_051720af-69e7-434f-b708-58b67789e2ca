//
//  AddTitleToProblem.swift
//
//
//  Created by Augment Agent on 7/18/25.
//

import Foundation
import Fluent
import Vapor

struct AddTitleToProblem: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        return database.schema("problems")
            .field("title", .string)
            .update()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema("problems")
            .deleteField("title")
            .update()
    }
}
