import Fluent

struct MoveOutcomeFieldsToReviewPeriod: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        // This migration has already been applied manually or the schema is already correct
        // The outcome_status and outcome_description fields are already in review_periods table
        // and not in program_tasks table, which is the desired state
        return database.eventLoop.makeSucceededFuture(())
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        // Add outcome fields back to program_tasks table
        return database.schema("program_tasks")
            .field("outcome_status", .string, .required)
            .field("outcome_description", .string, .required)
            .update()
    }
}
