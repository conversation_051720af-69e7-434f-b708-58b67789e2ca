//
//  AddAppointmentNoteToCarePlanService.swift
//
//
//  Created by Augment Agent on 7/25/25.
//

import Foundation
import Fluent
import Vapor

struct AddAppointmentNoteToCarePlanService: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        database.schema("care_plan_services")
            .field("appointment_note", .string)
            .update()
    }

    func revert(on database: Database) -> EventLoopFuture<Void> {
        database.schema("care_plan_services")
            .deleteField("appointment_note")
            .update()
    }
}
