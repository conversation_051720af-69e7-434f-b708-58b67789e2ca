//
//  AddPasswordResetToAuthUser.swift
//  
//
//  Created by <PERSON> on 6/29/25.
//

import Foundation
import Fluent
import Vapor

struct AddPasswordResetToAuthUser: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        database.schema(AuthUser.schema)
            .field("reset_token", .string)
            .field("reset_token_expires_at", .datetime)
            .update()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        database.schema(AuthUser.schema)
            .deleteField("reset_token")
            .deleteField("reset_token_expires_at")
            .update()
    }
}
