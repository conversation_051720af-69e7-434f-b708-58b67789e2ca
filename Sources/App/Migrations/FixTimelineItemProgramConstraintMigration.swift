//
//  FixTimelineItemProgramConstraintMigration.swift
//
//
//  Created by Augment Agent on 6/27/25.
//

import Foundation
import Fluent
import SQLKit
import Vapor

struct FixTimelineItemProgramConstraintMigration: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        guard let sql = database as? SQLDatabase else {
            return database.eventLoop.makeFailedFuture(Abort(.internalServerError, reason: "Database does not support SQL"))
        }

        // Drop the existing constraint and recreate it with CASCADE
        return sql.raw("ALTER TABLE timeline_item DROP CONSTRAINT IF EXISTS timeline_item_program_id_fkey")
            .run()
            .flatMap { _ in
                sql.raw("ALTER TABLE timeline_item ADD CONSTRAINT timeline_item_program_id_fkey FOREIGN KEY (program_id) REFERENCES programs(id) ON DELETE CASCADE")
                    .run()
            }
    }

    func revert(on database: Database) -> EventLoopFuture<Void> {
        guard let sql = database as? SQLDatabase else {
            return database.eventLoop.makeFailedFuture(Abort(.internalServerError, reason: "Database does not support SQL"))
        }

        // Revert back to NO ACTION constraint
        return sql.raw("ALTER TABLE timeline_item DROP CONSTRAINT IF EXISTS timeline_item_program_id_fkey")
            .run()
            .flatMap { _ in
                sql.raw("ALTER TABLE timeline_item ADD CONSTRAINT timeline_item_program_id_fkey FOREIGN KEY (program_id) REFERENCES programs(id) ON DELETE NO ACTION")
                    .run()
            }
    }
}
