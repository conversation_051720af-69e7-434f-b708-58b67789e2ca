//
//  AddRefIdToCarePlanService.swift
//
//
//  Created by Augment Agent on 7/15/25.
//

import Foundation
import Fluent
import Vapor

struct AddRefIdToCarePlanService: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        database.schema("care_plan_services")
            .field("ref_id", .string)
            .update()
    }

    func revert(on database: Database) -> EventLoopFuture<Void> {
        database.schema("care_plan_services")
            .deleteField("ref_id")
            .update()
    }
}
