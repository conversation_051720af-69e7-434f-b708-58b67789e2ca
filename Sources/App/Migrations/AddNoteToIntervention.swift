//
//  AddNoteToIntervention.swift
//
//
//  Created by Augment Agent on 7/23/25.
//

import Foundation
import Fluent
import Vapor

struct AddNoteToIntervention: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        return database.schema("interventions")
            .field("note", .string)
            .update()
    }

    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema("interventions")
            .deleteField("note")
            .update()
    }
}
