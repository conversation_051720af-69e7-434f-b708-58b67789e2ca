//
//  AddTitleToCarePlanReview.swift
//
//
//  Created by Augment Agent on 7/15/25.
//

import Foundation
import Fluent
import Vapor

struct AddTitleToCarePlanReview: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        database.schema("care_plan_reviews")
            .field("title", .string)
            .update()
    }

    func revert(on database: Database) -> EventLoopFuture<Void> {
        database.schema("care_plan_reviews")
            .deleteField("title")
            .update()
    }
}
