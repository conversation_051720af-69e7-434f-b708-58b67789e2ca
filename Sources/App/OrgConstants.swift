//
//  File.swift
//  
//
//  Created by <PERSON> on 2/2/24.
//

import Foundation
import Vapor

struct OrgConstants: Content {
    var orgId: String
    var constants: [String : [PickerItem]]
}

struct ConstantsManager: Content {
    static let store: [OrgConstants] = [OrgConstants(orgId: "fc312a94-9382-4909-8e08-7c8076ff9f68", constants: empowered)]
}

let empowered = [
    "appointmentKinds":  [
        PickerItem(title: "virtual", key: "virtual"),
        PickerItem(title: "appointment", key: "appointment")
    ],
    
    "appointmentStatus":  [
        Picker<PERSON><PERSON>(title: "booked", key: "booked"),
        PickerItem(title: "complete", key: "complete"),
        Picker<PERSON><PERSON>(title: "in session", key: "in_session"),
        Picker<PERSON><PERSON>(title: "canceled", key: "canceled"),
    ]
]
