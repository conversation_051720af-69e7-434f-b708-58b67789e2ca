//
//  File.swift
//  hmbl-core
//
//  Created by <PERSON> on 3/9/25.
//

import Foundation
import Fluent
import FluentPostgresDriver
import Vapor


struct DatabaseConfiguration {
    static func configure(_ app: Application) throws {
        
        guard let url: String = isProduction ? Environment.get("PROD_DATABASE_URL") : Environment.get("STG_DATABASE_URL"),
        let databaseUrl = URL(string: url) else {
            throw NetworkError.error(type: .badGateway, msg: "Missing database url")
        }
        
        guard let comp = URLComponents(url: databaseUrl, resolvingAgainstBaseURL: true), let username = comp.user else {
            throw URLError(.badURL, userInfo: [NSURLErrorFailingURLErrorKey: databaseUrl, NSURLErrorFailingURLStringErrorKey: databaseUrl.absoluteString])
        }
        
        guard let hostname = comp.host, !hostname.isEmpty else {
            throw URLError(.badURL, userInfo: [NSURLErrorFailingURLErrorKey: databaseUrl, NSURLErrorFailingURLStringErrorKey: databaseUrl.absoluteString])
        }
        
        var tlsConfig = TLSConfiguration.makeClientConfiguration()
            tlsConfig.certificateVerification = .none // Disable certificate verification
                
        guard let postgresConfig = try? SQLPostgresConfiguration(hostname: hostname,
                                                           port: comp.port ?? 5432,
                                                           username: username,
                                                           password: comp.password,
                                                           database: databaseUrl.lastPathComponent.isEmpty ? nil : databaseUrl.lastPathComponent,
                                                                 tls: .require(.init(configuration: tlsConfig))) else {
            throw NetworkError.error(type: .badGateway, msg: "Could not configur DB.")
        }
        
        app.databases.use(.postgres( configuration: postgresConfig,
                                     maxConnectionsPerEventLoop: 20,
                                     connectionPoolTimeout: .seconds(120)), as: .psql)
    }
}
