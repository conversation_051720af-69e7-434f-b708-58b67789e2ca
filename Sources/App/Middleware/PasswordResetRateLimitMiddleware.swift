//
//  PasswordResetRateLimitMiddleware.swift
//  
//
//  Created by <PERSON> on 6/29/25.
//

import Foundation
import Vapor
import Fluent

// MARK: - Rate Limit Tracking Model
final class PasswordResetAttempt: Model, Content, @unchecked Sendable {
    static let schema = "password_reset_attempts"
    
    @ID var id: UUID?
    
    @Field(key: "ip_address")
    var ipAddress: String
    
    @Field(key: "username")
    var username: String
    
    @Field(key: "attempt_count")
    var attemptCount: Int
    
    @Timestamp(key: "created_at", on: .create)
    var createdAt: Date?
    
    @Timestamp(key: "updated_at", on: .update)
    var updatedAt: Date?
    
    init() {}
    
    init(id: UUID? = nil, ipAddress: String, username: String, attemptCount: Int = 1) {
        self.id = id
        self.ipAddress = ipAddress
        self.username = username
        self.attemptCount = attemptCount
    }
}

// MARK: - Migration
struct CreatePasswordResetAttemptsMigration: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        database.schema(PasswordResetAttempt.schema)
            .id()
            .field("ip_address", .string, .required)
            .field("username", .string, .required)
            .field("attempt_count", .int, .required)
            .field("created_at", .datetime, .required)
            .field("updated_at", .datetime, .required)
            .create()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        database.schema(PasswordResetAttempt.schema).delete()
    }
}

// MARK: - Rate Limiting Middleware
struct PasswordResetRateLimitMiddleware: AsyncMiddleware {
    
    private let maxAttemptsPerHour: Int
    private let maxAttemptsPerDay: Int
    
    init(maxAttemptsPerHour: Int = 5, maxAttemptsPerDay: Int = 10) {
        self.maxAttemptsPerHour = maxAttemptsPerHour
        self.maxAttemptsPerDay = maxAttemptsPerDay
    }
    
    func respond(to request: Request, chainingTo next: AsyncResponder) async throws -> Response {
        // Only apply rate limiting to POST requests for password reset
        guard request.method == .POST,
              request.url.path.contains("password-reset/forgot") else {
            return try await next.respond(to: request)
        }
        
        let clientIP = getClientIP(from: request)
        
        // Check rate limits before processing
        try await checkRateLimits(request: request, clientIP: clientIP)
        
        // Process the request
        let response = try await next.respond(to: request)
        
        // Record the attempt after processing (only if it's a valid request)
        if response.status == .seeOther || response.status == .ok {
            try await recordAttempt(request: request, clientIP: clientIP)
        }
        
        return response
    }
    
    private func getClientIP(from request: Request) -> String {
        // Check for forwarded IP headers (for load balancers/proxies)
        if let forwardedFor = request.headers.first(name: "X-Forwarded-For") {
            return forwardedFor.split(separator: ",").first?.trimmingCharacters(in: .whitespaces) ?? request.remoteAddress?.hostname ?? "unknown"
        }
        
        if let realIP = request.headers.first(name: "X-Real-IP") {
            return realIP
        }
        
        return request.remoteAddress?.hostname ?? "unknown"
    }
    
    private func checkRateLimits(request: Request, clientIP: String) async throws {
        // Extract username from request body for more granular rate limiting
        let username: String
        do {
            let forgotPasswordRequest = try request.content.decode(ForgotPasswordRequest.self)
            username = forgotPasswordRequest.username.lowercased()
        } catch {
            username = "unknown"
        }

        let now = Date()
        let oneHourAgo = Calendar.current.date(byAdding: .hour, value: -1, to: now) ?? now
        let oneDayAgo = Calendar.current.date(byAdding: .day, value: -1, to: now) ?? now

        // Check hourly limit - filter by both IP and username for more granular control
        let hourlyAttempts = try await PasswordResetAttempt.query(on: request.db)
            .filter(\.$ipAddress == clientIP)
            .filter(\.$username == username)
            .filter(\.$createdAt >= oneHourAgo)
            .all()

        let hourlyCount = hourlyAttempts.reduce(0) { $0 + $1.attemptCount }

        if hourlyCount >= maxAttemptsPerHour {
            let cloudwatch = CloudWatchLogger(req: request, logGroupName: .actions)
            let logMessage = CloudWatchLogMessage.send(msg: .security(msg: "Rate limit exceeded (hourly) for IP: \(clientIP), username: \(username)"))
            _ = try await cloudwatch.putLog(message: logMessage, on: request.eventLoop).get()

            throw Abort(.tooManyRequests, reason: "Too many password reset attempts for this account. Please try again in an hour.")
        }

        // Check daily limit - filter by both IP and username for more granular control
        let dailyAttempts = try await PasswordResetAttempt.query(on: request.db)
            .filter(\.$ipAddress == clientIP)
            .filter(\.$username == username)
            .filter(\.$createdAt >= oneDayAgo)
            .all()

        let dailyCount = dailyAttempts.reduce(0) { $0 + $1.attemptCount }

        if dailyCount >= maxAttemptsPerDay {
            let cloudwatch = CloudWatchLogger(req: request, logGroupName: .actions)
            let logMessage = CloudWatchLogMessage.send(msg: .security(msg: "Rate limit exceeded (daily) for IP: \(clientIP), username: \(username)"))
            _ = try await cloudwatch.putLog(message: logMessage, on: request.eventLoop).get()

            throw Abort(.tooManyRequests, reason: "Daily password reset limit exceeded for this account. Please try again tomorrow.")
        }
    }
    
    private func recordAttempt(request: Request, clientIP: String) async throws {
        // Extract username from request body if available
        let username: String
        do {
            let forgotPasswordRequest = try request.content.decode(ForgotPasswordRequest.self)
            username = forgotPasswordRequest.username.lowercased()
        } catch {
            username = "unknown"
        }
        
        let now = Date()
        let oneHourAgo = Calendar.current.date(byAdding: .hour, value: -1, to: now) ?? now
        
        // Check if there's an existing attempt record for this IP in the last hour
        if let existingAttempt = try await PasswordResetAttempt.query(on: request.db)
            .filter(\.$ipAddress == clientIP)
            .filter(\.$createdAt >= oneHourAgo)
            .first() {
            
            existingAttempt.attemptCount += 1
            existingAttempt.username = username // Update with latest username
            try await existingAttempt.save(on: request.db)
        } else {
            // Create new attempt record
            let newAttempt = PasswordResetAttempt(ipAddress: clientIP, username: username)
            try await newAttempt.save(on: request.db)
        }
        
        // Clean up old records (older than 24 hours)
        let oneDayAgo = Calendar.current.date(byAdding: .day, value: -1, to: now) ?? now
        try await PasswordResetAttempt.query(on: request.db)
            .filter(\.$createdAt < oneDayAgo)
            .delete()
    }
}
