#!/bin/bash

# Quick script to check Lambda function status and wait for it to be ready

LAMBDA_FUNCTION_NAME="hmbl-sms-reminders-handler-prod"

echo "🔍 Checking Lambda function status..."

while true; do
    STATE=$(aws lambda get-function --function-name "$LAMBDA_FUNCTION_NAME" --query 'Configuration.State' --output text 2>/dev/null)
    
    if [ "$STATE" = "Active" ]; then
        echo "✅ Lambda function is Active and ready!"
        break
    elif [ "$STATE" = "Pending" ]; then
        echo "⏳ Lambda function is still Pending... waiting 10 seconds"
        sleep 10
    elif [ "$STATE" = "Failed" ]; then
        echo "❌ Lambda function is in Failed state"
        aws lambda get-function --function-name "$LAMBDA_FUNCTION_NAME" --query 'Configuration.StateReason'
        exit 1
    else
        echo "🤔 Unknown state: $STATE"
        sleep 5
    fi
done

echo "🚀 Now updating environment variables..."

# Create the environment variables JSON
cat > /tmp/lambda-env-vars.json << 'EOF'
{
  "Variables": {
    "TWILIO_ACCOUNT_SID": "**********************************",
    "TWILIO_AUTH_TOKEN": "eb227323ff58ff56eddb2ee41548754d",
    "TWILIO_PHONE_NUMBER": "+***********"
  }
}
EOF

# Update environment variables
aws lambda update-function-configuration \
    --function-name "$LAMBDA_FUNCTION_NAME" \
    --environment file:///tmp/lambda-env-vars.json

echo "✅ Environment variables updated successfully!"

# Cleanup
rm -f /tmp/lambda-env-vars.json
