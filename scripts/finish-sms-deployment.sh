#!/bin/bash

# Finish SMS Reminder Deployment Script
# This script completes the deployment after the Lambda function is ready

set -e

# Configuration
ENVIRONMENT="prod"
LAMBDA_FUNCTION_NAME="hmbl-sms-reminders-handler-${ENVIRONMENT}"
SCHEDULE_GROUP_NAME="hmbl-sms-reminders-${ENVIRONMENT}"
CLOUDWATCH_NAMESPACE="HMBL/SMS-Reminders"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Get Lambda function ARN
log_info "Getting Lambda function details..."
LAMBDA_ARN=$(aws lambda get-function --function-name "$LAMBDA_FUNCTION_NAME" --query 'Configuration.FunctionArn' --output text)
LAMBDA_ROLE_ARN=$(aws lambda get-function --function-name "$LAMBDA_FUNCTION_NAME" --query 'Configuration.Role' --output text)

log_success "Lambda ARN: $LAMBDA_ARN"

# Get scheduler execution role ARN
SCHEDULER_ROLE_NAME="hmbl-sms-reminders-scheduler-role-${ENVIRONMENT}"
SCHEDULER_ROLE_ARN=$(aws iam get-role --role-name "$SCHEDULER_ROLE_NAME" --query 'Role.Arn' --output text)

log_success "Scheduler Role ARN: $SCHEDULER_ROLE_ARN"

# Create CloudWatch Alarms
log_info "Creating CloudWatch Alarms..."

# Lambda Error Rate Alarm
aws cloudwatch put-metric-alarm \
    --alarm-name "SMS-Reminder-Lambda-Errors-${ENVIRONMENT}" \
    --alarm-description "SMS Reminder Lambda function errors" \
    --metric-name "Errors" \
    --namespace "AWS/Lambda" \
    --statistic "Sum" \
    --period 300 \
    --threshold 1 \
    --comparison-operator "GreaterThanOrEqualToThreshold" \
    --evaluation-periods 1 \
    --dimensions Name=FunctionName,Value="$LAMBDA_FUNCTION_NAME" \
    --alarm-actions "arn:aws:sns:us-east-2:117255351486:hmbl-alerts" || log_warning "Could not create error alarm (SNS topic may not exist)"

# Lambda Duration Alarm
aws cloudwatch put-metric-alarm \
    --alarm-name "SMS-Reminder-Lambda-Duration-${ENVIRONMENT}" \
    --alarm-description "SMS Reminder Lambda function duration" \
    --metric-name "Duration" \
    --namespace "AWS/Lambda" \
    --statistic "Average" \
    --period 300 \
    --threshold 25000 \
    --comparison-operator "GreaterThanThreshold" \
    --evaluation-periods 2 \
    --dimensions Name=FunctionName,Value="$LAMBDA_FUNCTION_NAME" \
    --alarm-actions "arn:aws:sns:us-east-2:117255351486:hmbl-alerts" || log_warning "Could not create duration alarm (SNS topic may not exist)"

log_success "CloudWatch alarms created"

# Test the Lambda function
log_info "Testing Lambda function..."

cat > /tmp/test-payload.json << 'EOF'
{
  "memberName": "Test User",
  "memberPhone": "+13058719378",
  "entityType": "appointment",
  "entityId": "test-123",
  "reminderType": "24h",
  "appointmentDate": "2024-01-15",
  "appointmentTime": "10:00 AM"
}
EOF

# Invoke Lambda function
aws lambda invoke \
    --function-name "$LAMBDA_FUNCTION_NAME" \
    --payload file:///tmp/test-payload.json \
    /tmp/lambda-response.json

# Check response
if [ -f /tmp/lambda-response.json ]; then
    log_success "Lambda function test completed"
    log_info "Response: $(cat /tmp/lambda-response.json)"
else
    log_error "Lambda function test failed"
fi

# Generate environment variables for .env file
log_info "Generating environment variables for your .env file..."

echo ""
echo "=========================================="
echo "Add these variables to your .env file:"
echo "=========================================="
echo "SMS_REMINDER_LAMBDA_ARN=$LAMBDA_ARN"
echo "SMS_REMINDER_EXECUTION_ROLE_ARN=$SCHEDULER_ROLE_ARN"
echo "SMS_REMINDER_SCHEDULE_GROUP=$SCHEDULE_GROUP_NAME"
echo "SMS_REMINDER_CLOUDWATCH_NAMESPACE=$CLOUDWATCH_NAMESPACE"
echo "=========================================="
echo ""

# Cleanup temporary files
rm -f /tmp/test-payload.json
rm -f /tmp/lambda-response.json

log_success "SMS Reminder deployment completed successfully!"
log_info "Next steps:"
log_info "1. Add the environment variables above to your .env file"
log_info "2. Restart your Vapor application: swift run"
log_info "3. Test creating appointments to verify SMS reminders are scheduled"
log_info "4. Monitor CloudWatch logs: aws logs tail /aws/lambda/$LAMBDA_FUNCTION_NAME --follow"

echo ""
log_success "🎉 Your SMS reminder system is ready!"
