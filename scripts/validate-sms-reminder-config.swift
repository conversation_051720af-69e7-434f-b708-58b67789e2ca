//
//  validate-sms-reminder-config.swift
//  
//
//  Created by <PERSON> on 7/25/25.
//

import Foundation
import Vapor
import SotoCore
import SotoScheduler

/**
 * Configuration Validation Script for SMS Reminder System
 * 
 * This script validates that all required environment variables and AWS resources
 * are properly configured for the SMS reminder system.
 */

struct SMSReminderConfigValidator {
    
    // MARK: - Environment Variables
    
    private static let requiredEnvVars = [
        "SMS_REMINDER_LAMBDA_ARN",
        "SMS_REMINDER_EXECUTION_ROLE_ARN", 
        "SMS_REMINDER_SCHEDULE_GROUP",
        "TWILIO_ACCOUNT_SID",
        "TWILIO_TOKEN",
        "TWILIO_PHONE_NUMBER",
        "AWSACCESSKEYID",
        "AWSSECRETACCESSKEY"
    ]
    
    // MARK: - Validation Methods
    
    static func validateEnvironmentVariables() -> [String] {
        var missingVars: [String] = []
        
        print("🔍 Validating environment variables...")
        
        for envVar in requiredEnvVars {
            if let value = Environment.get(envVar), !value.isEmpty {
                print("✅ \(envVar): ✓")
            } else {
                print("❌ \(envVar): Missing or empty")
                missingVars.append(envVar)
            }
        }
        
        return missingVars
    }
    
    static func validateTwilioCredentials() -> Bool {
        print("\n📱 Validating Twilio credentials...")
        
        guard let accountSid = Environment.get("TWILIO_ACCOUNT_SID"),
              let authToken = Environment.get("TWILIO_TOKEN"),
              let phoneNumber = Environment.get("TWILIO_PHONE_NUMBER") else {
            print("❌ Missing Twilio credentials")
            return false
        }
        
        // Validate format
        if accountSid.hasPrefix("AC") && accountSid.count == 34 {
            print("✅ TWILIO_ACCOUNT_SID format: ✓")
        } else {
            print("❌ TWILIO_ACCOUNT_SID format: Invalid")
            return false
        }
        
        if authToken.count == 32 {
            print("✅ TWILIO_TOKEN format: ✓")
        } else {
            print("❌ TWILIO_TOKEN format: Invalid")
            return false
        }
        
        if phoneNumber.hasPrefix("+") && phoneNumber.count >= 10 {
            print("✅ TWILIO_PHONE_NUMBER format: ✓")
        } else {
            print("❌ TWILIO_PHONE_NUMBER format: Invalid")
            return false
        }
        
        return true
    }
    
    static func validateAWSConfiguration(app: Application) -> EventLoopFuture<Bool> {
        print("\n☁️ Validating AWS configuration...")
        
        let scheduler = Scheduler(client: app.aws.client, region: .useast1)
        
        // Test AWS connectivity by listing schedule groups
        return scheduler.listScheduleGroups(Scheduler.ListScheduleGroupsInput())
            .map { response in
                print("✅ AWS EventBridge Scheduler connectivity: ✓")
                
                // Check if our schedule group exists
                let groupName = Environment.get("SMS_REMINDER_SCHEDULE_GROUP") ?? "sms-reminders"
                let groupExists = response.scheduleGroups?.contains { $0.name == groupName } ?? false
                
                if groupExists {
                    print("✅ Schedule group '\(groupName)': ✓")
                } else {
                    print("⚠️ Schedule group '\(groupName)': Not found (will be created automatically)")
                }
                
                return true
            }
            .flatMapError { error in
                print("❌ AWS EventBridge Scheduler connectivity: Failed")
                print("   Error: \(error)")
                return app.eventLoopGroup.next().makeSucceededFuture(false)
            }
    }
    
    static func validateLambdaFunction(app: Application) -> EventLoopFuture<Bool> {
        print("\n🔧 Validating Lambda function...")
        
        guard let lambdaArn = Environment.get("SMS_REMINDER_LAMBDA_ARN") else {
            print("❌ SMS_REMINDER_LAMBDA_ARN not configured")
            return app.eventLoopGroup.next().makeSucceededFuture(false)
        }
        
        // Validate ARN format
        let arnPattern = "^arn:aws:lambda:[a-z0-9-]+:[0-9]+:function:[a-zA-Z0-9-_]+$"
        let regex = try! NSRegularExpression(pattern: arnPattern)
        let range = NSRange(location: 0, length: lambdaArn.utf16.count)
        
        if regex.firstMatch(in: lambdaArn, options: [], range: range) != nil {
            print("✅ Lambda ARN format: ✓")
            print("   ARN: \(lambdaArn)")
            return app.eventLoopGroup.next().makeSucceededFuture(true)
        } else {
            print("❌ Lambda ARN format: Invalid")
            print("   ARN: \(lambdaArn)")
            return app.eventLoopGroup.next().makeSucceededFuture(false)
        }
    }
    
    static func testSMSReminderScheduling(app: Application) -> EventLoopFuture<Bool> {
        print("\n🧪 Testing SMS reminder scheduling...")
        
        let testDate = Date().addingTimeInterval(300) // 5 minutes from now
        let testPayload = SMSReminderPayload(
            phoneNumber: "+15551234567", // Test number
            message: "Test SMS reminder from HMBL system",
            appointmentId: nil,
            taskId: UUID().uuidString,
            goalId: nil,
            interventionId: nil,
            reminderType: .taskDue
        )
        
        let scheduler = app.smsReminderScheduler
        
        return scheduler.scheduleTaskReminder(
            req: MockRequest(application: app),
            taskId: UUID(),
            phoneNumber: "+15551234567",
            dueDate: testDate,
            memberName: "Test User",
            taskTitle: "Configuration Test Task"
        ).map { _ in
            print("✅ SMS reminder scheduling: ✓")
            print("   Test reminder scheduled for: \(testDate)")
            return true
        }.flatMapError { error in
            print("❌ SMS reminder scheduling: Failed")
            print("   Error: \(error)")
            return app.eventLoopGroup.next().makeSucceededFuture(false)
        }
    }
    
    // MARK: - Main Validation Function
    
    static func runFullValidation(app: Application) -> EventLoopFuture<Bool> {
        print("🚀 Starting SMS Reminder System Configuration Validation\n")
        
        // Step 1: Environment variables
        let missingVars = validateEnvironmentVariables()
        if !missingVars.isEmpty {
            print("\n❌ Validation failed: Missing environment variables")
            return app.eventLoopGroup.next().makeSucceededFuture(false)
        }
        
        // Step 2: Twilio credentials
        if !validateTwilioCredentials() {
            print("\n❌ Validation failed: Invalid Twilio credentials")
            return app.eventLoopGroup.next().makeSucceededFuture(false)
        }
        
        // Step 3: AWS configuration
        return validateAWSConfiguration(app: app).flatMap { awsValid in
            if !awsValid {
                print("\n❌ Validation failed: AWS configuration issues")
                return app.eventLoopGroup.next().makeSucceededFuture(false)
            }
            
            // Step 4: Lambda function
            return validateLambdaFunction(app: app).flatMap { lambdaValid in
                if !lambdaValid {
                    print("\n❌ Validation failed: Lambda function issues")
                    return app.eventLoopGroup.next().makeSucceededFuture(false)
                }
                
                // Step 5: Test scheduling
                return testSMSReminderScheduling(app: app).map { testValid in
                    if testValid {
                        print("\n🎉 All validations passed! SMS Reminder system is properly configured.")
                    } else {
                        print("\n❌ Validation failed: SMS reminder scheduling test failed")
                    }
                    return testValid
                }
            }
        }
    }
}

// MARK: - Mock Request for Testing

struct MockRequest: Request {
    let application: Application
    let eventLoop: EventLoop
    let logger: Logger
    
    init(application: Application) {
        self.application = application
        self.eventLoop = application.eventLoopGroup.next()
        self.logger = application.logger
    }
    
    // Implement other required Request properties with default values
    var headers: HTTPHeaders = HTTPHeaders()
    var url: URI = URI(string: "/test")
    var method: HTTPMethod = .GET
    var version: HTTPVersion = .http1_1
    var remoteAddress: SocketAddress? = nil
    var body: ByteBuffer = ByteBuffer()
    var parameters: Parameters = Parameters()
    var storage: Storage = Storage()
}

// MARK: - Usage Example

/*
 * To use this validator, add a route to your Vapor application:
 *
 * app.get("validate-sms-config") { req in
 *     return SMSReminderConfigValidator.runFullValidation(app: req.application)
 *         .map { success in
 *             return success ? "✅ Configuration valid" : "❌ Configuration invalid"
 *         }
 * }
 */
