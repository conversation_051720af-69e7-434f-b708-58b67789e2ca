#!/bin/bash

# SMS Reminder System Deployment Verification Script
# Run this after deploying to verify everything is working correctly

set -e

# Configuration - UPDATE THESE TO MATCH YOUR DEPLOYMENT
ENVIRONMENT="prod"  # Change to match your deployment
PROJECT_NAME="hmbl-sms-reminders"
AWS_REGION="us-east-2"

# Derived names
LAMBDA_FUNCTION_NAME="${PROJECT_NAME}-handler-${ENVIRONMENT}"
SCHEDULE_GROUP_NAME="${PROJECT_NAME}-${ENVIRONMENT}"
LAMBDA_ROLE_NAME="${PROJECT_NAME}-lambda-role-${ENVIRONMENT}"
SCHEDULER_ROLE_NAME="${PROJECT_NAME}-scheduler-role-${ENVIRONMENT}"
LOG_GROUP_NAME="/aws/lambda/${LAMBDA_FUNCTION_NAME}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Helper functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[✅ PASS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[⚠️  WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[❌ FAIL]${NC} $1"
}

# Test counters
TESTS_PASSED=0
TESTS_FAILED=0
TESTS_TOTAL=0

run_test() {
    local test_name="$1"
    local test_command="$2"
    
    TESTS_TOTAL=$((TESTS_TOTAL + 1))
    log_info "Testing: $test_name"
    
    if eval "$test_command" &> /dev/null; then
        log_success "$test_name"
        TESTS_PASSED=$((TESTS_PASSED + 1))
        return 0
    else
        log_error "$test_name"
        TESTS_FAILED=$((TESTS_FAILED + 1))
        return 1
    fi
}

# Verification tests
verify_aws_connectivity() {
    log_info "🔍 Verifying AWS connectivity..."
    run_test "AWS CLI configured" "aws sts get-caller-identity"
    run_test "AWS region accessible" "aws ec2 describe-regions --region $AWS_REGION"
}

verify_iam_roles() {
    log_info "🔍 Verifying IAM roles..."
    run_test "Lambda execution role exists" "aws iam get-role --role-name $LAMBDA_ROLE_NAME"
    run_test "Scheduler execution role exists" "aws iam get-role --role-name $SCHEDULER_ROLE_NAME"
    
    # Check role policies
    run_test "Lambda role has execution policy" "aws iam get-role-policy --role-name $LAMBDA_ROLE_NAME --policy-name LambdaExecutionPolicy"
    run_test "Scheduler role has execution policy" "aws iam get-role-policy --role-name $SCHEDULER_ROLE_NAME --policy-name SchedulerExecutionPolicy"
}

verify_lambda_function() {
    log_info "🔍 Verifying Lambda function..."
    run_test "Lambda function exists" "aws lambda get-function --function-name $LAMBDA_FUNCTION_NAME"
    
    # Check Lambda configuration
    if aws lambda get-function-configuration --function-name "$LAMBDA_FUNCTION_NAME" &> /dev/null; then
        local runtime=$(aws lambda get-function-configuration --function-name "$LAMBDA_FUNCTION_NAME" --query 'Runtime' --output text)
        local timeout=$(aws lambda get-function-configuration --function-name "$LAMBDA_FUNCTION_NAME" --query 'Timeout' --output text)
        local memory=$(aws lambda get-function-configuration --function-name "$LAMBDA_FUNCTION_NAME" --query 'MemorySize' --output text)
        
        log_success "Lambda configuration - Runtime: $runtime, Timeout: ${timeout}s, Memory: ${memory}MB"
        
        # Check environment variables
        local env_vars=$(aws lambda get-function-configuration --function-name "$LAMBDA_FUNCTION_NAME" --query 'Environment.Variables' --output json)
        if echo "$env_vars" | grep -q "TWILIO_ACCOUNT_SID"; then
            log_success "Twilio environment variables configured"
        else
            log_error "Twilio environment variables missing"
            TESTS_FAILED=$((TESTS_FAILED + 1))
        fi
    fi
}

verify_eventbridge_scheduler() {
    log_info "🔍 Verifying EventBridge Scheduler..."
    run_test "Schedule group exists" "aws scheduler get-schedule-group --name $SCHEDULE_GROUP_NAME"
}

verify_cloudwatch() {
    log_info "🔍 Verifying CloudWatch resources..."
    run_test "CloudWatch log group exists" "aws logs describe-log-groups --log-group-name-prefix '$LOG_GROUP_NAME'"
    
    # Check for alarms
    local alarm_count=$(aws cloudwatch describe-alarms --alarm-name-prefix "${PROJECT_NAME}" --query 'MetricAlarms | length(@)' --output text)
    if [ "$alarm_count" -gt 0 ]; then
        log_success "CloudWatch alarms configured ($alarm_count alarms)"
    else
        log_warning "No CloudWatch alarms found"
    fi
}

test_lambda_function() {
    log_info "🔍 Testing Lambda function execution..."
    
    # Create test payload
    cat > /tmp/test-payload.json << EOF
{
  "phoneNumber": "+15551234567",
  "message": "Deployment verification test - $(date)",
  "reminderType": "test"
}
EOF

    # Invoke Lambda function
    if aws lambda invoke \
        --function-name "$LAMBDA_FUNCTION_NAME" \
        --payload file:///tmp/test-payload.json \
        /tmp/lambda-response.json &> /dev/null; then
        
        # Check response
        if grep -q '"statusCode":200' /tmp/lambda-response.json; then
            log_success "Lambda function execution test"
        elif grep -q '"statusCode":500' /tmp/lambda-response.json; then
            log_warning "Lambda function returned error (this may be expected for test payload)"
            echo "Response: $(cat /tmp/lambda-response.json)"
        else
            log_error "Lambda function execution test - unexpected response"
            echo "Response: $(cat /tmp/lambda-response.json)"
            TESTS_FAILED=$((TESTS_FAILED + 1))
        fi
    else
        log_error "Lambda function execution test - invocation failed"
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
    
    TESTS_TOTAL=$((TESTS_TOTAL + 1))
}

test_scheduler_permissions() {
    log_info "🔍 Testing EventBridge Scheduler permissions..."
    
    # Try to create a test schedule (and immediately delete it)
    local test_schedule_name="test-schedule-$(date +%s)"
    local lambda_arn=$(aws lambda get-function --function-name "$LAMBDA_FUNCTION_NAME" --query 'Configuration.FunctionArn' --output text)
    local scheduler_role_arn=$(aws iam get-role --role-name "$SCHEDULER_ROLE_NAME" --query 'Role.Arn' --output text)
    
    # Create test schedule
    if aws scheduler create-schedule \
        --name "$test_schedule_name" \
        --group-name "$SCHEDULE_GROUP_NAME" \
        --schedule-expression "at($(date -u -v+5M '+%Y-%m-%dT%H:%M:%S'))" \
        --target "{\"Arn\":\"$lambda_arn\",\"RoleArn\":\"$scheduler_role_arn\",\"Input\":\"{\\\"test\\\":true}\"}" \
        --flexible-time-window '{"Mode":"OFF"}' &> /dev/null; then
        
        log_success "EventBridge Scheduler permissions test"
        
        # Clean up test schedule
        aws scheduler delete-schedule --name "$test_schedule_name" --group-name "$SCHEDULE_GROUP_NAME" &> /dev/null
    else
        log_error "EventBridge Scheduler permissions test"
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
    
    TESTS_TOTAL=$((TESTS_TOTAL + 1))
}

check_environment_variables() {
    log_info "🔍 Checking required environment variables..."
    
    local required_vars=(
        "SMS_REMINDER_LAMBDA_ARN"
        "SMS_REMINDER_EXECUTION_ROLE_ARN"
        "SMS_REMINDER_SCHEDULE_GROUP"
        "TWILIO_ACCOUNT_SID"
        "TWILIO_TOKEN"
        "TWILIO_PHONE_NUMBER"
    )
    
    local env_file=".env"
    if [ ! -f "$env_file" ]; then
        log_error "Environment file .env not found"
        return 1
    fi
    
    for var in "${required_vars[@]}"; do
        if grep -q "^${var}=" "$env_file"; then
            local value=$(grep "^${var}=" "$env_file" | cut -d'=' -f2-)
            if [ -n "$value" ] && [ "$value" != "your_placeholder_value" ]; then
                log_success "Environment variable $var is set"
            else
                log_error "Environment variable $var is empty or placeholder"
                TESTS_FAILED=$((TESTS_FAILED + 1))
            fi
        else
            log_error "Environment variable $var not found in .env"
            TESTS_FAILED=$((TESTS_FAILED + 1))
        fi
        TESTS_TOTAL=$((TESTS_TOTAL + 1))
    done
}

generate_test_report() {
    echo ""
    echo "=========================================="
    echo "📊 DEPLOYMENT VERIFICATION REPORT"
    echo "=========================================="
    echo "Environment: $ENVIRONMENT"
    echo "Region: $AWS_REGION"
    echo "Tests Passed: $TESTS_PASSED"
    echo "Tests Failed: $TESTS_FAILED"
    echo "Total Tests: $TESTS_TOTAL"
    echo ""
    
    if [ $TESTS_FAILED -eq 0 ]; then
        log_success "🎉 All tests passed! Your SMS reminder system is ready to use."
        echo ""
        echo "Next steps:"
        echo "1. Create a test appointment or task in your application"
        echo "2. Verify that reminders are scheduled in EventBridge"
        echo "3. Monitor CloudWatch logs for any issues"
        echo "4. Set up additional monitoring and alerts as needed"
    else
        log_error "❌ Some tests failed. Please review the issues above."
        echo ""
        echo "Common solutions:"
        echo "1. Ensure all environment variables are correctly set in .env"
        echo "2. Verify AWS credentials have sufficient permissions"
        echo "3. Check that all AWS resources were created successfully"
        echo "4. Review CloudWatch logs for detailed error messages"
    fi
    echo "=========================================="
}

cleanup() {
    rm -f /tmp/test-payload.json
    rm -f /tmp/lambda-response.json
}

# Main execution
main() {
    log_info "🚀 Starting SMS Reminder System Deployment Verification"
    echo ""
    
    verify_aws_connectivity
    verify_iam_roles
    verify_lambda_function
    verify_eventbridge_scheduler
    verify_cloudwatch
    test_lambda_function
    test_scheduler_permissions
    check_environment_variables
    
    generate_test_report
    cleanup
}

# Run main function
main "$@"
