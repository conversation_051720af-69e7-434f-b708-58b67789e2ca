#!/bin/bash

# One-Command SMS Reminder System Deployment and Verification
# This script deploys the entire SMS reminder system and verifies it's working

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
BOLD='\033[1m'
NC='\033[0m'

# Helper functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${BOLD}${BLUE}$1${NC}"
}

# Check if we're in the right directory
check_project_directory() {
    if [ ! -f "Package.swift" ] || [ ! -d "Sources/App" ]; then
        log_error "Please run this script from your HMBL project root directory"
        exit 1
    fi
    
    if [ ! -d "lambda/sms-reminder-handler" ]; then
        log_error "Lambda function directory not found. Please ensure the SMS reminder system files are in place."
        exit 1
    fi
}

# Main deployment function
main() {
    log_header "🚀 SMS Reminder System - One-Command Deployment"
    echo ""
    
    # Check prerequisites
    log_info "Checking project structure..."
    check_project_directory
    log_success "Project structure verified"
    
    # Make scripts executable
    chmod +x scripts/deploy-sms-reminder-aws.sh
    chmod +x scripts/verify-sms-reminder-deployment.sh
    
    # Step 1: Deploy to AWS
    log_header "📦 Step 1: Deploying to AWS..."
    echo ""
    ./scripts/deploy-sms-reminder-aws.sh
    
    echo ""
    log_header "⏳ Waiting 30 seconds for AWS resources to propagate..."
    sleep 30
    
    # Step 2: Verify deployment
    log_header "🔍 Step 2: Verifying deployment..."
    echo ""
    ./scripts/verify-sms-reminder-deployment.sh
    
    # Step 3: Final instructions
    echo ""
    log_header "🎉 Deployment Complete!"
    echo ""
    log_info "Your SMS reminder system has been deployed and verified."
    echo ""
    echo "📋 IMPORTANT NEXT STEPS:"
    echo ""
    echo "1. 🔄 Restart your Vapor application to pick up new environment variables:"
    echo "   swift run"
    echo ""
    echo "2. 🧪 Test the system by creating an appointment or task:"
    echo "   - Create an appointment with a future date"
    echo "   - Check EventBridge schedules: aws scheduler list-schedules --group-name hmbl-sms-reminders-prod"
    echo ""
    echo "3. 📊 Monitor the system:"
    echo "   - CloudWatch logs: aws logs tail /aws/lambda/hmbl-sms-reminders-handler-prod --follow"
    echo "   - CloudWatch metrics: aws cloudwatch list-metrics --namespace HMBL/SMSReminders"
    echo ""
    echo "4. 📱 Verify SMS delivery:"
    echo "   - Check Twilio console for message delivery status"
    echo "   - Monitor for any delivery failures"
    echo ""
    log_success "SMS Reminder System is ready for production use! 🎊"
}

# Run main function
main "$@"
