# Program Model Documentation

## Overview
The Program model system provides comprehensive tracking of member programs with review periods and tasks. This follows the LTSS (Long-Term Services and Supports) pattern with periodic assessments and reviews.

## Model Structure

### Program Model
The main Program model represents a healthcare program assigned to a member.

**Fields:**
- `id`: UUID - Primary key
- `member_id`: UUID - Foreign key to Member (relationship)
- `program_id`: String - External program identifier (e.g., "ltss_001")
- `program_type`: String - Type of program (e.g., "LTSS")
- `display_name`: String - Human-readable program name
- `start_date`: Date - Program start date
- `end_date`: Date - Program end date
- `status`: String - Program status (active, inactive, completed)
- `review_frequency_days`: Int - How often reviews occur (e.g., 90 days)
- `assigned_by`: String - Who assigned the program
- `assigned_to`: String - Who is responsible for the program
- `created_at`: Date - Timestamp
- `updated_at`: Date - Timestamp

**Relationships:**
- `member`: Parent relationship to Member
- `reviewPeriods`: Children relationship to ReviewPeriod

### ReviewPeriod Model
Represents a specific review period within a program.

**Fields:**
- `id`: UUID - Primary key
- `program_id`: UUID - Foreign key to Program
- `period_number`: Int - Sequential period number (1, 2, 3, etc.)
- `start_date`: Date - Period start date
- `end_date`: Date - Period end date
- `status`: String - Period status (active, locked, completed)
- `created_at`: Date - Timestamp
- `updated_at`: Date - Timestamp

**Relationships:**
- `program`: Parent relationship to Program
- `tasks`: Children relationship to ProgramTask

### ProgramTask Model
Represents individual tasks within a review period.

**Fields:**
- `id`: UUID - Primary key
- `review_period_id`: UUID - Foreign key to ReviewPeriod
- `task_type`: String - Type of task (assessment, review)
- `title`: String - Task title
- `assessment_key`: String? - Optional assessment identifier
- `review_key`: String? - Optional review identifier
- `assigned_to`: String? - Who is assigned this task
- `completed_at`: Date? - When task was completed
- `due_date`: Date - Task due date
- `status`: String - Task status (pending, complete, etc.)
- `outcome_status`: String - Outcome status (completed, incomplete, not_applicable, deferred)
- `outcome_description`: String - Description of the outcome
- `created_at`: Date - Timestamp
- `updated_at`: Date - Timestamp

**Relationships:**
- `reviewPeriod`: Parent relationship to ReviewPeriod

## API Endpoints

### Member Programs

#### Get Member Programs
```
GET /members/{memberID}/programs
```
Returns all programs for a specific member with nested review periods and tasks.

#### Create Member Program
```
POST /members/{memberID}/programs
```
Creates a new program for a member including review periods and tasks.

**Request Body:**
```json
{
  "programId": "ltss_001",
  "programType": "LTSS",
  "displayName": "Long-Term Services and Supports",
  "startDate": "2025-01-01T00:00:00Z",
  "endDate": "2025-12-31T00:00:00Z",
  "status": "active",
  "reviewFrequencyDays": 90,
  "assignedBy": "case_manager_123",
  "assignedTo": "case_manager_456",
  "reviewPeriods": [
    {
      "periodNumber": 1,
      "startDate": "2025-01-01T00:00:00Z",
      "endDate": "2025-03-30T00:00:00Z",
      "status": "active",
      "tasks": [
        {
          "taskType": "assessment",
          "title": "ADL/IADL Functional Assessment",
          "assessmentKey": "adl_iadl",
          "assignedTo": "paul_steves",
          "completedAt": "2025-01-12T00:00:00Z",
          "dueDate": "2025-01-30T00:00:00Z",
          "status": "complete",
          "outcomeStatus": "completed",
          "outcomeDescription": "Member continues to experience housing insecurity but is now engaged with shelter services."
        }
      ]
    }
  ]
}
```

#### Get Specific Member Program
```
GET /members/{memberID}/programs/{programID}
```

#### Update Member Program
```
PUT /members/{memberID}/programs/{programID}
```

#### Delete Member Program
```
DELETE /members/{memberID}/programs/{programID}
```

### Program Tasks

#### Get Program Tasks
```
GET /programs/{programID}/tasks
```

#### Create Program Task
```
POST /programs/{programID}/tasks
```

#### Update Program Task
```
PUT /programs/{programID}/tasks/{taskID}
```

#### Delete Program Task
```
DELETE /programs/{programID}/tasks/{taskID}
```

### Review Periods

#### Get Review Periods
```
GET /programs/{programID}/periods
```

#### Create Review Period
```
POST /programs/{programID}/periods
```

#### Update Review Period
```
PUT /programs/{programID}/periods/{periodID}
```

#### Delete Review Period
```
DELETE /programs/{programID}/periods/{periodID}
```

## Database Migrations

The following migrations need to be run to create the database schema:

1. `CreateProgram` - Creates the programs table
2. `CreateReviewPeriod` - Creates the review_periods table
3. `CreateProgramTask` - Creates the program_tasks table

These are automatically registered in `configure.swift` and will be applied when running:
```bash
swift run App migrate
```

## Content Conformance

All models implement custom Content conformance to exclude parent relationships from JSON encoding/decoding, preventing circular reference issues and ensuring clean API responses.

## Integration with Member Model

The Member model has been updated to include a `programs` relationship:
```swift
@Children(for: \.$member)
var programs: [Program]
```

This allows easy access to all programs for a member through the existing Member API endpoints.

## Example Usage

### Creating a Complete Program
The JSON structure provided in the task specification can be directly used with the `POST /members/{memberID}/programs` endpoint to create a complete program with all review periods and tasks.

### Task Status Management
Tasks support various status values:
- `pending` - Task not yet started
- `complete` - Task completed
- `in_progress` - Task currently being worked on
- `overdue` - Task past due date

### Outcome Status Tracking
Each task tracks outcome status:
- `completed` - Successfully completed
- `incomplete` - Not completed
- `not_applicable` - Task not applicable
- `deferred` - Task deferred to later period

This comprehensive system provides full lifecycle management of member programs with detailed tracking of assessments, reviews, and outcomes.
