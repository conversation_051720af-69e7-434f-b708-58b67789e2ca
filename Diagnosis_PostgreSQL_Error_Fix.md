# Diagnosis PostgreSQL Error Fix

## Problem Description
**Error**: `PSQLError – Generic description to prevent accidental leakage of sensitive data`

This error occurs when trying to create a Diagnosis on a member. The error is likely due to one of these issues:

1. **Migration not applied** - The `CreateDiagnosis` migration hasn't been run
2. **Missing table** - The `diagnoses` table doesn't exist
3. **Schema mismatch** - Database schema doesn't match model expectations
4. **Foreign key constraint** - Invalid member ID reference

## Root Cause Analysis

### **Diagnosis Model (Correct):**
The Diagnosis model in `Sources/App/Models/Member.swift` is properly defined:
```swift
final class Diagnosis: Model, @unchecked Sendable {
    static let schema = "diagnoses"
    
    @ID var id: UUID?
    @Parent(key: "member_id") var member: Member
    
    @Field(key: "icd_code") var icdCode: String?
    @Field(key: "description") var description: String
    @Field(key: "clinical_note") var clinicalNote: String?
    @Field(key: "status") var status: String
    @Field(key: "date_identified") var dateIdentified: Date
    @Field(key: "source") var source: String
    @Field(key: "confirmed_by") var confirmedBy: String?
    
    @Timestamp(key: "created_at", on: .create) var createdAt: Date?
    @Timestamp(key: "updated_at", on: .update) var updatedAt: Date?
}
```

### **CreateDiagnosis Migration (Correct):**
The migration includes all required fields including timestamps:
```swift
struct CreateDiagnosis: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        database.schema("diagnoses")
            .id()
            .field("member_id", .uuid, .required, .references("members", "id", onDelete: .cascade))
            .field("icd_code", .string)
            .field("description", .string, .required)
            .field("clinical_note", .string)
            .field("status", .string, .required, .sql(.default("active")))
            .field("date_identified", .date, .required)
            .field("source", .string, .required)
            .field("confirmed_by", .string)
            .field("created_at", .datetime)      // ✅ Present
            .field("updated_at", .datetime)      // ✅ Present
            .create()
    }
}
```

## Solution Steps

### **Step 1: Check Migration Status**
```bash
swift run Run migrate --status
```

This will show you which migrations have been applied and which are pending.

### **Step 2: Run Pending Migrations**
```bash
swift run Run migrate
```

This will apply all pending migrations, including `CreateDiagnosis`.

### **Step 3: Verify Database Schema**
Connect to your PostgreSQL database and check if the table exists:
```sql
-- Connect to database
psql -h localhost -U your_username -d your_database

-- Check if diagnoses table exists
\dt diagnoses

-- Check table structure
\d diagnoses

-- Should show columns:
-- id, member_id, icd_code, description, clinical_note, status, 
-- date_identified, source, confirmed_by, created_at, updated_at
```

### **Step 4: Check Migration Registration**
Ensure the `CreateDiagnosis` migration is registered in your `configure.swift` file:
```swift
app.migrations.add(CreateDiagnosis())
```

## Common Issues and Solutions

### **Issue 1: Migration Not Registered**
**Problem**: Migration exists but isn't registered in `configure.swift`

**Solution**: Add to your migration registration:
```swift
// In configure.swift
app.migrations.add(CreateDiagnosis())
app.migrations.add(CreateMedication())
```

### **Issue 2: Table Doesn't Exist**
**Problem**: Migration hasn't been run

**Solution**: Run migrations:
```bash
swift run Run migrate
```

### **Issue 3: Foreign Key Constraint**
**Problem**: Trying to create diagnosis with invalid member ID

**Solution**: Ensure the member ID exists:
```sql
-- Check if member exists
SELECT id, first_name, last_name FROM members WHERE id = 'your-member-id';
```

### **Issue 4: Required Field Missing**
**Problem**: Missing required fields in request

**Solution**: Ensure all required fields are provided:
```json
{
  "description": "Essential (primary) hypertension",     // Required
  "status": "active",                                    // Required
  "dateIdentified": "2023-06-01T00:00:00Z",            // Required
  "source": "EHR import",                               // Required
  "icdCode": "I10",                                     // Optional
  "clinicalNote": "BP remains elevated...",             // Optional
  "confirmedBy": "Dr. Smith, MD"                        // Optional
}
```

## Fixed Issues

### **Medication Model Typo (Fixed)**
Found and fixed a typo in the Medication Content conformance:
```swift
// Before (Broken)
case routelet  // ❌ Typo

// After (Fixed)
case route     // ✅ Correct
```

## Testing the Fix

### **Step 1: Run Migration**
```bash
swift run Run migrate
```

### **Step 2: Test Diagnosis Creation**
```bash
POST /api/members/{memberID}/diagnoses
Content-Type: application/json

{
  "icdCode": "I10",
  "description": "Essential (primary) hypertension",
  "clinicalNote": "BP remains elevated despite medication adjustment.",
  "status": "active",
  "dateIdentified": "2023-06-01T00:00:00Z",
  "source": "EHR import",
  "confirmedBy": "Dr. Smith, MD"
}
```

### **Expected Success Response:**
```json
{
  "id": "123e4567-e89b-12d3-a456-426614174000",
  "icdCode": "I10",
  "description": "Essential (primary) hypertension",
  "clinicalNote": "BP remains elevated despite medication adjustment.",
  "status": "active",
  "dateIdentified": "2023-06-01T00:00:00Z",
  "source": "EHR import",
  "confirmedBy": "Dr. Smith, MD",
  "createdAt": "2025-01-21T10:30:00Z",
  "updatedAt": "2025-01-21T10:30:00Z"
}
```

## Debugging Commands

### **Check Migration History:**
```bash
swift run Run migrate --status
```

### **Dry Run Migration:**
```bash
swift run Run migrate --dry-run
```

### **Check Database Tables:**
```sql
-- List all tables
\dt

-- Check specific table
\d diagnoses

-- Check foreign key constraints
\d+ diagnoses
```

### **Check Application Logs:**
```bash
swift run Run serve --log debug
```

## Prevention

### **For Future Models:**
1. **Always register migrations** in `configure.swift`
2. **Include timestamp fields** in migrations for models with `@Timestamp`
3. **Test migrations** in development before production
4. **Verify foreign key references** exist

### **Migration Checklist:**
- ✅ Model defined with proper fields
- ✅ Migration includes all model fields
- ✅ Timestamp fields included for `@Timestamp` properties
- ✅ Foreign key references are correct
- ✅ Migration registered in `configure.swift`
- ✅ Migration applied with `swift run Run migrate`

## Summary

The PostgreSQL error when creating Diagnosis is most likely due to:
1. **Migration not applied** - Run `swift run Run migrate`
2. **Migration not registered** - Add to `configure.swift`
3. **Invalid member ID** - Ensure member exists

**Most Common Solution:**
```bash
swift run Run migrate
```

After running the migration, Diagnosis creation should work without PostgreSQL errors! 🎯

## Next Steps

1. **Run Migration**: `swift run Run migrate`
2. **Test Creation**: Use Postman to create a diagnosis
3. **Verify Success**: Check that diagnosis is created with proper timestamps
4. **Monitor Logs**: Watch for any remaining issues

The Diagnosis API should now work perfectly! 🚀
