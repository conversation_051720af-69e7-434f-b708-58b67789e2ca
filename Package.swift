// swift-tools-version:5.9
import PackageDescription

let package = Package(
    name: "hmbl-core",
    platforms: [
       .macOS(.v14)
    ],
    dependencies: [
        // 💧 A server-side Swift web framework.
        .package(url: "https://github.com/vapor/vapor.git", from: "4.76.0"),
        .package(url: "https://github.com/vapor/fluent.git", from: "4.8.0"),
        .package(url: "https://github.com/vapor/fluent-postgres-driver.git", from: "2.9.0"),
        .package(url: "https://github.com/vapor/apns.git", from: "3.0.0"),
        .package(url: "https://github.com/vapor/queues-redis-driver.git", from: "1.0.0"),
        .package(url: "https://github.com/vapor/leaf.git", from: "4.0.0"),
        .package(url: "https://github.com/swiftcsv/SwiftCSV.git", from: "0.8.0"),
        .package(url: "https://github.com/vapor/jwt.git", from: "4.0.0"),
        .package(url: "https://github.com/soto-project/soto.git", from: "6.0.0"),        
        .package(url: "https://github.com/weichsel/ZIPFoundation", from: "0.9.0"),
//        .package(url: "https://github.com/dagronf/qrcode.git", from: "20.0.0")
    ],
    targets: [
        .target(
            name: "App",
            dependencies: [
                .product(name: "Fluent", package: "fluent"),
                .product(name: "FluentPostgresDriver", package: "fluent-postgres-driver"),
                .product(name: "Vapor", package: "vapor"),
                .product(name: "APNS", package: "apns"),
                .product(name: "SwiftCSV", package: "SwiftCSV"),
                .product(name: "QueuesRedisDriver", package: "queues-redis-driver"),
                .product(name: "Leaf", package: "leaf"),
                .product(name: "JWT", package: "jwt"),
                .product(name: "SotoSNS", package: "soto"),
                .product(name: "SotoS3", package: "soto"),
                .product(name: "SotoCloudWatch", package: "soto"),
                .product(name: "SotoCognitoIdentity", package: "soto"),
                .product(name: "SotoCognitoIdentityProvider", package: "soto"),
                .product(name: "SotoCognitoSync", package: "soto"),
                .product(name: "SotoCloudWatchLogs", package: "soto"),
                .product(name: "SotoEventBridge", package: "soto"),
                .product(name: "SotoScheduler", package: "soto"),
                .product(name: "ZIPFoundation", package: "ZIPFoundation"),
//                .product(name: "QRCode", package: "QRCode")
            ],
            swiftSettings: [
                // Enable better optimizations when building in Release configuration. Despite the use of
                // the `.unsafeFlags` construct required by SwiftPM, this flag is recommended for Release
                // builds. See <https://github.com/swift-server/guides/blob/main/docs/building.md#building-for-production> for details.
                .unsafeFlags(["-cross-module-optimization"], .when(configuration: .release))
            ]
        ),
        .executableTarget(name: "Run", dependencies: [.target(name: "App")]),
        .testTarget(name: "AppTests", dependencies: [
            .target(name: "App"),
            .product(name: "XCTVapor", package: "vapor"),
        ])
    ]
)
