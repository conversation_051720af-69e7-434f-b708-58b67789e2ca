# donaheath-core Application Overview

## Introduction

donaheath-core (also referred to as hmbl-core in the package configuration) is a server-side Swift application built using the Vapor framework. It appears to be a healthcare/wellness platform designed to manage relationships between organizations, members, and care providers.

## Technology Stack

- **Server Framework**: Vapor 4 (Swift)
- **Database**: PostgreSQL with Fluent ORM
- **Authentication**: JWT (JSON Web Tokens)
- **Messaging**: Twilio for SMS, APNS for push notifications
- **Cloud Services**: AWS services (SNS, S3, CloudWatch)
- **Queue System**: Redis for background job processing
- **Templating**: Leaf for server-side HTML rendering

## Core Features

1. **User Management**
   - Authentication and authorization
   - User roles and permissions
   - Profile management

2. **Organization Management**
   - Multiple organization types (mobile, standard, hospital)
   - Organization-specific configuration and customization

3. **Member Management**
   - Member profiles and data
   - Member status tracking
   - Member-organization relationships

4. **Team Collaboration**
   - Team creation and management
   - User-team associations
   - Team-based workflows

5. **Household Management**
   - Family/household grouping
   - Household-team associations

6. **Communication Tools**
   - SMS integration via Twilio
   - Push notifications via APNS
   - Chat functionality

7. **Task Management**
   - Task creation and assignment
   - Task status tracking
   - Task notifications

8. **Survey System**
   - Survey creation and management
   - Question and answer tracking
   - Survey templates

9. **Appointment Scheduling**
   - Appointment creation and management
   - Calendar integration

10. **Care Package Management**
    - Care package creation and tracking
    - Service delivery

11. **Insurance Integration**
    - Insurance carrier information
    - Insurance coverage details

12. **Notes and Documentation**
    - Note creation and management
    - Attachment handling
    - Documentation tracking

## Data Model

The application has a comprehensive data model with key entities including:

- **Users**: System users (likely staff/providers)
- **Members**: End users/patients/clients
- **Organizations**: Healthcare providers or service organizations
- **Teams**: Groups of users working together
- **Households**: Groups of related members
- **Tasks**: Actionable items assigned to users or members
- **Appointments**: Scheduled meetings or services
- **Notes**: Documentation of interactions or observations
- **Attachments**: Files and documents
- **Surveys/Questions/Answers**: Assessment tools

## API Structure

The application provides a RESTful API with controllers for each major entity. The routes.swift file registers numerous controllers that handle specific aspects of the application's functionality, including:

- Authentication (AuthController)
- Users and Members management
- Organization and Team management
- Tasks and Appointments
- Communication (Twilio, APNS)
- Content and Notes
- Insurance and Care Packages

## Deployment

The application is designed to be deployed in multiple environments:

- Local development
- Staging environment
- Production environment

It uses Docker for containerization, as evidenced by the Dockerfile and docker-compose.yml files.

## Security

The application implements several security measures:

- JWT-based authentication
- Role-based access control
- CORS configuration
- TLS for database connections

## Integrations

The application integrates with several external services:

- AWS services (SNS, S3, CloudWatch, Cognito)
- Twilio for SMS
- Apple Push Notification Service (APNS)
- Possibly Zoom (based on controller directory)

## Conclusion

donaheath-core appears to be a robust healthcare/wellness platform designed to facilitate communication and coordination between care providers and members/patients. It provides comprehensive tools for managing organizations, teams, members, and the interactions between them, with a focus on task management, communication, and documentation. 