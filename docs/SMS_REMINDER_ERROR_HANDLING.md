# SMS Reminder System Error Handling & Monitoring

This document outlines the comprehensive error handling, logging, and monitoring strategy for the SMS reminder system.

## Error Handling Strategy

### 1. Graceful Degradation

The SMS reminder system is designed to fail gracefully without affecting core healthcare operations:

- **Non-blocking**: Reminder scheduling failures don't prevent appointment/task creation
- **Retry Logic**: Automatic retries for transient failures
- **Fallback**: Manual reminder options when automated system fails

### 2. Error Categories

#### Configuration Errors
- Missing environment variables
- Invalid AWS credentials
- Malformed Lambda ARN
- Invalid Twilio credentials

#### Validation Errors
- Invalid phone number format
- Missing required data fields
- Invalid date/time values

#### AWS Service Errors
- EventBridge Scheduler API failures
- Lambda function invocation errors
- IAM permission issues
- Rate limiting

#### Twilio API Errors
- Invalid phone numbers
- Account balance issues
- Rate limiting
- Service outages

## Logging Implementation

### 1. Structured Logging

All SMS reminder operations use structured logging with consistent metadata:

```swift
logger.info("SMS reminder scheduled", metadata: [
    "action": "reminder_scheduled",
    "entity_type": "appointment",
    "entity_id": "123e4567-e89b-12d3-a456-************",
    "phone_number": "+1555***4567", // Masked for privacy
    "scheduled_date": "2025-07-26T14:00:00Z",
    "member_name": "John Doe"
])
```

### 2. Log Levels

- **INFO**: Successful operations, scheduled reminders
- **WARN**: Retryable failures, configuration warnings
- **ERROR**: Failed operations, validation errors
- **CRITICAL**: System-wide issues, high error rates

### 3. Privacy Protection

- Phone numbers are masked in logs: `+1555***4567`
- Personal information is limited to necessary identifiers
- Full phone numbers are never logged in plain text

## CloudWatch Integration

### 1. Custom Metrics

The system sends custom metrics to CloudWatch:

```
Namespace: HMBL/SMSReminders

Metrics:
- RemindersScheduled (Count)
- RemindersCancelled (Count)
- ReminderErrors (Count)
- SuccessRate (Percent)
- LambdaInvocations (Count)
- TwilioAPIErrors (Count)
```

### 2. Dimensions

Metrics are tagged with dimensions for filtering:

- `EntityType`: appointment, task, goal, intervention, etc.
- `ErrorType`: ValidationError, AWSError, TwilioError, etc.
- `Environment`: dev, staging, production

### 3. CloudWatch Alarms

Recommended alarms:

```bash
# High error rate alarm
aws cloudwatch put-metric-alarm \
  --alarm-name "SMS-Reminder-High-Error-Rate" \
  --alarm-description "Alert when SMS reminder error rate > 10%" \
  --metric-name SuccessRate \
  --namespace HMBL/SMSReminders \
  --statistic Average \
  --period 300 \
  --threshold 90 \
  --comparison-operator LessThanThreshold \
  --evaluation-periods 2

# Lambda function errors
aws cloudwatch put-metric-alarm \
  --alarm-name "SMS-Reminder-Lambda-Errors" \
  --alarm-description "Alert on Lambda function errors" \
  --metric-name Errors \
  --namespace AWS/Lambda \
  --statistic Sum \
  --period 300 \
  --threshold 5 \
  --comparison-operator GreaterThanThreshold \
  --dimensions Name=FunctionName,Value=sms-reminder-handler \
  --evaluation-periods 1
```

## Error Recovery Procedures

### 1. Automatic Recovery

#### Transient Failures
- Automatic retry with exponential backoff
- Maximum 3 retry attempts
- Fallback to manual notification

#### Rate Limiting
- Implement circuit breaker pattern
- Queue reminders for later processing
- Respect Twilio rate limits

### 2. Manual Recovery

#### Configuration Issues
1. Check environment variables
2. Validate AWS credentials
3. Verify Lambda function deployment
4. Test Twilio connectivity

#### AWS Service Issues
1. Check AWS service health dashboard
2. Verify IAM permissions
3. Review CloudTrail logs
4. Test EventBridge Scheduler manually

## Monitoring Dashboard

### 1. Key Metrics to Monitor

#### Operational Metrics
- Reminders scheduled per hour
- Success rate percentage
- Average processing time
- Error count by type

#### Business Metrics
- Appointment reminder delivery rate
- Task reminder effectiveness
- Member engagement with reminders

### 2. Health Checks

Implement automated health checks:

```swift
// Example health check endpoint
app.get("health", "sms-reminders") { req in
    return req.smsReminderMonitoring.performHealthCheck()
        .map { result in
            return result.overallHealth ? 
                Response(status: .ok, body: .init(string: result.description)) :
                Response(status: .serviceUnavailable, body: .init(string: result.description))
        }
}
```

### 3. Alerting Thresholds

| Metric | Warning | Critical |
|--------|---------|----------|
| Error Rate | > 5% | > 10% |
| Lambda Duration | > 10s | > 20s |
| Queue Depth | > 100 | > 500 |
| Success Rate | < 95% | < 90% |

## Troubleshooting Guide

### Common Issues and Solutions

#### 1. "SMS_REMINDER_LAMBDA_ARN not configured"
**Cause**: Missing environment variable
**Solution**: 
```bash
export SMS_REMINDER_LAMBDA_ARN=arn:aws:lambda:us-east-2:************:function:sms-reminder-handler
```

#### 2. "Invalid phone number format"
**Cause**: Phone number not in E.164 format
**Solution**: Ensure phone numbers include country code and start with +

#### 3. "AWS EventBridge Scheduler error: AccessDenied"
**Cause**: Insufficient IAM permissions
**Solution**: Add required permissions to IAM role

#### 4. "Twilio API error: Account not found"
**Cause**: Invalid Twilio credentials
**Solution**: Verify TWILIO_ACCOUNT_SID and TWILIO_TOKEN

### Debug Commands

```bash
# Check AWS connectivity
aws sts get-caller-identity

# List EventBridge schedules
aws scheduler list-schedules --group-name sms-reminders

# Test Lambda function
aws lambda invoke \
  --function-name sms-reminder-handler \
  --payload '{"phoneNumber":"+***********","message":"Test"}' \
  response.json

# Check CloudWatch logs
aws logs describe-log-groups --log-group-name-prefix "/aws/lambda/sms-reminder"
```

## Performance Optimization

### 1. Batch Operations

For bulk reminder scheduling:
- Process in batches of 10-50 reminders
- Implement rate limiting to avoid API throttling
- Use async processing for large volumes

### 2. Caching

Cache frequently accessed data:
- Member phone numbers
- Twilio account status
- AWS service endpoints

### 3. Connection Pooling

Reuse AWS client connections:
- Configure connection pooling
- Set appropriate timeouts
- Monitor connection health

## Security Considerations

### 1. Data Protection

- Encrypt sensitive data at rest
- Use TLS for all API communications
- Mask phone numbers in logs
- Implement data retention policies

### 2. Access Control

- Use IAM roles with least privilege
- Rotate credentials regularly
- Monitor access patterns
- Implement audit logging

### 3. Rate Limiting

- Implement application-level rate limiting
- Monitor for abuse patterns
- Set up automatic blocking for suspicious activity

## Disaster Recovery

### 1. Backup Strategies

- Regular backups of configuration
- Document manual override procedures
- Maintain fallback communication channels

### 2. Failover Procedures

1. **Primary System Failure**:
   - Switch to manual reminder process
   - Notify operations team
   - Begin recovery procedures

2. **AWS Service Outage**:
   - Use alternative scheduling mechanism
   - Queue reminders for later processing
   - Monitor AWS service status

3. **Twilio Service Outage**:
   - Switch to alternative SMS provider
   - Use email notifications as backup
   - Monitor Twilio status page

## Compliance and Auditing

### 1. HIPAA Compliance

- Ensure all logs are encrypted
- Implement access controls
- Maintain audit trails
- Regular compliance reviews

### 2. Audit Requirements

- Log all reminder operations
- Track consent and opt-out requests
- Maintain delivery confirmations
- Regular audit reports
