# WHO ICD API Usage Examples

This document provides practical examples of how to use the WHO ICD API integration in your healthcare application.

## Prerequisites

1. Ensure the WHO ICD API integration is properly configured
2. Have valid authentication tokens for your application
3. The server should be running and accessible

## Basic Usage Examples

### 1. Search for Diabetes Diagnoses

```bash
curl -X POST "http://localhost:8080/whoicd/search" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "query": "diabetes mellitus",
    "language": "en"
  }'
```

**Expected Response:**
```json
{
  "results": [
    {
      "id": "142601234",
      "title": "Type 2 diabetes mellitus",
      "code": "5A11",
      "definition": "A form of diabetes mellitus that is characterized by...",
      "browserUrl": "https://icd.who.int/browse11/l-m/en#/http://id.who.int/icd/entity/142601234",
      "score": 0.95
    }
  ],
  "totalResults": 25,
  "query": "diabetes mellitus",
  "releaseId": "2024-01",
  "language": "en"
}
```

### 2. Search Specifically in ICD-10

```bash
curl -X POST "http://localhost:8080/whoicd/search/icd10" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "query": "hypertension",
    "language": "en"
  }'
```

### 3. Search Specifically in ICD-11

```bash
curl -X POST "http://localhost:8080/whoicd/search/icd11" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "query": "covid-19",
    "language": "en"
  }'
```

### 4. Combined Search (Both ICD-10 and ICD-11)

```bash
curl -X POST "http://localhost:8080/whoicd/search/combined" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "query": "pneumonia",
    "language": "en"
  }'
```

**Expected Response:**
```json
{
  "query": "pneumonia",
  "icd10Results": [
    {
      "id": "J18.9",
      "title": "Pneumonia, unspecified organism",
      "code": "J18.9",
      "definition": "Pneumonia due to unspecified organism",
      "browserUrl": "...",
      "score": 0.92
    }
  ],
  "icd11Results": [
    {
      "id": "CA40.Z",
      "title": "Pneumonia, unspecified",
      "code": "CA40.Z",
      "definition": "Pneumonia, unspecified",
      "browserUrl": "...",
      "score": 0.94
    }
  ],
  "totalICD10Results": 15,
  "totalICD11Results": 18,
  "searchTimestamp": "2025-06-22T10:30:00Z"
}
```

### 5. Get Detailed Entity Information

```bash
curl -X GET "http://localhost:8080/whoicd/entity/142601234" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 6. Health Check

```bash
curl -X GET "http://localhost:8080/whoicd/health"
```

**Expected Response:**
```json
{
  "status": "healthy",
  "service": "WHO ICD API",
  "timestamp": "2025-06-22T10:30:00Z",
  "version": "1.0.0"
}
```

## JavaScript/Frontend Examples

### Using Fetch API

```javascript
// Search for diagnoses
async function searchDiagnosis(query, token) {
  try {
    const response = await fetch('/whoicd/search', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({
        query: query,
        language: 'en'
      })
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error searching diagnoses:', error);
    throw error;
  }
}

// Usage
searchDiagnosis('heart failure', 'your-jwt-token')
  .then(results => {
    console.log('Search results:', results);
    // Process results
    results.results.forEach(diagnosis => {
      console.log(`${diagnosis.code}: ${diagnosis.title}`);
    });
  })
  .catch(error => {
    console.error('Search failed:', error);
  });
```

### Using Axios

```javascript
import axios from 'axios';

const api = axios.create({
  baseURL: 'http://localhost:8080',
  headers: {
    'Content-Type': 'application/json'
  }
});

// Add auth token to requests
api.interceptors.request.use(config => {
  const token = localStorage.getItem('authToken');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Search functions
export const whoicdAPI = {
  search: async (query, language = 'en') => {
    const response = await api.post('/whoicd/search', {
      query,
      language
    });
    return response.data;
  },

  searchICD10: async (query, language = 'en') => {
    const response = await api.post('/whoicd/search/icd10', {
      query,
      language
    });
    return response.data;
  },

  searchICD11: async (query, language = 'en') => {
    const response = await api.post('/whoicd/search/icd11', {
      query,
      language
    });
    return response.data;
  },

  searchCombined: async (query, language = 'en') => {
    const response = await api.post('/whoicd/search/combined', {
      query,
      language
    });
    return response.data;
  },

  getEntity: async (entityId) => {
    const response = await api.get(`/whoicd/entity/${entityId}`);
    return response.data;
  },

  healthCheck: async () => {
    const response = await api.get('/whoicd/health');
    return response.data;
  }
};
```

## React Component Example

```jsx
import React, { useState, useEffect } from 'react';
import { whoicdAPI } from './api';

const DiagnosisSearch = () => {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const handleSearch = async (e) => {
    e.preventDefault();
    if (!query.trim()) return;

    setLoading(true);
    setError(null);

    try {
      const searchResults = await whoicdAPI.search(query);
      setResults(searchResults.results);
    } catch (err) {
      setError('Failed to search diagnoses. Please try again.');
      console.error('Search error:', err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="diagnosis-search">
      <form onSubmit={handleSearch}>
        <div>
          <label htmlFor="query">Search Diagnoses:</label>
          <input
            id="query"
            type="text"
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            placeholder="Enter diagnosis name..."
            disabled={loading}
          />
        </div>
        <button type="submit" disabled={loading || !query.trim()}>
          {loading ? 'Searching...' : 'Search'}
        </button>
      </form>

      {error && (
        <div className="error">
          {error}
        </div>
      )}

      {results.length > 0 && (
        <div className="results">
          <h3>Search Results:</h3>
          <ul>
            {results.map((diagnosis, index) => (
              <li key={diagnosis.id || index}>
                <strong>{diagnosis.code}</strong>: {diagnosis.title}
                {diagnosis.definition && (
                  <p className="definition">{diagnosis.definition}</p>
                )}
                {diagnosis.score && (
                  <span className="score">Score: {diagnosis.score.toFixed(2)}</span>
                )}
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};

export default DiagnosisSearch;
```

## Error Handling Examples

### Handling Different Error Types

```javascript
async function robustSearch(query) {
  try {
    const results = await whoicdAPI.search(query);
    return { success: true, data: results };
  } catch (error) {
    if (error.response) {
      // Server responded with error status
      const status = error.response.status;
      const message = error.response.data?.reason || 'Unknown error';
      
      switch (status) {
        case 400:
          return { success: false, error: 'Invalid search query', details: message };
        case 401:
          return { success: false, error: 'Authentication failed', details: message };
        case 429:
          return { success: false, error: 'Rate limit exceeded', details: message };
        case 503:
          return { success: false, error: 'Service unavailable', details: message };
        default:
          return { success: false, error: 'Server error', details: message };
      }
    } else if (error.request) {
      // Network error
      return { success: false, error: 'Network error', details: 'Unable to reach server' };
    } else {
      // Other error
      return { success: false, error: 'Unexpected error', details: error.message };
    }
  }
}
```

## Best Practices

1. **Always validate input**: Ensure search queries are at least 2 characters long
2. **Handle rate limits**: Implement retry logic with exponential backoff
3. **Cache results**: Consider caching frequent searches to reduce API calls
4. **Error handling**: Always handle different types of errors gracefully
5. **Loading states**: Show loading indicators during API calls
6. **Debounce searches**: For real-time search, debounce user input to avoid excessive API calls

## Common Search Terms

Here are some common medical terms you can test with:

- "diabetes"
- "hypertension"
- "pneumonia"
- "covid-19"
- "heart failure"
- "asthma"
- "depression"
- "migraine"
- "arthritis"
- "cancer"

## Troubleshooting

### Common Issues

1. **401 Unauthorized**: Check your JWT token is valid and not expired
2. **400 Bad Request**: Ensure your request body is properly formatted JSON
3. **503 Service Unavailable**: The WHO ICD API might be temporarily unavailable
4. **Rate Limiting**: Implement proper rate limiting in your application

### Debug Mode

To enable debug logging, check the server logs for detailed request/response information.
