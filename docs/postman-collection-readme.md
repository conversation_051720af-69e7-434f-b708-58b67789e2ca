# HMBL Core - Member Diagnoses, Medications & WHO ICD API Postman Collection

## Overview

This comprehensive Postman collection provides complete API testing capabilities for the HMBL Core healthcare platform, including:

- **WHO ICD API Integration** - Search and retrieve standardized disease classifications
- **Member Diagnoses Management** - Create, read, update, and delete member diagnoses
- **Member Medications Management** - Manage member medication records
- **Integrated Workflows** - Complete workflows demonstrating WHO ICD API integration with diagnosis creation

## Collection Structure

### 1. WHO ICD API
Core endpoints for accessing the World Health Organization's International Classification of Diseases database:

- **Health Check** - Verify WHO ICD API service status
- **Search Diagnoses (General)** - Search across default ICD release (ICD-11 2024-01)
- **Search ICD-10 Diagnoses** - Search specifically within ICD-10 classification
- **Search ICD-11 Diagnoses** - Search specifically within ICD-11 classification
- **Combined Search** - Search both ICD-10 and ICD-11 simultaneously
- **Get Entity Details** - Retrieve detailed information about specific ICD entities
- **Test Authentication Token** - Debug authentication issues

### 2. WHO ICD Examples
Pre-configured searches for common medical conditions:

- **Heart Conditions** - Search for heart failure and cardiovascular diseases
- **Respiratory Conditions** - Search for asthma and respiratory disorders
- **Mental Health** - Search for depression and mental health conditions
- **Infectious Diseases** - Search for influenza and infectious diseases
- **Cancer** - Search for breast cancer and oncological conditions

### 3. Member Diagnoses
Complete CRUD operations for member diagnoses:

- **Create Diagnosis** - Standard diagnosis creation
- **Create Diagnosis (WHO ICD Example)** - Diagnosis creation using WHO ICD API results
- **List Member Diagnoses** - Retrieve all diagnoses for a member
- **Get Diagnosis** - Retrieve specific diagnosis details
- **Update Diagnosis** - Modify existing diagnosis
- **Delete Diagnosis** - Remove diagnosis record

### 4. Diagnosis Workflow (WHO ICD Integration)
Step-by-step workflow demonstrating complete integration:

- **Step 1: Search WHO ICD for Condition** - Find appropriate ICD codes
- **Step 2: Get Detailed Entity Information** - Retrieve comprehensive entity details
- **Step 3: Create Diagnosis with WHO ICD Data** - Create diagnosis using WHO ICD results
- **Step 4: Verify Created Diagnosis** - Confirm successful diagnosis creation

### 5. Member Medications
Complete medication management endpoints:

- **Create Medication** - Add new medication records
- **List Member Medications** - Retrieve all medications for a member
- **Get Active Medications** - Retrieve only active medications
- **Get Medications by Type** - Filter medications by type (prescribed, OTC, etc.)
- **Get Medication** - Retrieve specific medication details
- **Update Medication** - Modify existing medication
- **Discontinue Medication** - Mark medication as discontinued
- **Delete Medication** - Remove medication record

## Setup Instructions

### 1. Import Collection
1. Download the `Member_Diagnoses_Medications_Postman_Collection.json` file
2. Open Postman
3. Click "Import" and select the JSON file
4. The collection will be imported with all endpoints and examples

### 2. Configure Variables
Update the collection variables with your environment-specific values:

```
baseUrl: http://localhost:8080 (or your server URL)
memberID: 123e4567-e89b-12d3-a456-************ (valid member UUID)
diagnosisID: 123e4567-e89b-12d3-a456-************ (valid diagnosis UUID)
medicationID: 123e4567-e89b-12d3-a456-************ (valid medication UUID)
authToken: your-jwt-token-here (valid JWT authentication token)
whoEntityId: 142601234 (valid WHO ICD entity ID)
```

### 3. Authentication Setup
Most endpoints require JWT authentication. To set up authentication:

1. Obtain a valid JWT token from your authentication endpoint
2. Update the `authToken` variable with your token
3. The collection automatically includes the Authorization header where needed

## Usage Examples

### Basic WHO ICD Search
1. Navigate to "WHO ICD API" → "Search Diagnoses (General)"
2. Modify the request body to search for your condition:
   ```json
   {
     "query": "your search term",
     "language": "en"
   }
   ```
3. Send the request to get search results

### Complete Diagnosis Workflow
1. **Search for Condition**: Use "WHO ICD Examples" → "Search - Heart Conditions"
2. **Get Entity Details**: Copy an entity ID from search results and use "Get Entity Details"
3. **Create Diagnosis**: Use the detailed information to create a diagnosis via "Create Diagnosis (WHO ICD Example)"
4. **Verify**: Check the created diagnosis using "List Member Diagnoses"

### Medication Management
1. **Create**: Use "Create Medication" with appropriate medication details
2. **List**: View all medications with "List Member Medications"
3. **Filter**: Use "Get Active Medications" or "Get Medications by Type"
4. **Update**: Modify dosage or status with "Update Medication"
5. **Discontinue**: Mark as discontinued with "Discontinue Medication"

## WHO ICD API Integration Benefits

### Standardized Coding
- Access to official WHO ICD-10 and ICD-11 classifications
- Standardized disease codes and descriptions
- Consistent terminology across healthcare systems

### Enhanced Accuracy
- Reduce coding errors with official WHO classifications
- Access to detailed diagnostic criteria and definitions
- Multiple language support for international use

### Comprehensive Coverage
- Search across both ICD-10 and ICD-11 simultaneously
- Access to latest disease classifications and updates
- Detailed entity relationships and hierarchies

## Common Use Cases

### 1. Diagnosis Code Lookup
```
Search WHO ICD → Get detailed entity → Create diagnosis with official code
```

### 2. Condition Research
```
Search multiple terms → Compare ICD-10 vs ICD-11 → Select appropriate classification
```

### 3. Bulk Diagnosis Creation
```
Search WHO ICD for multiple conditions → Create standardized diagnosis records
```

### 4. Code Validation
```
Verify existing diagnosis codes → Update with current WHO standards
```

## Error Handling

The collection includes comprehensive error handling examples:

- **400 Bad Request** - Invalid search parameters or malformed requests
- **401 Unauthorized** - Authentication failures or expired tokens
- **404 Not Found** - Entity or resource not found
- **429 Rate Limit** - Too many requests to WHO ICD API
- **503 Service Unavailable** - WHO ICD service temporarily unavailable

## Best Practices

### 1. Authentication
- Always use valid JWT tokens
- Refresh tokens before they expire
- Store tokens securely

### 2. WHO ICD API Usage
- Use specific searches when possible (ICD-10 vs ICD-11)
- Cache frequently used entity details
- Respect rate limits

### 3. Data Management
- Validate ICD codes before creating diagnoses
- Include WHO entity IDs for traceability
- Maintain audit trails for diagnosis changes

### 4. Testing
- Test with various search terms
- Verify error handling scenarios
- Test complete workflows end-to-end

## Troubleshooting

### Common Issues

1. **Authentication Errors**
   - Verify JWT token is valid and not expired
   - Check Authorization header format
   - Ensure user has appropriate permissions

2. **WHO ICD API Errors**
   - Check internet connectivity
   - Verify WHO ICD service status
   - Review search query format

3. **Data Validation Errors**
   - Ensure required fields are provided
   - Validate UUID formats for IDs
   - Check date formats (ISO 8601)

### Debug Steps

1. **Use Health Check**: Start with WHO ICD health check endpoint
2. **Test Authentication**: Use the token test endpoint
3. **Validate Data**: Check request body format and required fields
4. **Review Logs**: Check server logs for detailed error information

## Support

For technical support or questions about the Postman collection:

1. Review the API documentation in `docs/whoicd-api-documentation.md`
2. Check usage examples in `docs/whoicd-usage-examples.md`
3. Refer to the OpenAPI specification in `docs/whoicd-openapi.yaml`
4. Contact the development team for additional assistance

## Version History

- **v2.0.0** - Added WHO ICD API integration with complete workflow examples
- **v1.0.0** - Initial collection with Member Diagnoses and Medications endpoints
