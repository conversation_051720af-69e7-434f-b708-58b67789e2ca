# WHO ICD API Integration Documentation

## Overview

The WHO ICD API integration provides access to the World Health Organization's International Classification of Diseases (ICD) database, supporting both ICD-10 and ICD-11 classifications. This integration allows healthcare applications to search for diagnoses, retrieve detailed disease information, and access standardized medical coding.

## Base URL

All WHO ICD API endpoints are available under the `/whoicd` path:

```
https://your-api-domain.com/whoicd
```

## Authentication

The WHO ICD API uses OAuth 2.0 client credentials flow for authentication. The integration automatically handles token management using the configured client credentials.

### Configuration

The following credentials are configured in the system:
- **Client ID**: `1d7b63c0-9c0b-4852-b756-46219fd7f588_e2c51b17-9a8f-4bce-810c-640268b6c56b`
- **Client Secret**: `yyDt5vyvgSiSNwOlts2/NFvXl0TqpiLxgpiE8WVDmks=`
- **Scope**: `icdapi_access`

## Endpoints

### 1. General Diagnosis Search

**POST** `/whoicd/search`

Search for diagnoses across the default ICD release (ICD-11 2024-01).

#### Request Body

```json
{
  "query": "diabetes",
  "releaseId": "2024-01",
  "language": "en"
}
```

#### Parameters

- `query` (string, required): Search term for diagnosis
- `releaseId` (string, optional): ICD release ID (default: "2024-01")
- `language` (string, optional): Language code (default: "en")

#### Response

```json
{
  "results": [
    {
      "id": "142601234",
      "title": "Type 2 diabetes mellitus",
      "code": "5A11",
      "definition": "A form of diabetes mellitus...",
      "browserUrl": "https://icd.who.int/browse11/l-m/en#/http://id.who.int/icd/entity/142601234",
      "score": 0.95
    }
  ],
  "totalResults": 25,
  "query": "diabetes",
  "releaseId": "2024-01",
  "language": "en"
}
```

### 2. ICD-10 Specific Search

**POST** `/whoicd/search/icd10`

Search specifically within ICD-10 classification.

#### Request Body

```json
{
  "query": "hypertension",
  "language": "en"
}
```

#### Response

Returns results from ICD-10 (release 2019-04) with the same structure as general search.

### 3. ICD-11 Specific Search

**POST** `/whoicd/search/icd11`

Search specifically within ICD-11 classification.

#### Request Body

```json
{
  "query": "covid",
  "language": "en"
}
```

#### Response

Returns results from ICD-11 (release 2024-01) with the same structure as general search.

### 4. Combined Search

**POST** `/whoicd/search/combined`

Search across both ICD-10 and ICD-11 simultaneously.

#### Request Body

```json
{
  "query": "pneumonia",
  "language": "en"
}
```

#### Response

```json
{
  "query": "pneumonia",
  "icd10Results": [
    {
      "id": "J18.9",
      "title": "Pneumonia, unspecified organism",
      "code": "J18.9",
      "definition": "Pneumonia due to unspecified organism",
      "browserUrl": "...",
      "score": 0.92
    }
  ],
  "icd11Results": [
    {
      "id": "CA40.Z",
      "title": "Pneumonia, unspecified",
      "code": "CA40.Z",
      "definition": "Pneumonia, unspecified",
      "browserUrl": "...",
      "score": 0.94
    }
  ],
  "totalICD10Results": 15,
  "totalICD11Results": 18,
  "searchTimestamp": "2025-06-22T10:30:00Z"
}
```

### 5. Get Entity Details

**GET** `/whoicd/entity/{entityId}`

Retrieve detailed information about a specific ICD entity.

#### Parameters

- `entityId` (string, required): The unique identifier of the ICD entity

#### Response

```json
{
  "id": "142601234",
  "title": "Type 2 diabetes mellitus",
  "definition": "A form of diabetes mellitus that is characterized by...",
  "longDefinition": "Type 2 diabetes mellitus is a metabolic disorder...",
  "fullySpecifiedName": "Type 2 diabetes mellitus",
  "diagnosticCriteria": "Diagnosis is based on...",
  "codingNote": "Use additional code to identify...",
  "code": "5A11",
  "classKind": "category",
  "child": ["142601235", "142601236"],
  "parent": ["142601233"],
  "browserUrl": "https://icd.who.int/browse11/l-m/en#/http://id.who.int/icd/entity/142601234"
}
```

### 6. Health Check

**GET** `/whoicd/health`

Check the health status of the WHO ICD API integration.

#### Response

```json
{
  "status": "healthy",
  "service": "WHO ICD API",
  "timestamp": "2025-06-22T10:30:00Z",
  "version": "1.0.0"
}
```

### 7. Test Token

**GET** `/whoicd/token/test`

Test the authentication token retrieval (for debugging purposes).

#### Response

```json
{
  "access_token": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "Bearer",
  "expires_in": 3600,
  "scope": "icdapi_access"
}
```

## Error Handling

The API returns standard HTTP status codes and error messages:

### Common Error Responses

#### 400 Bad Request
```json
{
  "error": true,
  "reason": "Invalid WHO ICD request: Query parameter is required"
}
```

#### 401 Unauthorized
```json
{
  "error": true,
  "reason": "WHO ICD authentication failed: Invalid client credentials"
}
```

#### 404 Not Found
```json
{
  "error": true,
  "reason": "WHO ICD entity not found: Entity ID does not exist"
}
```

#### 429 Too Many Requests
```json
{
  "error": true,
  "reason": "WHO ICD API rate limit exceeded"
}
```

#### 503 Service Unavailable
```json
{
  "error": true,
  "reason": "WHO ICD service is currently unavailable"
}
```

## Usage Examples

### Search for Diabetes Diagnoses

```bash
curl -X POST "https://your-api-domain.com/whoicd/search" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "query": "diabetes mellitus",
    "language": "en"
  }'
```

### Get Specific Entity Details

```bash
curl -X GET "https://your-api-domain.com/whoicd/entity/142601234" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Combined ICD-10 and ICD-11 Search

```bash
curl -X POST "https://your-api-domain.com/whoicd/search/combined" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "query": "heart failure",
    "language": "en"
  }'
```

## Rate Limits

The WHO ICD API has rate limits in place. The integration handles rate limiting gracefully and returns appropriate error messages when limits are exceeded.

## Supported Languages

The API supports multiple languages. Common language codes include:
- `en` - English
- `es` - Spanish
- `fr` - French
- `de` - German
- `zh` - Chinese

## ICD Release Versions

### ICD-10
- Release ID: `2019-04`
- Description: ICD-10 WHO version

### ICD-11
- Release ID: `2024-01`
- Description: ICD-11 for Mortality and Morbidity Statistics (MMS)

## Integration Notes

1. **Token Management**: The integration automatically handles OAuth token refresh
2. **Caching**: Consider implementing caching for frequently searched terms
3. **Logging**: All requests and responses are logged for monitoring and debugging
4. **Error Recovery**: The system includes comprehensive error handling and recovery mechanisms

## Support

For technical support or questions about the WHO ICD API integration, please contact the development team or refer to the official WHO ICD API documentation at https://icd.who.int/docs/icd-api/
