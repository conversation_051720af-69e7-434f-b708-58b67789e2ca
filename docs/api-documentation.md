# DonaHeath Core API Documentation

## Introduction

The DonaHeath Core API provides a comprehensive set of endpoints for managing healthcare coordination services. Built with Swift and the Vapor framework, this API enables applications to manage users, members, organizations, care packages, and more.

This documentation is intended for developers integrating with the DonaHeath platform.

---

## Quick Reference

- **Base URL**: 
  - Production: [Production URL]
  - Staging: [Staging URL]
  - Development: [Development URL]

- **Authentication**: JWT (JSON Web Tokens)

- **Content Type**: `application/json`

---

## Authentication

### Getting Started with Authentication

Most endpoints require authentication using JSON Web Tokens (JWT). To authenticate:

1. Register a user or member account
2. Login to receive a token
3. Include the token in the `Authorization` header of subsequent requests

### User Authentication Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/auth/signup` | POST | Register a new user |
| `/auth/login` | POST | Login and receive a token |
| `/auth/session` | GET | Get current session info |

#### Example: User Registration

```http
POST /auth/signup
Content-Type: application/json

{
  "username": "johndoe",
  "password": "securepassword",
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>"
}
```

#### Example: User Login

```http
POST /auth/login
Content-Type: application/json

{
  "username": "johndoe",
  "password": "securepassword"
}
```

Response:
```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": "550e8400-e29b-41d4-a716-************",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "roles": ["admin"]
  }
}
```

### Member Authentication Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/auth/memberSignup` | POST | Register a new member |
| `/auth/memberLogin` | POST | Login as a member |

### Multi-Factor Authentication

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/auth/mfaCreateUser` | POST | Create a user with MFA |
| `/auth/mfaLogin` | POST | Login with MFA |
| `/auth/mfaVerify` | POST | Verify MFA code |

---

## Core Resources

### Users

Users represent administrators, healthcare providers, and other staff members.

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/users` | GET | List all users |
| `/users` | POST | Create a user |
| `/users/:userID` | GET | Get a specific user |
| `/users/:userID` | PUT | Update a user |
| `/users/:userID` | DELETE | Delete a user |
| `/users/:userID/chats` | GET | Get user's chats |
| `/users/:userID/memberChats` | GET | Get user's member chats |
| `/users/:userID/verify` | GET | Verify a user |
| `/users/:userID/attachments` | POST | Add an attachment |

#### Example: Create a User

```http
POST /users
Content-Type: application/json
Authorization: Bearer {token}

{
  "email": "<EMAIL>",
  "firstName": "Jane",
  "lastName": "Smith",
  "roles": ["provider"]
}
```

### Members

Members represent patients or clients receiving care.

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/members` | GET | List all members |
| `/members` | POST | Create a member |
| `/members/:memberID` | GET | Get a specific member |
| `/members/:memberID` | PUT | Update a member |
| `/members/:memberID` | DELETE | Delete a member |

#### Example: Create a Member

```http
POST /members
Content-Type: application/json
Authorization: Bearer {token}

{
  "email": "<EMAIL>",
  "firstName": "Patient",
  "lastName": "Name",
  "type": "client",
  "dob": "1980-01-01",
  "gender": "female",
  "status": "active"
}
```

### Organizations

Organizations represent healthcare providers, clinics, or other entities.

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/orgs` | GET | List all organizations |
| `/orgs` | POST | Create an organization |
| `/orgs/:orgID` | GET | Get a specific organization |
| `/orgs/:orgID` | PUT | Update an organization |
| `/orgs/:orgID` | DELETE | Delete an organization |

### Teams

Teams represent groups of users working together.

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/teams` | GET | List all teams |
| `/teams` | POST | Create a team |
| `/teams/:teamID` | GET | Get a specific team |
| `/teams/:teamID` | PUT | Update a team |
| `/teams/:teamID` | DELETE | Delete a team |

### Households

Households represent family or living units.

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/households` | GET | List all households |
| `/households` | POST | Create a household |
| `/households/:householdID` | GET | Get a specific household |
| `/households/:householdID` | PUT | Update a household |
| `/households/:householdID` | DELETE | Delete a household |

---

## Care Coordination

### Care Packages

Care packages represent bundles of services for members.

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/carePackages` | GET | List all care packages |
| `/carePackages` | POST | Create a care package |
| `/carePackages/:packageID` | GET | Get a specific care package |
| `/carePackages/:packageID` | PUT | Update a care package |
| `/carePackages/:packageID` | DELETE | Delete a care package |

### Services

Services represent healthcare or social services that can be provided.

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/services` | GET | List all services |
| `/services` | POST | Create a service |
| `/services/:serviceID` | GET | Get a specific service |
| `/services/:serviceID` | PUT | Update a service |
| `/services/:serviceID` | DELETE | Delete a service |

### Networks

Networks represent service provider networks.

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/networks` | GET | List all networks |
| `/networks` | POST | Create a network |
| `/networks/:networkID` | GET | Get a specific network |
| `/networks/:networkID` | PUT | Update a network |
| `/networks/:networkID` | DELETE | Delete a network |

### Appointments

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/appointments` | GET | List all appointments |
| `/appointments` | POST | Create an appointment |
| `/appointments/:appointmentID` | GET | Get a specific appointment |
| `/appointments/:appointmentID` | PUT | Update an appointment |
| `/appointments/:appointmentID` | DELETE | Delete an appointment |

### Tasks

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/tasks` | GET | List all tasks |
| `/tasks` | POST | Create a task |
| `/tasks/:taskID` | GET | Get a specific task |
| `/tasks/:taskID` | PUT | Update a task |
| `/tasks/:taskID` | DELETE | Delete a task |

---

## Communication

### Notes

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/notes` | GET | List all notes |
| `/notes` | POST | Create a note |
| `/notes/:noteID` | GET | Get a specific note |
| `/notes/:noteID` | PUT | Update a note |
| `/notes/:noteID` | DELETE | Delete a note |

### Chats

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/chats` | GET | List all chats |
| `/chats` | POST | Create a chat |
| `/chats/:chatID` | GET | Get a specific chat |
| `/chats/:chatID` | PUT | Update a chat |
| `/chats/:chatID` | DELETE | Delete a chat |

### Member Chats

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/memberChats` | GET | List all member chats |
| `/memberChats` | POST | Create a member chat |
| `/memberChats/:chatID` | GET | Get a specific member chat |
| `/memberChats/:chatID` | PUT | Update a member chat |
| `/memberChats/:chatID` | DELETE | Delete a member chat |

### Notifications

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/notifications` | GET | List all notifications |
| `/notifications` | POST | Create a notification |
| `/notifications/:notificationID` | GET | Get a specific notification |
| `/notifications/:notificationID` | PUT | Update a notification |
| `/notifications/:notificationID` | DELETE | Delete a notification |

### SMS Integration (Twilio)

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/sms` | POST | Handle incoming SMS |
| `/failedSMS` | POST | Handle failed SMS |

### Push Notifications (APNS)

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/pushService` | POST | Test push notifications |

---

## Assessment & Reporting

### Surveys

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/surveys` | GET | List all surveys |
| `/surveys` | POST | Create a survey |
| `/surveys/:surveyID` | GET | Get a specific survey |
| `/surveys/:surveyID` | PUT | Update a survey |
| `/surveys/:surveyID` | DELETE | Delete a survey |

### Sections

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/sections` | GET | List all sections |
| `/sections` | POST | Create a section |
| `/sections/:sectionID` | GET | Get a specific section |
| `/sections/:sectionID` | PUT | Update a section |
| `/sections/:sectionID` | DELETE | Delete a section |

### Questions

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/questions` | GET | List all questions |
| `/questions` | POST | Create a question |
| `/questions/:questionID` | GET | Get a specific question |
| `/questions/:questionID` | PUT | Update a question |
| `/questions/:questionID` | DELETE | Delete a question |

### Answers

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/answers` | GET | List all answers |
| `/answers` | POST | Create an answer |
| `/answers/:answerID` | GET | Get a specific answer |
| `/answers/:answerID` | PUT | Update an answer |
| `/answers/:answerID` | DELETE | Delete an answer |

### Templates

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/templates` | GET | List all templates |
| `/templates` | POST | Create a template |
| `/templates/:templateID` | GET | Get a specific template |
| `/templates/:templateID` | PUT | Update a template |
| `/templates/:templateID` | DELETE | Delete a template |

### Reports

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/dashboard` | POST | Get dashboard data |
| `/api/dashboard/export` | POST | Export dashboard data |
| `/api/dashboard/exportAllData` | POST | Export all data |
| `/api/report` | POST | Generate a CSV report |
| `/api/empowered` | POST | Generate an empowered report |
| `/api/custom` | POST | Generate a custom report |

---

## Additional Resources

### Insurance

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/insurance` | GET | List all insurance records |
| `/insurance` | POST | Create an insurance record |
| `/insurance/:insuranceID` | GET | Get a specific insurance record |
| `/insurance/:insuranceID` | PUT | Update an insurance record |
| `/insurance/:insuranceID` | DELETE | Delete an insurance record |

### Carriers

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/carriers` | GET | List all carriers |
| `/carriers` | POST | Create a carrier |
| `/carriers/:carrierID` | GET | Get a specific carrier |
| `/carriers/:carrierID` | PUT | Update a carrier |
| `/carriers/:carrierID` | DELETE | Delete a carrier |

### Content

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/content` | GET | List all content |
| `/content` | POST | Create content |
| `/content/:contentID` | GET | Get specific content |
| `/content/:contentID` | PUT | Update content |
| `/content/:contentID` | DELETE | Delete content |

### Timeline

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/timeline` | GET | List timeline events |
| `/timeline` | POST | Create a timeline event |
| `/timeline/:timelineID` | GET | Get a specific timeline event |
| `/timeline/:timelineID` | PUT | Update a timeline event |
| `/timeline/:timelineID` | DELETE | Delete a timeline event |

### Tags

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/tags` | GET | List all tags |
| `/tags` | POST | Create a tag |
| `/tags/:tagID` | GET | Get a specific tag |
| `/tags/:tagID` | PUT | Update a tag |
| `/tags/:tagID` | DELETE | Delete a tag |

---

## Common Data Models

### User

```json
{
  "id": "550e8400-e29b-41d4-a716-************",
  "email": "<EMAIL>",
  "firstName": "John",
  "lastName": "Doe",
  "profile": "https://example.com/profile.jpg",
  "color": "#4287f5",
  "roles": ["admin", "provider"],
  "createdAt": "2023-01-15T12:00:00Z",
  "updatedAt": "2023-01-16T09:30:00Z"
}
```

### Member

```json
{
  "id": "550e8400-e29b-41d4-a716-************",
  "email": "<EMAIL>",
  "firstName": "Jane",
  "lastName": "Smith",
  "type": "client",
  "dob": "1985-03-22",
  "gender": "female",
  "status": "active"
}
```

### Organization

```json
{
  "id": "550e8400-e29b-41d4-a716-************",
  "name": "Health Clinic",
  "description": "Community health provider",
  "logo": "https://example.com/logo.png",
  "color": "#42f5a7"
}
```

### Task

```json
{
  "id": "550e8400-e29b-41d4-a716-************",
  "title": "Follow-up call",
  "description": "Call member to check on medication adherence",
  "status": "pending",
  "priority": "high",
  "dueDate": "2023-02-01T15:00:00Z",
  "creatorID": "550e8400-e29b-41d4-a716-446655440001",
  "assigneeID": "550e8400-e29b-41d4-a716-446655440002"
}
```

---

## Error Handling

The API uses standard HTTP status codes to indicate success or failure:

| Status Code | Description |
|-------------|-------------|
| 200 | OK - The request succeeded |
| 201 | Created - A new resource was created |
| 400 | Bad Request - The request was invalid |
| 401 | Unauthorized - Authentication failed |
| 403 | Forbidden - The user doesn't have permission |
| 404 | Not Found - The resource wasn't found |
| 500 | Internal Server Error - Server-side error |

### Error Response Format

```json
{
  "error": true,
  "reason": "Detailed error message",
  "code": 400
}
```

---

## Rate Limiting

To ensure API stability, rate limits are enforced. The following headers provide information about rate limits:

| Header | Description |
|--------|-------------|
| X-RateLimit-Limit | Maximum requests allowed in the time window |
| X-RateLimit-Remaining | Requests remaining in the current window |
| X-RateLimit-Reset | Time when the current window resets (Unix timestamp) |

---

## API Versioning

The API version is specified in the URL path:

```
/v1/users
```

The current version is v1. 