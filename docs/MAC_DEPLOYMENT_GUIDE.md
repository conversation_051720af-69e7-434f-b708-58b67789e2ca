# SMS Reminder System - Mac Deployment Guide

This guide will walk you through deploying the SMS reminder system to AWS from your Mac using AWS CLI.

## Prerequisites

### 1. Install AWS CLI

```bash
# Install using Homebrew (recommended)
brew install awscli

# Or install using pip
pip3 install awscli

# Verify installation
aws --version
```

### 2. Configure AWS CLI

```bash
# Configure AWS credentials
aws configure

# You'll be prompted for:
# AWS Access Key ID: [Your access key]
# AWS Secret Access Key: [Your secret key]
# Default region name: us-east-1
# Default output format: json

# Verify configuration
aws sts get-caller-identity
```

### 3. Install Required Tools

```bash
# Ensure you have zip (usually pre-installed on Mac)
which zip

# Install Node.js if not already installed (for Lambda function)
brew install node
```

## Deployment Steps

### Step 1: Prepare the Deployment Script

```bash
# Navigate to your project root
cd /path/to/hmbl-core

# Make the deployment script executable
chmod +x scripts/deploy-sms-reminder-aws.sh

# Review the script configuration (optional)
nano scripts/deploy-sms-reminder-aws.sh
```

### Step 2: Run the Deployment Script

```bash
# Run the deployment script
./scripts/deploy-sms-reminder-aws.sh

# The script will:
# ✅ Check prerequisites
# ✅ Create IAM roles
# ✅ Create CloudWatch Log Group
# ✅ Package and deploy Lambda function
# ✅ Create EventBridge Scheduler Group
# ✅ Create CloudWatch Alarms
# ✅ Test the Lambda function
# ✅ Generate environment variables
```

### Step 3: Update Your Environment Variables

After the script completes, you'll see output like this:

```
==========================================
Add these variables to your .env file:
==========================================
SMS_REMINDER_LAMBDA_ARN=arn:aws:lambda:us-east-2:123456789012:function:hmbl-sms-reminders-handler-prod
SMS_REMINDER_EXECUTION_ROLE_ARN=arn:aws:iam::123456789012:role/hmbl-sms-reminders-scheduler-role-prod
SMS_REMINDER_SCHEDULE_GROUP=hmbl-sms-reminders-prod
SMS_REMINDER_CLOUDWATCH_NAMESPACE=HMBL/SMSReminders
SMS_REMINDER_LOG_GROUP=/aws/lambda/hmbl-sms-reminders-handler-prod
==========================================
```

**Update your `.env` file:**

```bash
# Edit your .env file
nano .env

# Replace the placeholder values with the generated ones:
SMS_REMINDER_LAMBDA_ARN=arn:aws:lambda:us-east-2:123456789012:function:hmbl-sms-reminders-handler-prod
SMS_REMINDER_EXECUTION_ROLE_ARN=arn:aws:iam::123456789012:role/hmbl-sms-reminders-scheduler-role-prod
SMS_REMINDER_SCHEDULE_GROUP=hmbl-sms-reminders-prod
SMS_REMINDER_CLOUDWATCH_NAMESPACE=HMBL/SMSReminders
SMS_REMINDER_LOG_GROUP=/aws/lambda/hmbl-sms-reminders-handler-prod
```

### Step 4: Test the Deployment

```bash
# Test Lambda function directly
aws lambda invoke \
  --function-name hmbl-sms-reminders-handler-prod \
  --payload '{"phoneNumber":"+15551234567","message":"Test from Mac deployment","reminderType":"test"}' \
  response.json

# Check the response
cat response.json

# Test EventBridge Scheduler
aws scheduler list-schedule-groups

# Check CloudWatch logs
aws logs describe-log-groups --log-group-name-prefix "/aws/lambda/hmbl-sms-reminders"
```

### Step 5: Restart Your Vapor Application

```bash
# Restart your Vapor app to pick up new environment variables
# This depends on how you're running your app

# If running locally:
swift run

# If using Docker:
docker-compose restart

# If using systemd:
sudo systemctl restart your-vapor-app
```

## Verification Steps

### 1. Test SMS Reminder Scheduling

Create a test appointment or task in your application and verify:

```bash
# Check EventBridge schedules
aws scheduler list-schedules --group-name hmbl-sms-reminders-prod

# Check CloudWatch logs
aws logs tail /aws/lambda/hmbl-sms-reminders-handler-prod --follow
```

### 2. Monitor CloudWatch Metrics

```bash
# View custom metrics
aws cloudwatch list-metrics --namespace HMBL/SMSReminders

# Check alarms
aws cloudwatch describe-alarms --alarm-name-prefix "hmbl-sms-reminders"
```

### 3. Test End-to-End Flow

1. Create an appointment in your app with a future date
2. Check that reminders are scheduled:
   ```bash
   aws scheduler list-schedules --group-name hmbl-sms-reminders-prod
   ```
3. Wait for the scheduled time or manually trigger the Lambda
4. Verify SMS delivery in Twilio logs

## Troubleshooting

### Common Issues

#### 1. "AccessDenied" Errors

**Problem**: Insufficient AWS permissions

**Solution**:
```bash
# Check your AWS identity
aws sts get-caller-identity

# Ensure your AWS user has these permissions:
# - IAM: CreateRole, PutRolePolicy, GetRole
# - Lambda: CreateFunction, UpdateFunctionCode, UpdateFunctionConfiguration
# - EventBridge Scheduler: CreateScheduleGroup, CreateSchedule
# - CloudWatch: CreateLogGroup, PutMetricAlarm
# - Logs: CreateLogGroup, PutRetentionPolicy
```

#### 2. "Role not found" Errors

**Problem**: IAM roles not created or not available yet

**Solution**:
```bash
# Wait a few minutes for IAM propagation
sleep 60

# Re-run the deployment script
./scripts/deploy-sms-reminder-aws.sh
```

#### 3. Lambda Function Errors

**Problem**: Lambda function fails to execute

**Solution**:
```bash
# Check Lambda logs
aws logs tail /aws/lambda/hmbl-sms-reminders-handler-prod

# Test with a simple payload
aws lambda invoke \
  --function-name hmbl-sms-reminders-handler-prod \
  --payload '{"phoneNumber":"+15551234567","message":"Test"}' \
  test-response.json

# Check the response
cat test-response.json
```

#### 4. Environment Variable Issues

**Problem**: Vapor app can't find environment variables

**Solution**:
```bash
# Verify .env file
cat .env | grep SMS_REMINDER

# Restart Vapor application
# Make sure to restart after updating .env
```

## Advanced Configuration

### Multiple Environments

To deploy to different environments (dev, staging, prod):

```bash
# Edit the script to change environment
nano scripts/deploy-sms-reminder-aws.sh

# Change this line:
ENVIRONMENT="dev"  # or "staging" or "prod"

# Run deployment
./scripts/deploy-sms-reminder-aws.sh
```

### Custom Configuration

You can customize the deployment by editing these variables in the script:

```bash
AWS_REGION="us-east-1"           # Change AWS region
ENVIRONMENT="prod"               # Change environment
PROJECT_NAME="hmbl-sms-reminders" # Change project name
```

### Monitoring Setup

Set up additional monitoring:

```bash
# Create SNS topic for alerts
aws sns create-topic --name sms-reminder-alerts

# Subscribe to alerts
aws sns subscribe \
  --topic-arn arn:aws:sns:us-east-1:123456789012:sms-reminder-alerts \
  --protocol email \
  --notification-endpoint <EMAIL>

# Update alarms to use SNS
aws cloudwatch put-metric-alarm \
  --alarm-name "hmbl-sms-reminders-lambda-errors-prod" \
  --alarm-actions arn:aws:sns:us-east-1:123456789012:sms-reminder-alerts \
  # ... other alarm parameters
```

## Cleanup (if needed)

To remove all AWS resources:

```bash
# Delete Lambda function
aws lambda delete-function --function-name hmbl-sms-reminders-handler-prod

# Delete IAM roles
aws iam delete-role-policy --role-name hmbl-sms-reminders-lambda-role-prod --policy-name LambdaExecutionPolicy
aws iam delete-role --role-name hmbl-sms-reminders-lambda-role-prod

aws iam delete-role-policy --role-name hmbl-sms-reminders-scheduler-role-prod --policy-name SchedulerExecutionPolicy
aws iam delete-role --role-name hmbl-sms-reminders-scheduler-role-prod

# Delete schedule group (after deleting all schedules)
aws scheduler delete-schedule-group --name hmbl-sms-reminders-prod

# Delete CloudWatch log group
aws logs delete-log-group --log-group-name /aws/lambda/hmbl-sms-reminders-handler-prod

# Delete CloudWatch alarms
aws cloudwatch delete-alarms --alarm-names hmbl-sms-reminders-lambda-errors-prod hmbl-sms-reminders-lambda-duration-prod
```

## Support

If you encounter issues:

1. Check the AWS CloudWatch logs
2. Verify all environment variables are set correctly
3. Ensure AWS credentials have sufficient permissions
4. Test each component individually (Lambda, EventBridge, etc.)

The deployment script includes comprehensive error checking and will guide you through any issues.
