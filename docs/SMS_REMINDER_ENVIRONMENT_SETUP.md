# SMS Reminder System Environment Configuration

This document outlines all the environment variables required to configure the SMS reminder system using AWS EventBridge Scheduler + Lambda + Twilio.

## Required Environment Variables

### AWS Configuration

These variables configure the AWS services for reminder scheduling:

```bash
# AWS EventBridge Scheduler Configuration
SMS_REMINDER_LAMBDA_ARN=arn:aws:lambda:us-east-1:YOUR_ACCOUNT_ID:function:sms-reminder-handler
SMS_REMINDER_EXECUTION_ROLE_ARN=arn:aws:iam::YOUR_ACCOUNT_ID:role/eventbridge-scheduler-execution-role
SMS_REMINDER_SCHEDULE_GROUP=sms-reminders

# AWS Credentials (if not using IAM roles)
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_DEFAULT_REGION=us-east-1
```

### Twilio Configuration

These variables are used by the Lambda function to send SMS messages:

```bash
# Twilio API Credentials
TWILIO_ACCOUNT_SID=**********************************
TWILIO_AUTH_TOKEN=eb227323ff58ff56eddb2ee41548754d
TWILIO_PHONE_NUMBER=+***********
```

## Environment Setup Instructions

### 1. Vapor Application Environment

Add these variables to your Vapor application's environment configuration:

**For Development (.env.development):**
```bash
# AWS EventBridge Scheduler
SMS_REMINDER_LAMBDA_ARN=arn:aws:lambda:us-east-1:************:function:sms-reminder-handler-dev
SMS_REMINDER_EXECUTION_ROLE_ARN=arn:aws:iam::************:role/eventbridge-scheduler-execution-role-dev
SMS_REMINDER_SCHEDULE_GROUP=sms-reminders-dev

# AWS Credentials
AWS_ACCESS_KEY_ID=your_dev_access_key
AWS_SECRET_ACCESS_KEY=your_dev_secret_key
AWS_DEFAULT_REGION=us-east-1

# Twilio (Development/Sandbox)
TWILIO_ACCOUNT_SID=**********************************
TWILIO_AUTH_TOKEN=eb227323ff58ff56eddb2ee41548754d
TWILIO_PHONE_NUMBER=+***********
```

**For Production (.env.production):**
```bash
# AWS EventBridge Scheduler
SMS_REMINDER_LAMBDA_ARN=arn:aws:lambda:us-east-1:************:function:sms-reminder-handler-prod
SMS_REMINDER_EXECUTION_ROLE_ARN=arn:aws:iam::************:role/eventbridge-scheduler-execution-role-prod
SMS_REMINDER_SCHEDULE_GROUP=sms-reminders-prod

# AWS Credentials (preferably use IAM roles in production)
AWS_ACCESS_KEY_ID=your_prod_access_key
AWS_SECRET_ACCESS_KEY=your_prod_secret_key
AWS_DEFAULT_REGION=us-east-1

# Twilio (Production)
TWILIO_ACCOUNT_SID=**********************************
TWILIO_AUTH_TOKEN=eb227323ff58ff56eddb2ee41548754d
TWILIO_PHONE_NUMBER=+***********
```

### 2. Lambda Function Environment

Configure these variables in the AWS Lambda function:

```bash
TWILIO_ACCOUNT_SID=**********************************
TWILIO_AUTH_TOKEN=eb227323ff58ff56eddb2ee41548754d
TWILIO_PHONE_NUMBER=+***********
```

## AWS Resource Setup

### 1. Create IAM Roles

**EventBridge Scheduler Execution Role:**
```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "lambda:InvokeFunction"
      ],
      "Resource": "arn:aws:lambda:us-east-1:YOUR_ACCOUNT_ID:function:sms-reminder-handler*"
    }
  ]
}
```

**Lambda Execution Role:**
```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "logs:CreateLogGroup",
        "logs:CreateLogStream",
        "logs:PutLogEvents"
      ],
      "Resource": "arn:aws:logs:*:*:*"
    },
    {
      "Effect": "Allow",
      "Action": [
        "cloudwatch:PutMetricData"
      ],
      "Resource": "*"
    }
  ]
}
```

**Vapor Application IAM Policy:**
```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "scheduler:CreateSchedule",
        "scheduler:DeleteSchedule",
        "scheduler:GetSchedule",
        "scheduler:UpdateSchedule"
      ],
      "Resource": "arn:aws:scheduler:us-east-1:YOUR_ACCOUNT_ID:schedule/sms-reminders/*"
    },
    {
      "Effect": "Allow",
      "Action": [
        "scheduler:CreateScheduleGroup",
        "scheduler:GetScheduleGroup"
      ],
      "Resource": "arn:aws:scheduler:us-east-1:YOUR_ACCOUNT_ID:schedule-group/sms-reminders*"
    },
    {
      "Effect": "Allow",
      "Action": [
        "iam:PassRole"
      ],
      "Resource": "arn:aws:iam::YOUR_ACCOUNT_ID:role/eventbridge-scheduler-execution-role*"
    }
  ]
}
```

### 2. Create EventBridge Scheduler Group

```bash
aws scheduler create-schedule-group \
  --name sms-reminders \
  --description "Schedule group for SMS reminders"
```

### 3. Deploy Lambda Function

Follow the instructions in `lambda/sms-reminder-handler/README.md` to deploy the Lambda function.

## Configuration Validation

### 1. Test AWS Connectivity

Add this method to test AWS configuration in your Vapor application:

```swift
// Add to configure.swift or a test route
func testAWSConfiguration(req: Request) -> EventLoopFuture<String> {
    let scheduler = req.application.aws.client.scheduler
    
    return scheduler.listScheduleGroups(Scheduler.ListScheduleGroupsInput())
        .map { response in
            return "AWS EventBridge Scheduler connected successfully. Groups: \(response.scheduleGroups?.count ?? 0)"
        }
        .flatMapError { error in
            return req.eventLoop.makeFailedFuture(Abort(.internalServerError, reason: "AWS connection failed: \(error)"))
        }
}
```

### 2. Test SMS Reminder Scheduling

Add this test route to verify the complete flow:

```swift
func testSMSReminder(req: Request) -> EventLoopFuture<String> {
    let testDate = Date().addingTimeInterval(300) // 5 minutes from now
    
    return req.smsReminderScheduler.scheduleTaskReminder(
        req: req,
        taskId: UUID(),
        phoneNumber: "+***********", // Use a test number
        dueDate: testDate,
        memberName: "Test User",
        taskTitle: "Test Task"
    ).map {
        return "Test SMS reminder scheduled successfully"
    }
}
```

## Security Best Practices

1. **Use IAM Roles**: In production, use IAM roles instead of access keys
2. **Rotate Credentials**: Regularly rotate Twilio auth tokens
3. **Environment Separation**: Use different credentials for dev/staging/prod
4. **Least Privilege**: Grant minimal required permissions
5. **Monitoring**: Enable CloudTrail and CloudWatch logging

## Troubleshooting

### Common Configuration Issues

1. **"Access Denied" errors**
   - Check IAM permissions
   - Verify role ARNs are correct
   - Ensure PassRole permission is granted

2. **"Schedule group not found"**
   - Create the schedule group using AWS CLI
   - Verify the group name matches the environment variable

3. **"Lambda function not found"**
   - Deploy the Lambda function
   - Verify the function ARN is correct
   - Check the AWS region

4. **SMS not sending**
   - Verify Twilio credentials
   - Check Twilio account balance
   - Ensure phone number is verified

### Debug Commands

```bash
# Test AWS CLI access
aws sts get-caller-identity

# List schedule groups
aws scheduler list-schedule-groups

# List Lambda functions
aws lambda list-functions --query 'Functions[?contains(FunctionName, `sms-reminder`)]'

# Test Lambda function
aws lambda invoke \
  --function-name sms-reminder-handler \
  --payload '{"phoneNumber":"+***********","message":"Test message","reminderType":"test"}' \
  response.json
```

## Monitoring and Alerts

Set up CloudWatch alarms for:

1. **Lambda Errors**: Alert on function failures
2. **Lambda Duration**: Alert on slow executions
3. **EventBridge Failures**: Alert on scheduling failures
4. **Twilio API Errors**: Monitor SMS delivery failures

Example CloudWatch alarm for Lambda errors:

```bash
aws cloudwatch put-metric-alarm \
  --alarm-name "SMS-Reminder-Lambda-Errors" \
  --alarm-description "Alert on SMS reminder Lambda errors" \
  --metric-name Errors \
  --namespace AWS/Lambda \
  --statistic Sum \
  --period 300 \
  --threshold 1 \
  --comparison-operator GreaterThanOrEqualToThreshold \
  --dimensions Name=FunctionName,Value=sms-reminder-handler \
  --evaluation-periods 1
```
