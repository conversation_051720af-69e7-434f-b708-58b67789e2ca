# Networks by Organization API

## Overview

This document describes the new API endpoint for querying networks by organization ID with pagination and search capabilities.

## Endpoint

**GET** `/networks/organization/:orgID`

Query networks belonging to a specific organization with support for pagination, search, and filtering.

## Path Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `orgID` | UUID | Yes | The organization ID to query networks for |

## Query Parameters

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `name` | String | No | - | Search networks by name (case-insensitive partial match) |
| `search` | String | No | - | Alternative search parameter for name |
| `type` | String | No | - | Filter by network type |
| `carrier` | String | No | - | Filter by carrier name |
| `city` | String | No | - | Filter by address city |
| `state` | String | No | - | Filter by address state |
| `zip` | String | No | - | Filter by address zip code |
| `page` | Integer | No | 1 | Page number for pagination |
| `per` | Integer | No | 20 | Items per page (max determined by server config) |

## Response Format

The API returns a paginated response with the following structure:

```json
{
  "items": [
    {
      "id": "550e8400-e29b-41d4-a716-************",
      "name": "General Hospital Network",
      "types": ["medical", "emergency"],
      "status": "active",
      "contact": "<EMAIL>",
      "website": "https://hospital.com",
      "memberBook": true,
      "schedulerId": "scheduler_123",
      "services": [
        {
          "id": "service-uuid",
          "name": "Emergency Care",
          "type": "emergency",
          "status": "active"
        }
      ],
      "address": [
        {
          "id": "address-uuid",
          "street": "123 Main St",
          "city": "Anytown",
          "state": "CA",
          "zip": "12345"
        }
      ],
      "phones": [
        {
          "id": "phone-uuid",
          "label": "main",
          "number": "(*************"
        }
      ],
      "carriers": [
        {
          "id": "carrier-uuid",
          "name": "Blue Cross Blue Shield"
        }
      ],
      "org": {
        "id": "550e8400-e29b-41d4-a716-************",
        "title": "Healthcare Organization",
        "type": "hospital"
      },
      "createdAt": "2023-01-15T12:00:00Z",
      "updatedAt": "2023-01-16T09:30:00Z"
    }
  ],
  "metadata": {
    "page": 1,
    "per": 20,
    "total": 45,
    "pageCount": 3
  }
}
```

## Example Requests

### Basic Request
Get all networks for an organization:
```bash
GET /networks/organization/550e8400-e29b-41d4-a716-************
```

### Search by Name
Search for networks containing "hospital" in the name:
```bash
GET /networks/organization/550e8400-e29b-41d4-a716-************?name=hospital
```

### Pagination
Get the second page with 10 items per page:
```bash
GET /networks/organization/550e8400-e29b-41d4-a716-************?page=2&per=10
```

### Combined Filters
Search for medical networks in California:
```bash
GET /networks/organization/550e8400-e29b-41d4-a716-************?type=medical&state=CA
```

### Advanced Search
Search for networks with "clinic" in name, filtered by carrier:
```bash
GET /networks/organization/550e8400-e29b-41d4-a716-************?name=clinic&carrier=aetna
```

## Response Codes

| Code | Description |
|------|-------------|
| 200 | Success - Returns paginated network data |
| 400 | Bad Request - Invalid organization ID format |
| 500 | Internal Server Error - Server error occurred |

## Features

- **Pagination**: Built-in pagination support with configurable page size
- **Search**: Case-insensitive partial matching on network names
- **Filtering**: Filter by type, carrier, and address fields
- **Related Data**: Automatically includes services, addresses, phones, carriers, and organization data
- **Sorting**: Results are sorted alphabetically by network name
- **Validation**: Organization ID format validation

## Implementation Notes

- The API uses Vapor's built-in pagination system
- Search is case-insensitive and supports partial matches
- Address filtering requires joining with the Address table
- Carrier filtering requires joining with the Carrier table through the NetworkCarriers pivot table
- All related data is eagerly loaded to minimize database queries
- Results are automatically sorted by network name in ascending order

## Comparison with Existing Endpoints

This new endpoint provides several advantages over the existing `/networks/page` endpoint:

1. **Cleaner URL structure**: Organization ID is part of the path rather than a query parameter
2. **Enhanced validation**: Explicit organization ID validation
3. **Optimized queries**: Dedicated query builder for organization-specific searches
4. **Better organization**: Separates organization-specific logic from general network queries
5. **Consistent sorting**: Always sorts by name for predictable results
