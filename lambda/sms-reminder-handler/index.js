/**
 * AWS Lambda Function for SMS Reminder Delivery
 * 
 * This function is triggered by AWS EventBridge Scheduler to send SMS reminders
 * via Twilio API for healthcare appointments, tasks, goals, and interventions.
 * 
 * Environment Variables Required:
 * - TWILIO_ACCOUNT_SID: **********************************
 * - TWILIO_AUTH_TOKEN: eb227323ff58ff56eddb2ee41548754d
 * - TWILIO_PHONE_NUMBER: +***********
 */

const https = require('https');
const querystring = require('querystring');

// Twilio configuration from environment variables
const TWILIO_ACCOUNT_SID = process.env.TWILIO_ACCOUNT_SID || '**********************************';
const TWILIO_AUTH_TOKEN = process.env.TWILIO_AUTH_TOKEN || 'eb227323ff58ff56eddb2ee41548754d';
const TWILIO_PHONE_NUMBER = process.env.TWILIO_PHONE_NUMBER || '+***********';

/**
 * Main Lambda handler function
 * @param {Object} event - EventBridge Scheduler event containing SMS reminder payload
 * @param {Object} context - Lambda context object
 * @returns {Object} Response object with status and message
 */
exports.handler = async (event, context) => {
    console.log('SMS Reminder Lambda triggered:', JSON.stringify(event, null, 2));
    
    try {
        // Parse the SMS reminder payload from the event
        const payload = typeof event === 'string' ? JSON.parse(event) : event;
        
        // Validate required fields
        if (!payload.phoneNumber || !payload.message) {
            throw new Error('Missing required fields: phoneNumber and message');
        }
        
        // Validate phone number format
        if (!isValidPhoneNumber(payload.phoneNumber)) {
            throw new Error(`Invalid phone number format: ${payload.phoneNumber}`);
        }
        
        // Send SMS via Twilio
        const result = await sendSMS(payload.phoneNumber, payload.message);
        
        // Log successful delivery
        console.log('SMS sent successfully:', {
            to: payload.phoneNumber,
            reminderType: payload.reminderType,
            messageId: result.sid,
            status: result.status
        });
        
        return {
            statusCode: 200,
            body: JSON.stringify({
                success: true,
                messageId: result.sid,
                status: result.status,
                reminderType: payload.reminderType
            })
        };
        
    } catch (error) {
        console.error('Error sending SMS reminder:', error);
        
        return {
            statusCode: 500,
            body: JSON.stringify({
                success: false,
                error: error.message,
                event: event
            })
        };
    }
};

/**
 * Send SMS message via Twilio API
 * @param {string} to - Recipient phone number
 * @param {string} message - SMS message content
 * @returns {Promise<Object>} Twilio API response
 */
function sendSMS(to, message) {
    return new Promise((resolve, reject) => {
        // Prepare Twilio API request data
        const postData = querystring.stringify({
            To: to,
            From: TWILIO_PHONE_NUMBER,
            Body: message
        });
        
        // Twilio API endpoint
        const options = {
            hostname: 'api.twilio.com',
            port: 443,
            path: `/2010-04-01/Accounts/${TWILIO_ACCOUNT_SID}/Messages.json`,
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'Content-Length': Buffer.byteLength(postData),
                'Authorization': 'Basic ' + Buffer.from(`${TWILIO_ACCOUNT_SID}:${TWILIO_AUTH_TOKEN}`).toString('base64')
            }
        };
        
        const req = https.request(options, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                try {
                    const response = JSON.parse(data);
                    
                    if (res.statusCode >= 200 && res.statusCode < 300) {
                        resolve(response);
                    } else {
                        reject(new Error(`Twilio API error: ${response.message || data}`));
                    }
                } catch (parseError) {
                    reject(new Error(`Failed to parse Twilio response: ${data}`));
                }
            });
        });
        
        req.on('error', (error) => {
            reject(new Error(`HTTP request error: ${error.message}`));
        });
        
        // Set timeout for the request
        req.setTimeout(30000, () => {
            req.destroy();
            reject(new Error('Request timeout'));
        });
        
        req.write(postData);
        req.end();
    });
}

/**
 * Validate phone number format
 * @param {string} phoneNumber - Phone number to validate
 * @returns {boolean} True if valid, false otherwise
 */
function isValidPhoneNumber(phoneNumber) {
    // Basic validation for international phone numbers
    // Should start with + and contain 10-15 digits
    const phoneRegex = /^\+[1-9]\d{9,14}$/;
    return phoneRegex.test(phoneNumber);
}

/**
 * Format phone number to E.164 format if needed
 * @param {string} phoneNumber - Phone number to format
 * @returns {string} Formatted phone number
 */
function formatPhoneNumber(phoneNumber) {
    // Remove all non-digit characters except +
    let cleaned = phoneNumber.replace(/[^\d+]/g, '');
    
    // If it doesn't start with +, assume US number and add +1
    if (!cleaned.startsWith('+')) {
        if (cleaned.length === 10) {
            cleaned = '+1' + cleaned;
        } else if (cleaned.length === 11 && cleaned.startsWith('1')) {
            cleaned = '+' + cleaned;
        }
    }
    
    return cleaned;
}

/**
 * Extract entity information from payload for logging
 * @param {Object} payload - SMS reminder payload
 * @returns {Object} Entity information
 */
function extractEntityInfo(payload) {
    const entityInfo = {
        type: payload.reminderType,
        id: null
    };
    
    switch (payload.reminderType) {
        case 'appointment_24h':
        case 'appointment_48h':
            entityInfo.id = payload.appointmentId;
            break;
        case 'task_due':
            entityInfo.id = payload.taskId;
            break;
        case 'goal_target':
            entityInfo.id = payload.goalId;
            break;
        case 'intervention_due':
            entityInfo.id = payload.interventionId;
            break;
        case 'follow_up':
        case 'program_task':
            entityInfo.id = payload.entityId;
            break;
    }
    
    return entityInfo;
}

/**
 * Create CloudWatch custom metrics for monitoring
 * @param {string} metricName - Name of the metric
 * @param {number} value - Metric value
 * @param {string} unit - Metric unit
 */
function putMetric(metricName, value = 1, unit = 'Count') {
    // Note: In a production environment, you would use AWS SDK to put custom metrics
    // For now, we'll just log the metrics
    console.log(`METRIC: ${metricName} = ${value} ${unit}`);
}
