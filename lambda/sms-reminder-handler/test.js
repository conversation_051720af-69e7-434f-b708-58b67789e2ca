/**
 * Test file for SMS Reminder Lambda Function
 * 
 * This file contains test cases for the Lambda function to ensure
 * it handles different types of SMS reminder payloads correctly.
 */

const { handler } = require('./index');

// Test payloads for different reminder types
const testPayloads = {
    appointment24h: {
        phoneNumber: "+***********",
        message: "Hi <PERSON>! Reminder: You have an appointment tomorrow - Annual Checkup on Jul 26, 2025 at 2:00 PM. Please contact us if you need to reschedule.",
        appointmentId: "123e4567-e89b-12d3-a456-426614174000",
        reminderType: "appointment_24h"
    },
    
    appointment48h: {
        phoneNumber: "+***********",
        message: "Hi <PERSON>! Reminder: You have an appointment in 2 days - Annual Checkup on Jul 27, 2025 at 2:00 PM. Please contact us if you need to reschedule.",
        appointmentId: "123e4567-e89b-12d3-a456-426614174000",
        reminderType: "appointment_48h"
    },
    
    taskDue: {
        phoneNumber: "+***********",
        message: "Hi <PERSON>! Reminder: Your task 'Complete Health Assessment' is due tomorrow (Jul 26, 2025 at 11:59 PM). Please complete it when you have a chance.",
        taskId: "456e7890-e89b-12d3-a456-426614174001",
        reminderType: "task_due"
    },
    
    goalTarget: {
        phoneNumber: "+***********",
        message: "Hi John! Reminder: Your goal 'Lose 10 pounds' target date is tomorrow (Jul 26, 2025). You're doing great - keep it up!",
        goalId: "789e0123-e89b-12d3-a456-426614174002",
        reminderType: "goal_target"
    },
    
    interventionDue: {
        phoneNumber: "+***********",
        message: "Hi John! Reminder: Your intervention 'Blood pressure monitoring' is due tomorrow (Jul 26, 2025). Please follow up with your care team if needed.",
        interventionId: "012e3456-e89b-12d3-a456-426614174003",
        reminderType: "intervention_due"
    },
    
    followUp: {
        phoneNumber: "+***********",
        message: "Hi John! Reminder: You have a care plan follow-up appointment tomorrow (Jul 26, 2025 at 10:00 AM). Please contact us if you need to reschedule.",
        reminderType: "follow_up"
    },
    
    programTask: {
        phoneNumber: "+***********",
        message: "Hi John! Reminder: Your program task 'Weekly Exercise Log' is due tomorrow (Jul 26, 2025 at 11:59 PM). Please complete it as part of your care program.",
        reminderType: "program_task"
    }
};

// Test cases for error scenarios
const errorTestCases = {
    missingPhoneNumber: {
        message: "Test message",
        reminderType: "test"
    },
    
    missingMessage: {
        phoneNumber: "+***********",
        reminderType: "test"
    },
    
    invalidPhoneNumber: {
        phoneNumber: "invalid-phone",
        message: "Test message",
        reminderType: "test"
    }
};

/**
 * Run all test cases
 */
async function runTests() {
    console.log('🧪 Starting SMS Reminder Lambda Tests...\n');
    
    // Test successful cases
    console.log('✅ Testing successful reminder types:');
    for (const [type, payload] of Object.entries(testPayloads)) {
        await testPayload(type, payload, true);
    }
    
    console.log('\n❌ Testing error cases:');
    for (const [type, payload] of Object.entries(errorTestCases)) {
        await testPayload(type, payload, false);
    }
    
    console.log('\n🎉 All tests completed!');
}

/**
 * Test a single payload
 * @param {string} testName - Name of the test
 * @param {Object} payload - Test payload
 * @param {boolean} shouldSucceed - Whether the test should succeed
 */
async function testPayload(testName, payload, shouldSucceed = true) {
    try {
        console.log(`\n📋 Testing: ${testName}`);
        console.log(`   Payload: ${JSON.stringify(payload, null, 2)}`);
        
        const result = await handler(payload, {});
        
        if (shouldSucceed) {
            if (result.statusCode === 200) {
                console.log(`   ✅ SUCCESS: ${result.body}`);
            } else {
                console.log(`   ❌ UNEXPECTED FAILURE: ${result.body}`);
            }
        } else {
            if (result.statusCode === 500) {
                console.log(`   ✅ EXPECTED FAILURE: ${result.body}`);
            } else {
                console.log(`   ❌ UNEXPECTED SUCCESS: ${result.body}`);
            }
        }
    } catch (error) {
        if (shouldSucceed) {
            console.log(`   ❌ UNEXPECTED ERROR: ${error.message}`);
        } else {
            console.log(`   ✅ EXPECTED ERROR: ${error.message}`);
        }
    }
}

/**
 * Test phone number validation
 */
function testPhoneValidation() {
    console.log('\n📞 Testing phone number validation:');
    
    const validNumbers = [
        '+***********',
        '+447911123456',
        '+33123456789',
        '+12345678901'
    ];
    
    const invalidNumbers = [
        '5551234567',
        '+1555123456',  // too short
        '+***********8901234',  // too long
        'invalid',
        '+',
        ''
    ];
    
    console.log('   Valid numbers:');
    validNumbers.forEach(num => {
        console.log(`     ${num}: Should be valid`);
    });
    
    console.log('   Invalid numbers:');
    invalidNumbers.forEach(num => {
        console.log(`     "${num}": Should be invalid`);
    });
}

// Run tests if this file is executed directly
if (require.main === module) {
    // Set test environment variables
    process.env.TWILIO_ACCOUNT_SID = 'test_account_sid';
    process.env.TWILIO_AUTH_TOKEN = 'test_auth_token';
    process.env.TWILIO_PHONE_NUMBER = '+***********';
    
    console.log('⚠️  NOTE: This is a test run with mock Twilio credentials.');
    console.log('   No actual SMS messages will be sent.\n');
    
    testPhoneValidation();
    runTests().catch(console.error);
}

module.exports = {
    testPayloads,
    errorTestCases,
    runTests,
    testPayload
};
