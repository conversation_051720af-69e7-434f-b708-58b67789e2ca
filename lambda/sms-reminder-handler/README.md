# SMS Reminder Lambda Function

This AWS Lambda function handles SMS reminder delivery for the HMBL healthcare platform. It's triggered by AWS EventBridge Scheduler to send SMS messages via Twilio API for appointments, tasks, goals, interventions, and other healthcare-related reminders.

## Overview

The function receives SMS reminder payloads from EventBridge Scheduler and sends them via <PERSON><PERSON><PERSON>'s SMS API using the configured credentials.

## Environment Variables

The following environment variables must be configured in the Lambda function:

| Variable | Value | Description |
|----------|-------|-------------|
| `TWILIO_ACCOUNT_SID` | `**********************************` | Twilio Account SID |
| `TWILIO_AUTH_TOKEN` | `eb227323ff58ff56eddb2ee41548754d` | Twilio <PERSON> |
| `TWILIO_PHONE_NUMBER` | `+***********` | Twilio phone number for sending SMS |

## Payload Format

The function expects the following JSON payload structure:

```json
{
  "phoneNumber": "+***********",
  "message": "Reminder: You have an appointment in 24 hours.",
  "appointmentId": "123e4567-e89b-12d3-a456-************",
  "taskId": "456e7890-e89b-12d3-a456-************",
  "goalId": "789e0123-e89b-12d3-a456-************",
  "interventionId": "012e3456-e89b-12d3-a456-************",
  "reminderType": "appointment_24h"
}
```

### Reminder Types

- `appointment_24h` - 24-hour appointment reminder
- `appointment_48h` - 48-hour appointment reminder
- `task_due` - Task due date reminder
- `goal_target` - Goal target date reminder
- `intervention_due` - Intervention due date reminder
- `follow_up` - Follow-up appointment reminder
- `program_task` - Program task due date reminder

## Deployment

### Prerequisites

1. AWS CLI configured with appropriate permissions
2. Node.js 18+ installed
3. Access to AWS Lambda and EventBridge services

### Steps

1. **Package the function:**
   ```bash
   cd lambda/sms-reminder-handler
   npm run package
   ```

2. **Create the Lambda function (first time):**
   ```bash
   aws lambda create-function \
     --function-name sms-reminder-handler \
     --runtime nodejs18.x \
     --role arn:aws:iam::YOUR_ACCOUNT:role/lambda-execution-role \
     --handler index.handler \
     --zip-file fileb://sms-reminder-handler.zip \
     --timeout 30 \
     --memory-size 128
   ```

3. **Update existing function:**
   ```bash
   npm run deploy
   ```

4. **Set environment variables:**
   ```bash
   aws lambda update-function-configuration \
     --function-name sms-reminder-handler \
     --environment Variables='{
       "TWILIO_ACCOUNT_SID":"**********************************",
       "TWILIO_AUTH_TOKEN":"eb227323ff58ff56eddb2ee41548754d",
       "TWILIO_PHONE_NUMBER":"+***********"
     }'
   ```

### IAM Role Requirements

The Lambda execution role needs the following permissions:

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "logs:CreateLogGroup",
        "logs:CreateLogStream",
        "logs:PutLogEvents"
      ],
      "Resource": "arn:aws:logs:*:*:*"
    },
    {
      "Effect": "Allow",
      "Action": [
        "cloudwatch:PutMetricData"
      ],
      "Resource": "*"
    }
  ]
}
```

## EventBridge Scheduler Integration

The Lambda function ARN should be configured in the Vapor application's environment variables:

```bash
SMS_REMINDER_LAMBDA_ARN=arn:aws:lambda:us-east-1:YOUR_ACCOUNT:function:sms-reminder-handler
SMS_REMINDER_EXECUTION_ROLE_ARN=arn:aws:iam::YOUR_ACCOUNT:role/eventbridge-scheduler-role
SMS_REMINDER_SCHEDULE_GROUP=sms-reminders
```

## Testing

Run the test suite to verify the function works correctly:

```bash
node test.js
```

This will test various reminder types and error scenarios without sending actual SMS messages.

## Monitoring

The function logs all SMS delivery attempts and results to CloudWatch Logs. Monitor the following metrics:

- **Invocations**: Number of times the function is called
- **Errors**: Failed SMS delivery attempts
- **Duration**: Function execution time
- **Throttles**: Rate limiting events

## Error Handling

The function handles the following error scenarios:

1. **Missing required fields**: Returns 500 error
2. **Invalid phone number format**: Returns 500 error
3. **Twilio API errors**: Returns 500 error with details
4. **Network timeouts**: Returns 500 error after 30 seconds

All errors are logged to CloudWatch for debugging and monitoring.

## Security Considerations

1. **Environment Variables**: Twilio credentials are stored as environment variables, not in code
2. **Phone Number Validation**: Input validation prevents SMS abuse
3. **Rate Limiting**: Twilio provides built-in rate limiting
4. **Logging**: Sensitive information is not logged

## Troubleshooting

### Common Issues

1. **"Invalid phone number format"**
   - Ensure phone numbers are in E.164 format (+**********)
   - Check that the number includes country code

2. **"Twilio API error"**
   - Verify Twilio credentials are correct
   - Check Twilio account balance and status
   - Ensure the from phone number is verified in Twilio

3. **"Request timeout"**
   - Check network connectivity
   - Verify Twilio service status

### Debug Mode

To enable verbose logging, add this environment variable:
```bash
DEBUG=true
```

This will log additional details about the SMS sending process.
