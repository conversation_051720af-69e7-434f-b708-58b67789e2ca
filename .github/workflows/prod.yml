name:  Build-Deploy-To-Prod
on:
  workflow_dispatch:
env:
  duplo_host: https://duplo.duploapps.dona.health 
  duplo_token: "${{ secrets.DUPLO_TOKEN }}"
  SERVICE_NAME: donahealth
  TENANT_NAME: prod

# A workflow run is made up of one or more jobs that can run sequentially or in parallel
jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      # Set up for docker build
      - name: Get AWS credentials
        uses: duplocloud/ghactions-aws-jit@master
        with:
          tenant: default
      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1
      - name: Docker Build and Push
        uses: docker/build-push-action@v2
        with:
          context: .
          push: true
          file: ./Dockerfile
          tags: |
            ${{ steps.login-ecr.outputs.registry }}/${{ env.SERVICE_NAME }}:${{ github.sha }}
    # This part is important - it will be used by the deploy job
    outputs:
      image: "${{ steps.login-ecr.outputs.registry }}/${{ env.SERVICE_NAME }}:${{ github.sha }}"
  deploy:
    runs-on: ubuntu-latest
    needs:
      - build
    steps:
      # Update the backend service to use the new image.
      - name: Deploy
        uses: duplocloud/ghactions-service-update@master
        with:
          tenant: "${{ env.TENANT_NAME }}"
          services: |-
            [
              { "Name": "${{ env.SERVICE_NAME }}", "Image": "${{ needs.build.outputs.image }}" }
            ]
