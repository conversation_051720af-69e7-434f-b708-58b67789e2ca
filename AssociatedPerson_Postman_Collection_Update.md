# AssociatedPerson Postman Collection Update

## Overview
Updated the HMBL Core API Postman collection to include comprehensive endpoints for managing Associated Persons (emergency contacts, caregivers, legal guardians, etc.) for healthcare members.

## Collection Updates

### Version Update
- **Previous Version**: 5.0.0
- **New Version**: 6.0.0
- **Updated Description**: Added "Associated Persons" to the collection description

### New Variables Added
```json
{
  "key": "associatedPersonID",
  "value": "123e4567-e89b-12d3-a456-426614174007",
  "type": "string"
}
```

### New Endpoint Section: "Associated Persons"

#### 1. Get Member Associated Persons
- **Method**: GET
- **URL**: `{{baseUrl}}/api/members/{{memberID}}/associated-persons`
- **Description**: Retrieve all associated persons for a specific member
- **Headers**: Authorization Bearer token

#### 2. Get Specific Associated Person
- **Method**: GET
- **URL**: `{{baseUrl}}/api/members/{{memberID}}/associated-persons/{{associatedPersonID}}`
- **Description**: Retrieve a specific associated person by ID
- **Headers**: Authorization Bearer token

#### 3. Create Associated Person
- **Method**: POST
- **URL**: `{{baseUrl}}/api/members/{{memberID}}/associated-persons`
- **Headers**: Content-Type: application/json, Authorization Bearer token
- **Body Example**:
```json
{
  "fullName": "Jane Smith",
  "relationship": "Mother",
  "role": "emergency_contact",
  "phone": "+1-************",
  "email": "<EMAIL>"
}
```

#### 4. Update Associated Person
- **Method**: PUT
- **URL**: `{{baseUrl}}/api/members/{{memberID}}/associated-persons/{{associatedPersonID}}`
- **Headers**: Content-Type: application/json, Authorization Bearer token
- **Body Example**:
```json
{
  "fullName": "Jane Smith-Johnson",
  "relationship": "Mother",
  "role": "emergency_contact",
  "phone": "+1-************",
  "email": "<EMAIL>"
}
```

#### 5. Delete Associated Person
- **Method**: DELETE
- **URL**: `{{baseUrl}}/api/members/{{memberID}}/associated-persons/{{associatedPersonID}}`
- **Headers**: Authorization Bearer token

### Additional Example Requests

#### Create Caregiver Example
- **Role**: `formal_caregiver`
- **Example**: Home Health Aide with professional contact information

#### Create Legal Guardian Example
- **Role**: `legal_guardian`
- **Example**: Legal guardian with legal services contact information

#### Create Healthcare Provider Example
- **Role**: `healthcare_provider`
- **Example**: Primary Care Physician with medical practice contact information

## Available Role Types
The AssociatedPersonRole enum supports the following values:
- `emergency_contact`
- `informal_caregiver`
- `formal_caregiver`
- `legal_guardian`
- `navigator`
- `family_member`
- `friend`
- `healthcare_provider`
- `social_worker`
- `other`

## Authentication
All endpoints require Bearer token authentication using the `{{authToken}}` variable.

## Testing Notes
1. Update the `{{memberID}}` variable with a valid member UUID before testing
2. Update the `{{associatedPersonID}}` variable when testing specific person operations
3. Ensure your `{{authToken}}` is valid and has appropriate permissions
4. Phone numbers should include country code format (e.g., "+1-************")
5. Email addresses are validated for proper format

## Timeline Integration
All CRUD operations automatically create timeline entries with:
- Operation tracking (Created/Updated/Deleted)
- Associated person details in metadata
- User tracking via authentication token
- Reference ID linking to the associated person

## File Location
Updated collection saved as: `HMBL_Core_API_Collection.json`

## Import Instructions
1. Open Postman
2. Click "Import" button
3. Select the updated `HMBL_Core_API_Collection.json` file
4. The collection will be imported with all new Associated Person endpoints
5. Update environment variables as needed for your testing environment
