# Problem Model Migration Guide

## Overview
This guide explains how to migrate your existing `problems` table to include the new healthcare tracking fields using the `CreateProblemUpdate` migration.

## Migration Structure

### New Migration: `CreateProblemUpdate`
**File**: `Sources/App/Controllers/CarePlans/CarePlansController.swift` (lines 562-581)

This migration adds the following fields to the existing `problems` table:
- `clinical_note` (optional)
- `status` (required, defaults to "active")
- `date_identified` (required, defaults to current date)
- `source` (required, defaults to "care team")
- `confirmed_by` (optional)

## Migration Details

### Fields Being Added:

```swift
.field("clinical_note", .string)
.field("status", .string, .required, .sql(.default("active")))
.field("date_identified", .date, .required, .sql(.default(.literal("CURRENT_DATE"))))
.field("source", .string, .required, .sql(.default("care team")))
.field("confirmed_by", .string)
```

### Default Values:
- **`status`**: `"active"` - All existing problems will be marked as active
- **`date_identified`**: `CURRENT_DATE` - Uses the migration date as identification date
- **`source`**: `"care team"` - Assumes existing problems were identified by care team
- **`clinical_note`**: `NULL` - Optional field, no default needed
- **`confirmed_by`**: `NULL` - Optional field, no default needed

## How to Apply the Migration

### Step 1: Register the Migration
Add the new migration to your application's migration configuration. In your `configure.swift` file:

```swift
// Add this to your migrations
app.migrations.add(CreateProblemUpdate())
```

### Step 2: Run the Migration
Execute the migration using Vapor's migration command:

```bash
# Development
swift run App migrate

# Or if using Docker
docker-compose exec app swift run App migrate

# Production (with confirmation)
swift run App migrate --env production
```

### Step 3: Verify Migration
Check that the new fields were added successfully:

```sql
-- PostgreSQL command to verify table structure
\d problems

-- Or query to see the new fields
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_name = 'problems';
```

## Migration Safety Features

### 🔒 **Safe for Production:**
- **Non-destructive**: Only adds new fields, doesn't modify existing data
- **Default values**: Required fields have sensible defaults
- **Rollback support**: Can be reverted if needed

### 🔄 **Rollback Capability:**
If you need to rollback the migration:

```bash
swift run App migrate --revert
```

This will remove the new fields:
- `clinical_note`
- `status`
- `date_identified`
- `source`
- `confirmed_by`

## Post-Migration Data Updates

After running the migration, you may want to update existing records with more accurate data:

### Example Update Queries:

```sql
-- Update problems with known ICD codes to have better source tracking
UPDATE problems 
SET source = 'EHR import' 
WHERE icd_code IS NOT NULL;

-- Update problems with specific confirmed_by information
UPDATE problems 
SET confirmed_by = 'Dr. Smith, MD' 
WHERE care_plan_id IN (
    SELECT id FROM care_plans WHERE /* your criteria */
);

-- Add clinical notes for specific problems
UPDATE problems 
SET clinical_note = 'Imported from legacy system during migration' 
WHERE created_at < '2025-01-01';
```

## API Impact

### Before Migration:
```json
{
  "description": "Hypertension, uncontrolled",
  "icdCode": "I10"
}
```

### After Migration:
```json
{
  "icdCode": "I10",
  "description": "Essential (primary) hypertension",
  "clinicalNote": "BP remains elevated despite medication adjustment.",
  "status": "active",
  "dateIdentified": "2023-06-01T00:00:00Z",
  "source": "EHR import",
  "confirmedBy": "Dr. Smith, MD"
}
```

## Testing the Migration

### 1. Test in Development First:
```bash
# Create a backup of your dev database
pg_dump your_dev_db > problems_backup.sql

# Run the migration
swift run App migrate

# Test API endpoints
# Use the updated Postman collection to verify functionality
```

### 2. Verify Existing Data:
```sql
-- Check that existing problems have default values
SELECT id, description, status, date_identified, source 
FROM problems 
LIMIT 10;
```

### 3. Test New Problem Creation:
Use the updated Postman collection to create new problems with all fields.

## Production Deployment Checklist

### Pre-Migration:
- [ ] **Backup database**: Create full backup before migration
- [ ] **Test in staging**: Run migration in staging environment first
- [ ] **Update API clients**: Ensure client applications handle new fields
- [ ] **Review default values**: Confirm defaults are appropriate for your use case

### During Migration:
- [ ] **Schedule maintenance window**: Brief downtime may be required
- [ ] **Monitor migration**: Watch for any errors during execution
- [ ] **Verify completion**: Confirm all fields were added successfully

### Post-Migration:
- [ ] **Test API endpoints**: Verify all CRUD operations work
- [ ] **Update documentation**: Share new API structure with team
- [ ] **Monitor performance**: Check for any performance impacts
- [ ] **Update existing data**: Run any necessary data updates

## Troubleshooting

### Common Issues:

#### Migration Fails:
```bash
# Check migration status
swift run App migrate --dry-run

# View migration history
SELECT * FROM fluent_migrations;
```

#### Default Value Issues:
If default values don't work as expected, you can manually update:

```sql
-- Fix any NULL values that shouldn't be NULL
UPDATE problems SET status = 'active' WHERE status IS NULL;
UPDATE problems SET source = 'care team' WHERE source IS NULL;
UPDATE problems SET date_identified = created_at WHERE date_identified IS NULL;
```

#### Performance Concerns:
For large tables, consider:
- Running migration during low-traffic periods
- Adding indexes after migration if needed
- Monitoring query performance

## Migration File Location

The migration is defined in:
```
Sources/App/Controllers/CarePlans/CarePlansController.swift
```

**Lines 562-581**: `struct CreateProblemUpdate: Migration`

## Next Steps

1. **Apply Migration**: Run the migration in your development environment
2. **Test Thoroughly**: Use the updated Postman collection
3. **Update Client Code**: Modify any applications that consume the API
4. **Deploy to Staging**: Test in staging environment
5. **Production Deployment**: Apply to production with proper backup procedures

## Support

If you encounter issues during migration:
1. Check the migration logs for specific error messages
2. Verify database permissions for schema modifications
3. Ensure all dependencies are properly configured
4. Consider rolling back and investigating if critical issues arise

**Remember**: Always backup your database before running migrations in production! 🔒
