# CarePlan and Goal Title Enhancement Summary

## Overview
This enhancement adds `title` fields to both the CarePlan and Goal models, providing better organization and identification of care plans and their associated goals.

## Changes Made

### 1. Model Updates

#### CarePlan Model
- **Added**: `@Field(key: "title") var title: String`
- **Location**: `Sources/App/Controllers/CarePlans/CarePlansController.swift` (line 1001)
- **Purpose**: Provides a descriptive title for each care plan

#### Goal Model  
- **Added**: `@Field(key: "title") var title: String`
- **Location**: `Sources/App/Controllers/CarePlans/CarePlansController.swift` (line 1066)
- **Purpose**: Provides a descriptive title for each goal

### 2. Content Conformance Updates

#### CarePlan Content Conformance
- **Added**: `case title` to CodingKeys enum
- **Location**: `Sources/App/Controllers/CarePlans/CarePlansController.swift` (line 1027)

#### Goal Content Conformance
- **Added**: `case title` to CodingKeys enum  
- **Location**: `Sources/App/Controllers/CarePlans/CarePlansController.swift` (line 1087)

### 3. Request DTO Updates

#### CarePlanCreateRequest
- **Added**: `let title: String`
- **Location**: `Sources/App/Controllers/CarePlans/CarePlansController.swift` (line 1477)

#### GoalCreateRequest
- **Added**: `let title: String`
- **Location**: `Sources/App/Controllers/CarePlans/CarePlansController.swift` (line 1486)

### 4. Controller Method Updates

#### CarePlan Methods
- **createCarePlan**: Added `carePlan.title = input.title` (line 522)
- **updateCarePlan**: Added `plan.title = input.title` (line 572)

#### Goal Methods
- **createGoal**: Added `goal.title = input.title` (line 486)
- **updateGoal**: Added `goal.title = input.title` (line 618)

### 5. Database Migrations

#### AddTitleToCarePlan Migration
- **File**: `Sources/App/Migrations/AddTitleToCarePlan.swift`
- **Purpose**: Adds `title` field to `care_plans` table
- **Strategy**: 3-step process to handle existing data:
  1. Add title field as optional
  2. Update existing records with default title "Care Plan"
  3. Make title field required

#### AddTitleToGoal Migration
- **File**: `Sources/App/Migrations/AddTitleToGoal.swift`
- **Purpose**: Adds `title` field to `goals` table
- **Strategy**: 3-step process to handle existing data:
  1. Add title field as optional
  2. Update existing records with title from first 50 chars of description
  3. Make title field required

#### Migration Registration
- **Location**: `Sources/App/configure.swift` (lines 249-250)
- **Added**: Both migrations to the migration list

### 6. API Documentation Updates

#### Postman Collection Updates
- **File**: `CarePlansController_Postman_Collection.json`
- **Create Care Plan**: Added title example: "Comprehensive Care Plan for Diabetes Management"
- **Update Care Plan**: Added title example: "Updated Comprehensive Care Plan for Diabetes Management"
- **Create Goal**: Added title example: "Medication Adherence Goal"
- **Update Goal**: Added title example: "Enhanced Medication Adherence Goal"

#### README Documentation Updates
- **File**: `CarePlansController_README.md`
- **Updated**: Care Plan model example to include title field
- **Updated**: Goal model example to include title field

## API Usage Examples

### Creating a Care Plan with Title
```json
POST /api/members/{memberID}/careplans
{
  "title": "Comprehensive Care Plan for Diabetes Management",
  "startDate": "2025-01-01T00:00:00Z",
  "lastReviewed": "2025-01-15T00:00:00Z",
  "nextReviewDate": "2025-04-01T00:00:00Z",
  "outcome": "Initial assessment completed",
  "status": "active"
}
```

### Creating a Goal with Title
```json
POST /api/careplans/{carePlanID}/goals
{
  "title": "Medication Adherence Goal",
  "description": "Improve medication adherence",
  "type": "health",
  "targetDate": "2025-06-01T00:00:00Z",
  "status": "active",
  "objective": "Patient will take prescribed medications as directed",
  "measurementCriteria": "90% medication adherence rate",
  "achievabilityNote": "Patient has shown willingness to improve",
  "barriers": "Forgetfulness, complex medication schedule"
}
```

## Migration Instructions

1. **Run Migrations**: Execute `swift run Run migrate` to apply the new database schema changes
2. **Test API**: Use the updated Postman collection to test the new title fields
3. **Verify**: Ensure all existing functionality continues to work with the new required title fields

## Migration Status

✅ **Successfully Applied**: Both migrations have been successfully applied to the database:
- `AddTitleToCarePlan`: Added title field to care_plans table with default values for existing records
- `AddTitleToGoal`: Added title field to goals table with smart defaults from description field
- Server is running and ready for testing

## Benefits

- **Better Organization**: Care plans and goals now have descriptive titles for easier identification
- **Improved UX**: Frontend applications can display meaningful titles instead of just IDs or descriptions
- **Enhanced Searchability**: Titles provide additional searchable content
- **Consistent API**: Both CarePlan and Goal models now follow similar patterns with title fields

## Breaking Changes

⚠️ **Important**: The `title` field is required for both CarePlan and Goal creation/update operations. Existing API clients will need to be updated to include the title field in their requests.

## Files Modified

1. `Sources/App/Controllers/CarePlans/CarePlansController.swift`
2. `Sources/App/Migrations/AddTitleToCarePlan.swift` (new)
3. `Sources/App/Migrations/AddTitleToGoal.swift` (new)
4. `Sources/App/configure.swift`
5. `CarePlansController_Postman_Collection.json`
6. `CarePlansController_README.md`
