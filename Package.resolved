{"pins": [{"identity": "apns", "kind": "remoteSourceControl", "location": "https://github.com/vapor/apns.git", "state": {"revision": "0eff8eee62001cff9ee21853a4357777ddc523b6", "version": "3.0.0"}}, {"identity": "apnswift", "kind": "remoteSourceControl", "location": "https://github.com/swift-server-community/APNSwift.git", "state": {"revision": "f9d50c860386d138b51e991010875ed494b53bad", "version": "4.0.1"}}, {"identity": "async-http-client", "kind": "remoteSourceControl", "location": "https://github.com/swift-server/async-http-client.git", "state": {"revision": "7f05a8da46cc2a4ab43218722298b81ac7a08031", "version": "1.13.2"}}, {"identity": "async-kit", "kind": "remoteSourceControl", "location": "https://github.com/vapor/async-kit.git", "state": {"revision": "e048c8ee94967e8d8a1c2ec0e1156d6f7fa34d31", "version": "1.20.0"}}, {"identity": "console-kit", "kind": "remoteSourceControl", "location": "https://github.com/vapor/console-kit.git", "state": {"revision": "447f1046fb4e9df40973fe426ecb24a6f0e8d3b4", "version": "4.6.0"}}, {"identity": "fluent", "kind": "remoteSourceControl", "location": "https://github.com/vapor/fluent.git", "state": {"revision": "4b4d8bf15a06fd60137e9c543e5503c4b842654e", "version": "4.8.0"}}, {"identity": "fluent-kit", "kind": "remoteSourceControl", "location": "https://github.com/vapor/fluent-kit.git", "state": {"revision": "4f886ebdfc7eb497bd58e622c3eacea6cad893cd", "version": "1.50.3"}}, {"identity": "fluent-postgres-driver", "kind": "remoteSourceControl", "location": "https://github.com/vapor/fluent-postgres-driver.git", "state": {"revision": "fd57101e426d3edf66a32ba63a7d0b8ced4d7499", "version": "2.10.0"}}, {"identity": "jmespath.swift", "kind": "remoteSourceControl", "location": "https://github.com/adam-fowler/jmespath.swift.git", "state": {"revision": "3877a5060e85ae33e3b9fe51ab581784f65ec80e", "version": "1.0.3"}}, {"identity": "jwt", "kind": "remoteSourceControl", "location": "https://github.com/vapor/jwt.git", "state": {"revision": "d65f32bfd08fae5910d603028be5dec4f35b3482", "version": "4.2.2"}}, {"identity": "jwt-kit", "kind": "remoteSourceControl", "location": "https://github.com/vapor/jwt-kit.git", "state": {"revision": "c2595b9ad7f512d7f334830b4df1fed6e917946a", "version": "4.13.4"}}, {"identity": "leaf", "kind": "remoteSourceControl", "location": "https://github.com/vapor/leaf.git", "state": {"revision": "6fe0e843c6599f5189e45c7b08739ebc5c410c3b", "version": "4.2.4"}}, {"identity": "leaf-kit", "kind": "remoteSourceControl", "location": "https://github.com/vapor/leaf-kit.git", "state": {"revision": "c04e54753fb30a985b42c3be8530bb94442da9c6", "version": "1.11.0"}}, {"identity": "multipart-kit", "kind": "remoteSourceControl", "location": "https://github.com/vapor/multipart-kit.git", "state": {"revision": "0d55c35e788451ee27222783c7d363cb88092fab", "version": "4.5.2"}}, {"identity": "postgres-kit", "kind": "remoteSourceControl", "location": "https://github.com/vapor/postgres-kit.git", "state": {"revision": "0b72fa83b1023c4b82072e4049a3db6c29781fff", "version": "2.13.5"}}, {"identity": "postgres-nio", "kind": "remoteSourceControl", "location": "https://github.com/vapor/postgres-nio.git", "state": {"revision": "5d817be55cae8b00003b7458944954558302d006", "version": "1.25.0"}}, {"identity": "queues", "kind": "remoteSourceControl", "location": "https://github.com/vapor/queues.git", "state": {"revision": "f1adaf4c925eb7074a4acf363172c1fa6ec888c8", "version": "1.12.1"}}, {"identity": "queues-redis-driver", "kind": "remoteSourceControl", "location": "https://github.com/vapor/queues-redis-driver.git", "state": {"revision": "5e42bcd7632ba5832e49e69698ba8bea26f40f51", "version": "1.0.4"}}, {"identity": "redis", "kind": "remoteSourceControl", "location": "https://github.com/vapor/redis.git", "state": {"revision": "2a8d3e4639b90b39b74309b54216bdfd9cb52b41", "version": "4.10.0"}}, {"identity": "redistack", "kind": "remoteSourceControl", "location": "https://github.com/swift-server/RediStack.git", "state": {"revision": "43d94452a8be6ebebb07ec7bcf4e6f19888e1251", "version": "1.4.1"}}, {"identity": "routing-kit", "kind": "remoteSourceControl", "location": "https://github.com/vapor/routing-kit.git", "state": {"revision": "ffac7b3a127ce1e85fb232f1a6271164628809ad", "version": "4.6.0"}}, {"identity": "soto", "kind": "remoteSourceControl", "location": "https://github.com/soto-project/soto.git", "state": {"revision": "26bd91a43a3e569956b99b7f15aa2709a1a6ff23", "version": "6.5.0"}}, {"identity": "soto-core", "kind": "remoteSourceControl", "location": "https://github.com/soto-project/soto-core.git", "state": {"revision": "787be995b0cff07bd28e9aea4c8a6424ead36e71", "version": "6.4.2"}}, {"identity": "sql-kit", "kind": "remoteSourceControl", "location": "https://github.com/vapor/sql-kit.git", "state": {"revision": "e0b35ff07601465dd9f3af19a1c23083acaae3bd", "version": "3.32.0"}}, {"identity": "swift-algorithms", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-algorithms.git", "state": {"revision": "87e50f483c54e6efd60e885f7f5aa946cee68023", "version": "1.2.1"}}, {"identity": "swift-async-algorithms", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-async-algorithms.git", "state": {"revision": "4c3ea81f81f0a25d0470188459c6d4bf20cf2f97", "version": "1.0.3"}}, {"identity": "swift-atomics", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-atomics.git", "state": {"revision": "cd142fd2f64be2100422d658e7411e39489da985", "version": "1.2.0"}}, {"identity": "swift-backtrace", "kind": "remoteSourceControl", "location": "https://github.com/swift-server/swift-backtrace.git", "state": {"revision": "f25620d5d05e2f1ba27154b40cafea2b67566956", "version": "1.3.3"}}, {"identity": "swift-collections", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-collections.git", "state": {"revision": "671108c96644956dddcd89dd59c203dcdb36cec7", "version": "1.1.4"}}, {"identity": "swift-crypto", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-crypto.git", "state": {"revision": "75ec60b8b4cc0f085c3ac414f3dca5625fa3588e", "version": "2.2.4"}}, {"identity": "swift-log", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-log.git", "state": {"revision": "96a2f8a0fa41e9e09af4585e2724c4e825410b91", "version": "1.6.2"}}, {"identity": "swift-metrics", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-metrics.git", "state": {"revision": "5e63558d12e0267782019f5dadfcae83a7d06e09", "version": "2.5.1"}}, {"identity": "swift-nio", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-nio.git", "state": {"revision": "c51907a839e63ebf0ba2076bba73dd96436bd1b9", "version": "2.81.0"}}, {"identity": "swift-nio-extras", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-nio-extras.git", "state": {"revision": "91dd2d61fb772e1311bb5f13b59266b579d77e42", "version": "1.15.0"}}, {"identity": "swift-nio-http2", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-nio-http2.git", "state": {"revision": "d6656967f33ed8b368b38e4b198631fc7c484a40", "version": "1.23.1"}}, {"identity": "swift-nio-ssl", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-nio-ssl.git", "state": {"revision": "0cc3528ff48129d64ab9cab0b1cd621634edfc6b", "version": "2.29.3"}}, {"identity": "swift-nio-transport-services", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-nio-transport-services.git", "state": {"revision": "3c394067c08d1225ba8442e9cffb520ded417b64", "version": "1.23.1"}}, {"identity": "swift-numerics", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-numerics", "state": {"revision": "0a5bc04095a675662cf24757cc0640aa2204253b", "version": "1.0.2"}}, {"identity": "swift-service-lifecycle", "kind": "remoteSourceControl", "location": "https://github.com/swift-server/swift-service-lifecycle.git", "state": {"revision": "c2e97cf6f81510f2d6b4a69453861db65d478560", "version": "2.6.3"}}, {"identity": "swift-system", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-system.git", "state": {"revision": "a34201439c74b53f0fd71ef11741af7e7caf01e1", "version": "1.4.2"}}, {"identity": "swiftcsv", "kind": "remoteSourceControl", "location": "https://github.com/swiftcsv/SwiftCSV.git", "state": {"revision": "6ab5d0fe9b6ef3c79d717eefa70862df389e74d9", "version": "0.10.0"}}, {"identity": "vapor", "kind": "remoteSourceControl", "location": "https://github.com/vapor/vapor.git", "state": {"revision": "1baf62327cc55f55a0767c3a9414dec8ebfdff96", "version": "4.77.1"}}, {"identity": "websocket-kit", "kind": "remoteSourceControl", "location": "https://github.com/vapor/websocket-kit.git", "state": {"revision": "2d9d2188a08eef4a869d368daab21b3c08510991", "version": "2.6.1"}}, {"identity": "zipfoundation", "kind": "remoteSourceControl", "location": "https://github.com/weichsel/ZIPFoundation", "state": {"revision": "02b6abe5f6eef7e3cbd5f247c5cc24e246efcfe0", "version": "0.9.19"}}], "version": 2}