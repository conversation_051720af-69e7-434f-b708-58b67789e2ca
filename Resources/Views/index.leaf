<!DOCTYPE html>
<html>
<head>
    <link href="https://fonts.cdnfonts.com/css/graphik" rel="stylesheet">
<script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.7.0/chart.min.js" integrity="sha512-TW5s0IT/IppJtu76UbysrBH9Hy/5X41OTAbQuffZFU6lQ1rdcLHzpU5BzVvr/YFykoiMYZVWlr/PX1mDcfM9Qg==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>

<title>Dona Health Widget</title>
   <style>
   html, body {
      height: 100%;
    }
    
    body {
      font-family: 'Graphik', sans-serif;
       display: flex;
      flex-direction: column;
      align-items: left;
    }
                
    table {
      border-collapse: collapse;
    }

    .score-header {
        font-family: 'Graphik';
        font-style: normal;
        font-weight: 500;
        font-size: 27px;
        line-height: 100%;
        color: #E97100;
        padding-top:0;
    }
    
    .min-header {
        font-family: 'Graphik';
        font-style: normal;
        font-weight: 500;
        font-size: 19px;
        line-height: 100%;
        color: #001018;
    }
    
    .name {
        font-family: 'Graphik';
        font-style: normal;
        font-weight: 500;
        font-size: 27px;
        line-height: 100%;
    }
    
    th, td {
      border-bottom: 1px solid black;
      border-color: #D5DCE2;
      padding-bottom: 15px;
      padding-top: 15px;
      padding-left: 18px;
    }
    
    table {
       padding-top: 0px;
       padding-bottom: 0px;
    }
    
    td {
        font-family: 'Graphik';
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 100%;
        color: #646F79;
    }
    
    .value {
        font-family: 'Graphik';
        font-style: normal;
        font-weight: 500;
        font-size: 14px;
        line-height: 100%;
        color: #001018;
        padding-left: 32px;
    }
    
    .circle-image {
      border-radius: 50%;
      width: 215px;
      height:215px;
    }

    .icon {
      width: 44px;
      height:44px;
    }

    .score-value{
        font-style: normal;
        font-weight: 500;
        font-size: 14px;
        line-height: 65%;
    }

    tr:not(:last-child) {
      border-bottom: 1px solid black;
    }
  </style>

</head>
<body>
#if(layout == "full"):
    #extend("full")
#elseif(layout == "minimal"):
    #extend("minimal")

#else:
    No valid layout provided.
#endif
</body>
</html>
