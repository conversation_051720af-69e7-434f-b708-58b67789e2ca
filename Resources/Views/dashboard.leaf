<!DOCTYPE html>
<html>
<head>
  <title>Dashboard</title>
  <style>
  
   body {
      text-align: center;
      max-width: 1000px;
      margin: 0 auto;
    }
    
    .header {
      position: fixed;
      top: 0;
      right: 0;
      left: 0;
      height: 60px;
      background-color: #ffffff;
      display: flex;
      align-items: center;
      padding: 0 20px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    .header-icon {
      font-size: 24px;
      margin-right: 10px;
    }
    
    .header-title {
        font-size: 20px;
        margin-right: auto; /* Move the title to the left */
        font-family: 'Avenir';
        font-style: normal;
        font-weight: 900;
        line-height: 100%;
        color: #0DAAE4;
    }
    
    .divider {
      height: 1px;
      background-color: #ccc;
      margin-top: 20px; /* Adjust the margin as needed */
      margin-bottom: 40px; /* Adjust the margin as needed */
    }
    
    
    .title {
        font-family: 'Avenir';
        font-style: normal;
        font-weight: 900;
        font-size: 36px;
        line-height: 49px;
        color: #313537;
        margin-bottom: 50px;
        text-align: left;
        margin-top: 70px;
    }
    
    .container-wrapper {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20px;
    }
    
    .number-container {
      text-align: center;
      width: 30%; /* Adjust the width as needed */
    }
    
    .number {
      font-family: 'Avenir';
        font-style: normal;
        font-weight: 900;
        font-size: 48px;
        line-height: 100%;
        letter-spacing: -0.02em;
    }
    
    .key-label {
        font-family: 'Avenir';
        font-style: normal;
        font-weight: 900;
        font-size: 14px;
        line-height: 100%;
        color: #313537;
        padding-bottom:5px;
    }
    
        .chart-container {
      display: flex;
      flex-direction: row;
    }

    .chart {
      width: 300px;
      height: 300px;
      margin-right: 20px;
      overflow: hidden;
      display: flex;
      flex-direction: column;
    }

    .chart-bars {
      display: flex;
      flex-direction: row;
    }

    .chart-bar {
      height: 18px;
      background-color: #0DAAE4;
      margin-bottom: 15px;
    }

    .chart-bar-remaining {
      background-color: #ccc;
    }

    .chart-label {
      text-align: left;
      font-family: 'Avenir';
      font-style: normal;
      font-weight: 900;
      font-size: 12px;
      line-height: 100%;
      color: #313537;
      margin-bottom: 7px;
    }
    
  </style>
</head>
<body>

<div class="header">
    <span class="header-icon">
        <img class="icon" src="/images/report_icon.png" alt="Dona Image">
    </span>
    <span class="header-title">Reporting</span>
  </div>
  
  <div class="divider"></div>

  <h1 class="title">Dashboard</h1>
  
  
  <div class="container-wrapper">
    <div class="number-container">
      <div class="key-label">Total Clients</div>
      <div class="number">#(totalMembers)</div>
    </div>
  
    <div class="number-container">
      <div class="key-label">Total Navigators</div>
      <div class="number">#(totalNavs)</div>
    </div>
  
    <div class="number-container">
      <div class="key-label">Total Care Packages</div>
      <div class="number">#(totalCarePacks)</div>
    </div>
    <div class="number-container">
      <div class="key-label">Total Assesments</div>
      <div class="number">#(totalSurveys)</div>
    </div>
    </div>
    #extend("barchart")
</body>
</html>
