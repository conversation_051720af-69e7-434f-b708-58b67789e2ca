<img class="circle-image" src=#(memberURL) alt="Circular Image">
<p class="name">#(dashboard.member.firstName) #(dashboard.member.lastName)</p>
  <table>
    <tr>
      <td>Type</td>
      <td class="value">#(dashboard.member.type)</td>
    </tr>
    <tr>
      <td>Gender</td>
      <td class="value">#(dashboard.member.gender)</td>
    </tr>
     <tr>
      <td>DOB</td>
      <td class="value">#(dashboard.member.dob)</td>
    </tr>
     <tr>
      <td>Status</td>
      <td class="value">#(dashboard.member.status)</td>
    </tr>
     <tr>
      <td>Ethnicity</td>
      <td class="value">#(dashboard.member.ethnicity)</td>
    </tr>
     <tr>
      <td>Language</td>
      <td class="value">#(dashboard.member.lang)</td>
    </tr>
     <tr>
      <td>Email</td>
      <td class="value">#(dashboard.member.email)</td>
    </tr>
     <tr>
      <td>Phone</td>
      <td class="value">305 - 123 - 4567</td>
    </tr>
  </table>
<p class="min-header">Latest Result</p>
<p class="score-header">Safety Net</p>
<table>
    <tr>
      <td>Date</td>
      <td class="value">Jan 2, 2023</td>
    </tr>
    <tr>
      <td>Naviagtor</td>
      <td class="value">Paul Stevens - Miami Team A</td>
    </tr>
  </table>
  
<canvas id="radarChart"></canvas>
<table>
            <tr>
                <td>
                    <img class="icon" src="/images/income.png" alt="Circular Image">
                </td>
                <td class="value">
                    <p class="score-value">
                        Income
                    </p>
                    <p class="score-value" style="color:#E97100;">
                        Safety Net (1)
                    </p>
                </td>
            </tr>
            <tr>
                <td>
                    <img class="icon" src="/images/nutrition.png" alt="Circular Image">
                </td>
                <td class="value">
                    <p class="score-value">
                        Nutrition
                    </p>
                    <p class="score-value" style="color:#DE6F09;">
                        Urgent Need (1)
                    </p>
                </td>
            </tr>
            <tr>
                <td>
                    <img class="icon" src="/images/house.png" alt="Circular Image">
                </td>
                <td class="value">
                    <p class="score-value">
                        Housing
                    </p>
                    <p class="score-value" style="color:#DE6F09;">
                        Urgent Need (2)
                    </p>
                </td>
            </tr>
            <tr>
                <td>
                    <img class="icon" src="/images/policy.png" alt="Circular Image">
                </td>
                <td class="value">
                    <p class="score-value">
                        Policy Determinants
                    </p>
                    <p class="score-value" style="color:#E97100;">
                        Safety Net (0)
                    </p>
                </td>
            </tr>
            <tr>
                <td>
                    <img class="icon" src="/images/tech.png" alt="Circular Image">
                </td>
                <td class="value">
                    <p class="score-value">
                        Technology
                    </p>
                    <p class="score-value" style="color:#0ABF89;">
                        Stable (0)
                    </p>
                </td>
            </tr>
            <tr>
                <td>
                    <img class="icon" src="/images/trans.png" alt="Circular Image">
                </td>
                <td class="value">
                    <p class="score-value">
                        Transportation
                    </p>
                    <p class="score-value" style="color:#0ABF89;">
                        Stable (0)
                    </p>
                </td>
            </tr>
            <tr>
                <td>
                    <img class="icon" src="/images/employ.png" alt="Circular Image">
                </td>
                <td class="value">
                    <p class="score-value">
                        Employment
                    </p>
                    <p class="score-value" style="color:#DE6F09;">
                        Urgent Need (2)
                    </p>
                </td>
            </tr>
            <tr>
                <td>
                    <img class="icon" src="/images/edu.png" alt="Circular Image">
                </td>
                <td class="value">
                    <p class="score-value">
                        Life Skills & Education
                    </p>
                    <p class="score-value" style="color:#E97100;">
                        Safety Net (1)
                    </p>
                </td>
            </tr>

        </table>

  <script>
    // Get the canvas element
    const canvas = document.getElementById('radarChart');

    // Define chart data
    const data = {
    labels: ['Income', 'Nutrition', 'Housing', 'Policy\nDeterminants', 'Technology', 'Transport.', 'Employ.', 'Life Skills\n& Education'],
      datasets: [{
        label: '',
        data: [2, 1, 1, 2, 3, 3, 1, 2],
        backgroundColor: 'rgba(20, 112, 196, 0.3)',
        borderColor: 'rgba(213, 220, 226, 1)',
        borderWidth: 4
      }]
    };

    
new Chart(canvas, {
    type: 'radar',
    data: data,
    options: {
        plugins: {
            legend: {
                display: false
            }
        },
        responsive: false,
        scales: {
            r: {
                min: 0,
                max: 4,
                ticks: {
                    beginAtZero: true,
                    stepSize: 1
                }
            }
        }
    }
});

  </script>
