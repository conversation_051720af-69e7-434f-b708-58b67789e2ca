
<!DOCTYPE html>
<html>
<head>
  <title>Consent Page</title>
  <style>
    /* CSS styles */
  </style>
</head>
<body>
  <div class="container">
    <h1>Consent Form</h1>
    
    <p>By submitting this form, you hereby consent to the following terms:</p>
    <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Fusce hendrerit, lectus id varius ultrices, dui nulla rutrum velit, vitae varius ligula metus nec enim. Aliquam efficitur mauris vitae ligula faucibus hendrerit. Nullam et enim nec sapien gravida interdum. Integer convallis est ac purus consectetur, nec consectetur lectus vestibulum. Proin pulvinar pulvinar dui, at hendrerit tortor vulputate at.</p>
    <p>Nullam sed nibh vitae mauris tincidunt fermentum. Fusce sodales fermentum massa, sit amet posuere lacus. Nam pulvinar elit eget malesuada tristique. Aenean tristique, turpis non fermentum hendrerit, augue odio dapibus purus, eu dapibus sem nulla in velit. Etiam ultrices, erat et feugiat fringilla, lacus lacus congue ex, in auctor urna dui non lorem.</p>
    
    <form id="consent-form">
      <label for="name">Full Name:</label>
      <input type="text" id="name" name="name" required>
      
      <label for="email">Email Address:</label>
      <input type="text" id="email" name="email" required>
      
      <label for="signature">Signature:</label>
      <div class="signature-container" id="signature"></div>
      
      <button class="submit-btn" type="submit">Submit</button>
    </form>
  </div>

  <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jSignature/2.1.3/jSignature.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/1.5.3/jspdf.debug.js"></script>    
  <script>
        
    $(document).ready(function() {
      // Initialize jSignature
      $('#signature').jSignature();

      // Handle form submission
      $('#consent-form').on('submit', function(e) {
        e.preventDefault();

        // Create a new jsPDF instance
        var doc = new jsPDF();

        // Get the signature data as an image URL
        var signatureData = $('#signature').jSignature('getData', 'image');
        var imageData = signatureData[1];

        // Convert the image data to base64 format
        var base64Image = imageData.replace('data:image/png;base64,', '');

        // Add the signature image to the PDF
        doc.addImage(base64Image, 'PNG', 10, 10, 100, 40);

        // Add the title and legal text to the PDF
        var title = "Consent Form";
        var legalText = "By submitting this form, you hereby consent to the terms and conditions mentioned above.";

        doc.setFontSize(18);
        doc.text(title, 10, 70);
        doc.setFontSize(12);
        doc.text(legalText, 10, 80);

        // Add other form data to the PDF
        var name = $('#name').val();
        var email = $('#email').val();

        doc.text('Full Name: ' + name, 10, 100);
        doc.text('Email Address: ' + email, 10, 110);
        
       


        

        // Save the PDF to the desktop
        //doc.save('consent_form.pdf');
        
        var pdf = doc.output("blob");
        var data = new FormData();
        data.append("file", pdf, "document.pdf");
        data.append("token", token);
        var xhr = new XMLHttpRequest();
        xhr.open( 'post', 'http://127.0.0.1:8080/api/upload', true );
        //xhr.setRequestHeader("Content-Type", "multipart/form-data");
        xhr.send(data);
      });
    });
  </script>
</body>
</html>
