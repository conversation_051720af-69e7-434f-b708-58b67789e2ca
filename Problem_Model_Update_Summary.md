# Problem Model Update Summary

## Overview
Updated the Problem model in CarePlansController to include comprehensive healthcare problem tracking fields as requested.

## Changes Made

### 1. Problem Model (`final class Problem`)
**File**: `Sources/App/Controllers/CarePlans/CarePlansController.swift` (lines 521-537)

#### New Fields Added:
- `@Field(key: "clinical_note") var clinicalNote: String?` - Clinical notes about the problem
- `@Field(key: "status") var status: String` - Problem status (active | resolved | inactive)
- `@Field(key: "date_identified") var dateIdentified: Date` - When the problem was first identified
- `@Field(key: "source") var source: String` - Source of the problem (EHR import | self-reported | care team)
- `@Field(key: "confirmed_by") var confirmedBy: String?` - Who confirmed the problem

#### Field Reordering:
- Moved `icdCode` to be first field (matching your example)
- Kept `description` as required field
- Added all new fields with appropriate optionality

### 2. Database Migration (`CreateProblem`)
**File**: `Sources/App/Controllers/CarePlans/CarePlansController.swift` (lines 539-559)

#### New Database Fields:
```sql
.field("clinical_note", .string)           -- Optional clinical notes
.field("status", .string, .required)       -- Required status field
.field("date_identified", .date, .required) -- Required identification date
.field("source", .string, .required)       -- Required source field
.field("confirmed_by", .string)            -- Optional confirmer field
```

### 3. Controller Update (`updateProblem`)
**File**: `Sources/App/Controllers/CarePlans/CarePlansController.swift` (lines 329-344)

#### Updated to handle all new fields:
```swift
problem.icdCode = input.icdCode
problem.description = input.description
problem.clinicalNote = input.clinicalNote
problem.status = input.status
problem.dateIdentified = input.dateIdentified
problem.source = input.source
problem.confirmedBy = input.confirmedBy
```

### 4. Postman Collection Updates
**File**: `CarePlansController_Postman_Collection.json`

#### Create Problem Request:
```json
{
  "icdCode": "I10",
  "description": "Essential (primary) hypertension",
  "clinicalNote": "BP remains elevated despite medication adjustment.",
  "status": "active",
  "dateIdentified": "2023-06-01T00:00:00Z",
  "source": "EHR import",
  "confirmedBy": "Dr. Smith, MD"
}
```

#### Update Problem Request:
```json
{
  "icdCode": "I10",
  "description": "Essential (primary) hypertension",
  "clinicalNote": "BP now controlled with current medication regimen. Patient showing good compliance.",
  "status": "resolved",
  "dateIdentified": "2023-06-01T00:00:00Z",
  "source": "EHR import",
  "confirmedBy": "Dr. Smith, MD"
}
```

### 5. Documentation Updates
**File**: `CarePlansController_README.md`

#### Updated Problem Model Documentation:
- Added complete field descriptions
- Included valid status values: `active` | `resolved` | `inactive`
- Included valid source values: `EHR import` | `self-reported` | `care team`
- Updated example JSON to match new structure

## Field Specifications

### Required Fields:
- `description` - Problem description
- `status` - Current status of the problem
- `dateIdentified` - When the problem was identified
- `source` - Source of the problem information

### Optional Fields:
- `icdCode` - ICD-10 code for the problem
- `clinicalNote` - Additional clinical notes
- `confirmedBy` - Healthcare provider who confirmed the problem

### Status Values:
- `active` - Problem is currently active and being managed
- `resolved` - Problem has been resolved
- `inactive` - Problem is not currently active but may recur

### Source Values:
- `EHR import` - Problem imported from Electronic Health Record
- `self-reported` - Problem reported by the patient
- `care team` - Problem identified by care team members

## Database Migration Notes

**Important**: This update requires a database migration to add the new fields. The migration will:

1. Add `clinical_note` as optional string field
2. Add `status` as required string field
3. Add `date_identified` as required date field
4. Add `source` as required string field
5. Add `confirmed_by` as optional string field

**Migration Command**: Run your application's migration command to apply these schema changes.

## API Compatibility

### Breaking Changes:
- `status`, `dateIdentified`, and `source` are now **required fields**
- Existing API calls will need to include these fields

### Backward Compatibility:
- `icdCode`, `clinicalNote`, and `confirmedBy` remain optional
- Existing `description` field unchanged

## Testing

The updated Postman collection includes:
- Create Problem with all new fields
- Update Problem with status change example
- Realistic healthcare scenarios
- Proper date formatting (ISO 8601)

## Next Steps

1. **Run Database Migration**: Apply the schema changes to your database
2. **Update Client Applications**: Ensure all API consumers include the new required fields
3. **Test API Endpoints**: Use the updated Postman collection to verify functionality
4. **Update Documentation**: Share the updated API documentation with your team

## Example Usage

### Creating a New Problem:
```bash
POST /api/careplans/{carePlanID}/problems
Content-Type: application/json

{
  "icdCode": "I10",
  "description": "Essential (primary) hypertension",
  "clinicalNote": "BP remains elevated despite medication adjustment.",
  "status": "active",
  "dateIdentified": "2023-06-01T00:00:00Z",
  "source": "EHR import",
  "confirmedBy": "Dr. Smith, MD"
}
```

### Updating Problem Status:
```bash
PUT /api/careplans/{carePlanID}/problems/{problemID}
Content-Type: application/json

{
  "icdCode": "I10",
  "description": "Essential (primary) hypertension",
  "clinicalNote": "BP now controlled with current medication regimen.",
  "status": "resolved",
  "dateIdentified": "2023-06-01T00:00:00Z",
  "source": "EHR import",
  "confirmedBy": "Dr. Smith, MD"
}
```
