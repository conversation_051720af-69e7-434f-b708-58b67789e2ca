# Member Problems Integration Documentation

## Overview
This document describes the implementation of the member-problem relationship system that allows members to be assigned multiple problems from the existing Problem model. This creates a many-to-many relationship between Members and Problems through a pivot table.

## Architecture

### Database Schema

#### New Table: `member_problems`
```sql
CREATE TABLE member_problems (
    id UUID PRIMARY KEY,
    member_id UUID NOT NULL REFERENCES members(id) ON DELETE CASCADE,
    problem_id UUID NOT NULL REFERENCES problems(id) ON DELETE CASCADE,
    assigned_date DATE NOT NULL,
    assigned_by VARCHAR,
    notes TEXT,
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    UNIQUE(member_id, problem_id)
);
```

### Model Relationships

#### Member Model
```swift
@Siblings(through: MemberProblems.self, from: \.$member, to: \.$problem)
var problems: [Problem]
```

#### Problem Model
```swift
@Siblings(through: MemberProblems.self, from: \.$problem, to: \.$member)
var members: [Member]
```

#### MemberProblems Pivot Model
```swift
final class MemberProblems: Model {
    @Parent(key: "member_id") var member: Member
    @Parent(key: "problem_id") var problem: Problem
    @Field(key: "assigned_date") var assignedDate: Date
    @OptionalField(key: "assigned_by") var assignedBy: String?
    @OptionalField(key: "notes") var notes: String?
    @Field(key: "is_active") var isActive: Bool
}
```

## API Endpoints

### Base URL: `/api/members/{memberID}/problems`

#### 1. List Member Problems
```http
GET /api/members/{memberID}/problems
```
**Response:**
```json
[
  {
    "id": "uuid",
    "assignedDate": "2025-06-22T10:00:00Z",
    "assignedBy": "Dr. Smith, MD",
    "notes": "Patient reports ongoing symptoms",
    "isActive": true,
    "createdAt": "2025-06-22T10:00:00Z",
    "updatedAt": "2025-06-22T10:00:00Z",
    "problem": {
      "id": "uuid",
      "icdCode": "I10",
      "description": "Essential (primary) hypertension",
      "clinicalNote": "BP remains elevated despite medication adjustment",
      "status": "active",
      "dateIdentified": "2025-06-20T00:00:00Z",
      "source": "EHR import",
      "confirmedBy": "Dr. Johnson, MD"
    }
  }
]
```

#### 2. Assign Problem to Member
```http
POST /api/members/{memberID}/problems
```
**Request Body:**
```json
{
  "problemId": "uuid",
  "assignedBy": "Dr. Smith, MD",
  "notes": "Patient needs monitoring for this condition"
}
```

#### 3. Get Specific Member Problem
```http
GET /api/members/{memberID}/problems/{problemAssignmentID}
```

#### 4. Update Member Problem
```http
PUT /api/members/{memberID}/problems/{problemAssignmentID}
```
**Request Body:**
```json
{
  "assignedBy": "Dr. Johnson, MD",
  "notes": "Updated notes about the condition",
  "isActive": false
}
```

#### 5. Remove Problem from Member
```http
DELETE /api/members/{memberID}/problems/{problemAssignmentID}
```

#### 6. List Active Problems Only
```http
GET /api/members/{memberID}/problems/active
```

#### 7. List Inactive Problems Only
```http
GET /api/members/{memberID}/problems/inactive
```

#### 8. Search Member Problems
```http
GET /api/members/{memberID}/problems/search?q=hypertension&status=active&icd_code=I10
```

#### 9. Bulk Assign Problems
```http
POST /api/members/{memberID}/problems/bulk
```
**Request Body:**
```json
[
  {
    "problemId": "uuid1",
    "assignedBy": "Dr. Smith, MD",
    "notes": "First condition"
  },
  {
    "problemId": "uuid2",
    "assignedBy": "Dr. Smith, MD",
    "notes": "Second condition"
  }
]
```

#### 10. Bulk Deactivate Problems
```http
PUT /api/members/{memberID}/problems/bulk/deactivate
```
**Request Body:**
```json
["uuid1", "uuid2", "uuid3"]
```

## Key Features

### 1. Many-to-Many Relationship
- Members can have multiple problems
- Problems can be assigned to multiple members
- Each assignment is tracked individually with metadata

### 2. Assignment Tracking
- **Assigned Date**: When the problem was assigned to the member
- **Assigned By**: Who assigned the problem (doctor, care coordinator, etc.)
- **Notes**: Specific notes about this assignment
- **Is Active**: Whether the assignment is currently active

### 3. Flexible Problem Management
- Activate/deactivate problem assignments without deletion
- Search and filter problems by various criteria
- Bulk operations for efficiency
- Detailed assignment history

### 4. Integration with Existing Systems
- Leverages existing Problem model from CarePlans
- Maintains all existing Problem functionality
- Seamless integration with Member model
- Automatic eager loading in member queries

## Data Flow

### Problem Assignment Workflow
1. **Search/Select Problem**: Use existing CarePlan problems or create new ones
2. **Assign to Member**: Create MemberProblems relationship
3. **Track Assignment**: Record who assigned it and when
4. **Manage Status**: Activate/deactivate as needed
5. **Monitor Progress**: Update notes and status over time

### Member Problem Access
1. **Member Fetch**: Problems automatically loaded with member data
2. **Problem Filtering**: Filter by active/inactive status
3. **Search Capability**: Search by description, ICD code, or notes
4. **Bulk Operations**: Manage multiple problems efficiently

## Use Cases

### 1. Care Coordination
- Assign problems from care plans to specific members
- Track which care team member made the assignment
- Monitor problem resolution across care episodes

### 2. Chronic Disease Management
- Assign ongoing conditions to members
- Track long-term problem management
- Coordinate care across multiple providers

### 3. Population Health
- Identify members with specific conditions
- Track problem prevalence across member populations
- Generate reports on problem assignments

### 4. Clinical Documentation
- Maintain detailed problem lists for each member
- Track problem assignment history
- Support clinical decision making

## Migration and Setup

### 1. Migration Registration
```swift
// In configure.swift
app.migrations.add(CreateMemberProblems())
```

### 2. Controller Registration
```swift
// In routes.swift
try app.register(collection: MemberProblemsController())
```

### 3. Run Migration
```bash
swift run Run migrate
```

## Error Handling

### Common Scenarios
- **Member Not Found**: 404 with appropriate message
- **Problem Not Found**: 404 with appropriate message
- **Duplicate Assignment**: Updates existing assignment instead of creating duplicate
- **Invalid Assignment ID**: 404 for non-existent assignments
- **Validation Errors**: 400 with detailed validation messages

## Security Considerations

### Authorization
- All endpoints require valid authentication token
- Member access controlled by organization membership
- Problem assignments respect care team permissions

### Data Validation
- UUID validation for all IDs
- Required field validation
- Relationship integrity checks
- Unique constraint enforcement

## Performance Optimizations

### Database Optimizations
- Unique constraint prevents duplicate assignments
- Indexes on member_id and problem_id for fast lookups
- Cascade deletes maintain referential integrity

### Query Optimizations
- Eager loading of problem relationships
- Efficient filtering and search queries
- Bulk operations for multiple assignments

## Testing

### Unit Tests
- Model relationship tests
- Controller endpoint tests
- Validation logic tests
- Error handling tests

### Integration Tests
- End-to-end assignment workflows
- Bulk operation tests
- Search and filtering tests
- Performance tests

## Future Enhancements

### Potential Features
- Problem assignment notifications
- Assignment approval workflows
- Problem severity tracking
- Integration with care plan goals
- Automated problem suggestions
- Problem assignment analytics

## Troubleshooting

### Common Issues
1. **Migration Fails**: Check database permissions and existing data
2. **Duplicate Key Errors**: Ensure unique constraint is properly handled
3. **Relationship Loading**: Verify eager loading configuration
4. **Performance Issues**: Check query optimization and indexing

### Debug Commands
```bash
# Check migration status
swift run Run migrate --dry-run

# Verify table structure
psql -d your_db -c "\d member_problems"

# Check relationship data
psql -d your_db -c "SELECT COUNT(*) FROM member_problems"
```

This implementation provides a robust, scalable solution for managing member-problem relationships while maintaining the integrity and functionality of existing systems.
