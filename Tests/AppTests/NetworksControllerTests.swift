//
//  NetworksControllerTests.swift
//  
//
//  Created by <PERSON> on 7/14/25.
//

import XCTVapor
import Fluent
@testable import App

final class NetworksControllerTests: XCTestCase {
    var app: Application!
    
    override func setUp() async throws {
        app = Application(.testing)
        try configure(app)
        
        // Run migrations
        try await app.autoMigrate()
    }
    
    override func tearDown() async throws {
        try await app.autoRevert()
        app.shutdown()
    }
    
    // MARK: - Organization Networks API Tests
    
    func testNetworksByOrganizationWithValidOrgID() async throws {
        // Create test organization
        let org = Organization(title: "Test Hospital", type: "hospital")
        try await org.save(on: app.db)
        let orgID = try org.requireID()
        
        // Create test networks
        let network1 = Network(name: "Test Network 1", types: ["medical"], status: "active")
        let network2 = Network(name: "Test Network 2", types: ["dental"], status: "active")
        
        try await org.$networks.create([network1, network2], on: app.db)
        
        // Test the new API endpoint
        try await app.test(.GET, "/networks/organization/\(orgID)") { res in
            XCTAssertEqual(res.status, .ok)
            
            let page = try res.content.decode(Page<Network>.self)
            XCTAssertEqual(page.items.count, 2)
            XCTAssertEqual(page.metadata.total, 2)
            
            // Verify networks are sorted by name
            XCTAssertEqual(page.items[0].name, "Test Network 1")
            XCTAssertEqual(page.items[1].name, "Test Network 2")
        }
    }
    
    func testNetworksByOrganizationWithInvalidOrgID() async throws {
        try await app.test(.GET, "/networks/organization/invalid-uuid") { res in
            XCTAssertEqual(res.status, .internalServerError)
        }
    }
    
    func testNetworksByOrganizationWithSearchByName() async throws {
        // Create test organization
        let org = Organization(title: "Test Hospital", type: "hospital")
        try await org.save(on: app.db)
        let orgID = try org.requireID()
        
        // Create test networks
        let network1 = Network(name: "General Hospital", types: ["medical"], status: "active")
        let network2 = Network(name: "Dental Clinic", types: ["dental"], status: "active")
        let network3 = Network(name: "Emergency Hospital", types: ["emergency"], status: "active")
        
        try await org.$networks.create([network1, network2, network3], on: app.db)
        
        // Test search by name
        try await app.test(.GET, "/networks/organization/\(orgID)?name=hospital") { res in
            XCTAssertEqual(res.status, .ok)
            
            let page = try res.content.decode(Page<Network>.self)
            XCTAssertEqual(page.items.count, 2)
            
            // Should find both networks with "hospital" in the name
            let networkNames = page.items.map { $0.name }.sorted()
            XCTAssertTrue(networkNames.contains("General Hospital"))
            XCTAssertTrue(networkNames.contains("Emergency Hospital"))
            XCTAssertFalse(networkNames.contains("Dental Clinic"))
        }
    }
    
    func testNetworksByOrganizationWithPagination() async throws {
        // Create test organization
        let org = Organization(title: "Test Hospital", type: "hospital")
        try await org.save(on: app.db)
        let orgID = try org.requireID()
        
        // Create multiple test networks
        var networks: [Network] = []
        for i in 1...25 {
            let network = Network(name: "Network \(String(format: "%02d", i))", types: ["medical"], status: "active")
            networks.append(network)
        }
        
        try await org.$networks.create(networks, on: app.db)
        
        // Test first page with default pagination
        try await app.test(.GET, "/networks/organization/\(orgID)") { res in
            XCTAssertEqual(res.status, .ok)
            
            let page = try res.content.decode(Page<Network>.self)
            XCTAssertEqual(page.items.count, 20) // Default page size
            XCTAssertEqual(page.metadata.total, 25)
            XCTAssertEqual(page.metadata.page, 1)
            XCTAssertEqual(page.metadata.pageCount, 2)
        }
        
        // Test second page
        try await app.test(.GET, "/networks/organization/\(orgID)?page=2") { res in
            XCTAssertEqual(res.status, .ok)
            
            let page = try res.content.decode(Page<Network>.self)
            XCTAssertEqual(page.items.count, 5) // Remaining items
            XCTAssertEqual(page.metadata.page, 2)
        }
        
        // Test custom page size
        try await app.test(.GET, "/networks/organization/\(orgID)?per=10") { res in
            XCTAssertEqual(res.status, .ok)
            
            let page = try res.content.decode(Page<Network>.self)
            XCTAssertEqual(page.items.count, 10)
            XCTAssertEqual(page.metadata.pageCount, 3)
        }
    }
    
    func testNetworksByOrganizationIncludesRelatedData() async throws {
        // Create test organization
        let org = Organization(title: "Test Hospital", type: "hospital")
        try await org.save(on: app.db)
        let orgID = try org.requireID()
        
        // Create test network
        let network = Network(name: "Test Network", types: ["medical"], status: "active")
        try await org.$networks.create(network, on: app.db)
        
        // Test that related data is included
        try await app.test(.GET, "/networks/organization/\(orgID)") { res in
            XCTAssertEqual(res.status, .ok)
            
            let page = try res.content.decode(Page<Network>.self)
            XCTAssertEqual(page.items.count, 1)
            
            let returnedNetwork = page.items[0]
            XCTAssertEqual(returnedNetwork.name, "Test Network")
            
            // Verify related data is loaded (these should not be nil if properly loaded)
            XCTAssertNotNil(returnedNetwork.services)
            XCTAssertNotNil(returnedNetwork.address)
            XCTAssertNotNil(returnedNetwork.phones)
            XCTAssertNotNil(returnedNetwork.carriers)
        }
    }
}
