//
//  ShortUrlTests.swift
//  
//
//  Created by <PERSON> on 6/29/25.
//

import XCTVapor
import Fluent
@testable import App

final class ShortUrlTests: XCTestCase {
    var app: Application!
    
    override func setUp() async throws {
        app = Application(.testing)
        try await configure(app)
        try await app.autoMigrate()
    }
    
    override func tearDown() async throws {
        try await app.autoRevert()
        app.shutdown()
    }
    
    func testCreateShortUrl() async throws {
        // Create a short URL for password reset
        let resetToken = "test-reset-token-123"
        let shortUrl = try await ShortUrl.createForPasswordReset(
            resetToken: resetToken,
            userId: nil,
            expirationMinutes: 30,
            on: app.db
        )
        
        XCTAssertNotNil(shortUrl.id)
        XCTAssertEqual(shortUrl.resetToken, resetToken)
        XCTAssertEqual(shortUrl.shortCode.count, 6)
        XCTAssertTrue(shortUrl.isValid())
        XCTAssertEqual(shortUrl.clickCount, 0)
        XCTAssertEqual(shortUrl.originalUrl, "/password-reset/verify?token=\(resetToken)")
    }
    
    func testShortUrlRedirect() async throws {
        // Create a short URL
        let resetToken = "test-reset-token-456"
        let shortUrl = try await ShortUrl.createForPasswordReset(
            resetToken: resetToken,
            userId: nil,
            expirationMinutes: 30,
            on: app.db
        )
        
        // Test the redirect
        try await app.test(.GET, "/s/\(shortUrl.shortCode)") { res in
            XCTAssertEqual(res.status, .temporaryRedirect)
            XCTAssertEqual(res.headers.first(name: .location), "/password-reset/verify?token=\(resetToken)")
        }
        
        // Verify click count was incremented
        let updatedShortUrl = try await ShortUrl.find(shortUrl.id, on: app.db)
        XCTAssertEqual(updatedShortUrl?.clickCount, 1)
    }
    
    func testShortUrlNotFound() async throws {
        try await app.test(.GET, "/s/nonexistent") { res in
            XCTAssertEqual(res.status, .notFound)
        }
    }
    
    func testExpiredShortUrl() async throws {
        // Create an expired short URL
        let resetToken = "expired-token"
        let shortUrl = ShortUrl(
            shortCode: "expire1",
            originalUrl: "/password-reset/verify?token=\(resetToken)",
            resetToken: resetToken,
            userId: nil,
            expirationMinutes: -1 // Already expired
        )
        try await shortUrl.save(on: app.db)
        
        // Test accessing expired URL
        try await app.test(.GET, "/s/expire1") { res in
            XCTAssertEqual(res.status, .gone)
        }
    }
    
    func testShortUrlStats() async throws {
        // Create a short URL and record some clicks
        let resetToken = "stats-test-token"
        let shortUrl = try await ShortUrl.createForPasswordReset(
            resetToken: resetToken,
            userId: nil,
            expirationMinutes: 30,
            on: app.db
        )
        
        // Record a click
        try await shortUrl.recordClick(on: app.db)
        
        // Test stats endpoint
        try await app.test(.GET, "/api/short-urls/\(shortUrl.shortCode)/stats") { res in
            XCTAssertEqual(res.status, .ok)
            let stats = try res.content.decode(ShortUrlStats.self)
            XCTAssertEqual(stats.shortCode, shortUrl.shortCode)
            XCTAssertEqual(stats.clickCount, 1)
            XCTAssertTrue(stats.isValid)
        }
    }
    
    func testUniqueShortCodeGeneration() async throws {
        // Create multiple short URLs to test uniqueness
        var shortCodes: Set<String> = []
        
        for i in 0..<10 {
            let shortUrl = try await ShortUrl.createForPasswordReset(
                resetToken: "token-\(i)",
                userId: nil,
                expirationMinutes: 30,
                on: app.db
            )
            
            XCTAssertFalse(shortCodes.contains(shortUrl.shortCode), "Short code should be unique")
            shortCodes.insert(shortUrl.shortCode)
        }
        
        XCTAssertEqual(shortCodes.count, 10)
    }
}
