import XCTVapor
@testable import App

final class PhoneNumberTests: XCTestCase {
    var app: Application!
    
    override func setUpWithError() throws {
        app = Application(.testing)
        try configure(app)
    }
    
    override func tearDownWithError() throws {
        app.shutdown()
    }
    
    // MARK: - Phone Number Formatting Tests
    
    func testSMSNumberFormattingWithUSNumbers() throws {
        // Test 10-digit US number
        let phone1 = PhoneNumber(label: "main", number: "**********")
        XCTAssertEqual(phone1.smsNumber(), "+1**********")
        
        // Test 11-digit US number with leading 1
        let phone2 = PhoneNumber(label: "main", number: "1**********")
        XCTAssertEqual(phone2.smsNumber(), "+1**********")
        
        // Test formatted US number
        let phone3 = PhoneNumber(label: "main", number: "(*************")
        XCTAssertEqual(phone3.smsNumber(), "+1**********")
        
        // Test US number with dashes and spaces
        let phone4 = PhoneNumber(label: "main", number: "************")
        XCTAssertEqual(phone4.smsNumber(), "+1**********")
        
        // Test US number with spaces
        let phone5 = PhoneNumber(label: "main", number: "************")
        XCTAssertEqual(phone5.smsNumber(), "+1**********")
    }
    
    func testSMSNumberFormattingWithInternationalNumbers() throws {
        // Test already formatted international number
        let phone1 = PhoneNumber(label: "main", number: "+447911123456")
        XCTAssertEqual(phone1.smsNumber(), "+447911123456")
        
        // Test international number with formatting
        let phone2 = PhoneNumber(label: "main", number: "+44 (*************")
        XCTAssertEqual(phone2.smsNumber(), "+447911123456")
        
        // Test French number
        let phone3 = PhoneNumber(label: "main", number: "+33 1 23 45 67 89")
        XCTAssertEqual(phone3.smsNumber(), "+33123456789")
    }
    
    func testSMSNumberFormattingEdgeCases() throws {
        // Test empty number
        let phone1 = PhoneNumber(label: "main", number: "")
        XCTAssertNil(phone1.smsNumber())
        
        // Test number with only formatting characters
        let phone2 = PhoneNumber(label: "main", number: "()-")
        XCTAssertNil(phone2.smsNumber())
        
        // Test number with whitespace only
        let phone3 = PhoneNumber(label: "main", number: "   ")
        XCTAssertNil(phone3.smsNumber())
        
        // Test invalid length number (too short)
        let phone4 = PhoneNumber(label: "main", number: "123")
        XCTAssertEqual(phone4.smsNumber(), "123") // Returns as-is for manual handling
        
        // Test invalid length number (too long for US)
        let phone5 = PhoneNumber(label: "main", number: "123456789012345")
        XCTAssertEqual(phone5.smsNumber(), "123456789012345") // Returns as-is for manual handling
    }
    
    // MARK: - String Extension Tests
    
    func testStringFormatToE164() throws {
        // Test 10-digit US number
        XCTAssertEqual("**********".formatToE164(), "+1**********")
        
        // Test 11-digit US number with leading 1
        XCTAssertEqual("1**********".formatToE164(), "+1**********")
        
        // Test formatted US number
        XCTAssertEqual("(*************".formatToE164(), "+1**********")
        
        // Test already formatted international number
        XCTAssertEqual("+447911123456".formatToE164(), "+447911123456")
        
        // Test international number with formatting
        XCTAssertEqual("+44 (*************".formatToE164(), "+447911123456")
        
        // Test empty string
        XCTAssertNil("".formatToE164())
        
        // Test string with only formatting characters
        XCTAssertNil("()-".formatToE164())
    }
    
    func testStringStripPhone() throws {
        // Test basic formatting removal
        XCTAssertEqual("(*************".stripPhone(), "**********")
        
        // Test with spaces
        XCTAssertEqual("************".stripPhone(), "**********")
        
        // Test with dashes
        XCTAssertEqual("************".stripPhone(), "**********")
        
        // Test with mixed formatting
        XCTAssertEqual("(************* ".stripPhone(), "**********")
        
        // Test with no formatting
        XCTAssertEqual("**********".stripPhone(), "**********")
        
        // Test with plus sign (should be preserved in stripPhone)
        XCTAssertEqual("+1**********".stripPhone(), "+1**********")
    }
    
    // MARK: - Member SMS Phone Tests
    
    func testMemberSMSPhone() async throws {
        // Create a test member
        let member = Member(
            email: "<EMAIL>",
            firstName: "John",
            lastName: "Doe",
            type: "patient",
            roles: ["member"],
            dob: "1990-01-01"
        )
        try await member.save(on: app.db)
        
        // Create a main phone number
        let phoneNumber = PhoneNumber(label: "main", number: "**********")
        phoneNumber.$member.id = member.id
        try await phoneNumber.save(on: app.db)
        
        // Load member with phones
        let memberWithPhones = try await Member.query(on: app.db)
            .filter(\.$id == member.id!)
            .with(\.$phones)
            .first()
        
        XCTAssertNotNil(memberWithPhones)
        XCTAssertEqual(memberWithPhones?.smsPhone(), "+1**********")
    }
    
    func testMemberSMSPhoneWithNoMainPhone() async throws {
        // Create a test member
        let member = Member(
            email: "<EMAIL>",
            firstName: "John",
            lastName: "Doe",
            type: "patient",
            roles: ["member"],
            dob: "1990-01-01"
        )
        try await member.save(on: app.db)
        
        // Create a non-main phone number
        let phoneNumber = PhoneNumber(label: "mobile", number: "**********")
        phoneNumber.$member.id = member.id
        try await phoneNumber.save(on: app.db)
        
        // Load member with phones
        let memberWithPhones = try await Member.query(on: app.db)
            .filter(\.$id == member.id!)
            .with(\.$phones)
            .first()
        
        XCTAssertNotNil(memberWithPhones)
        XCTAssertNil(memberWithPhones?.smsPhone()) // Should return nil since no "main" phone
    }
}
