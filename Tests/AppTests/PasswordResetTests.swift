//
//  PasswordResetTests.swift
//  
//
//  Created by <PERSON> on 6/29/25.
//

import XCTVapor
import Fluent
@testable import App

final class PasswordResetTests: XCTestCase {
    var app: Application!
    
    override func setUp() async throws {
        app = Application(.testing)
        try configure(app)
        
        // Run migrations
        try await app.autoMigrate()
    }
    
    override func tearDown() async throws {
        try await app.autoRevert()
        app.shutdown()
    }
    
    // MARK: - Forgot Password Tests
    
    func testForgotPasswordPageLoads() async throws {
        try await app.test(.GET, "/password-reset/forgot") { res in
            XCTAssertEqual(res.status, .ok)
            XCTAssertTrue(res.body.string.contains("Forgot Password"))
        }
    }
    
    func testForgotPasswordWithValidUser() async throws {
        // Create test user
        let user = AuthUser(username: "<EMAIL>", passwordHash: try Bcrypt.hash("password123"))
        try await user.save(on: app.db)
        
        try await app.test(.POST, "/password-reset/forgot", beforeRequest: { req in
            try req.content.encode(ForgotPasswordRequest(username: "<EMAIL>"))
        }) { res in
            XCTAssertEqual(res.status, .seeOther)
            XCTAssertTrue(res.headers.first(name: .location)?.contains("success=") == true)
        }
        
        // Verify token was generated
        let updatedUser = try await AuthUser.find(user.id, on: app.db)
        XCTAssertNotNil(updatedUser?.resetToken)
        XCTAssertNotNil(updatedUser?.resetTokenExpiresAt)
    }
    
    func testForgotPasswordWithInvalidUser() async throws {
        try await app.test(.POST, "/password-reset/forgot", beforeRequest: { req in
            try req.content.encode(ForgotPasswordRequest(username: "<EMAIL>"))
        }) { res in
            XCTAssertEqual(res.status, .seeOther)
            // Should still show success message for security
            XCTAssertTrue(res.headers.first(name: .location)?.contains("success=") == true)
        }
    }
    
    // MARK: - Password Reset Tests
    
    func testResetPasswordPageLoads() async throws {
        try await app.test(.GET, "/password-reset/verify") { res in
            XCTAssertEqual(res.status, .ok)
            XCTAssertTrue(res.body.string.contains("Reset Password"))
        }
    }
    
    func testResetPasswordWithValidToken() async throws {
        // Create test user with reset token
        let user = AuthUser(username: "<EMAIL>", passwordHash: try Bcrypt.hash("oldpassword"))
        user.setResetToken(expirationMinutes: 30)
        try await user.save(on: app.db)
        
        let resetToken = user.resetToken!
        
        try await app.test(.POST, "/password-reset/reset", beforeRequest: { req in
            try req.content.encode(ResetPasswordRequest(
                token: resetToken,
                newPassword: "newpassword123",
                confirmPassword: "newpassword123"
            ))
        }) { res in
            XCTAssertEqual(res.status, .seeOther)
            XCTAssertTrue(res.headers.first(name: .location)?.contains("login") == true)
        }
        
        // Verify password was changed and token was cleared
        let updatedUser = try await AuthUser.find(user.id, on: app.db)
        XCTAssertNil(updatedUser?.resetToken)
        XCTAssertNil(updatedUser?.resetTokenExpiresAt)
        XCTAssertTrue(try Bcrypt.verify("newpassword123", created: updatedUser!.passwordHash))
    }
    
    func testResetPasswordWithInvalidToken() async throws {
        try await app.test(.POST, "/password-reset/reset", beforeRequest: { req in
            try req.content.encode(ResetPasswordRequest(
                token: "invalid-token",
                newPassword: "newpassword123",
                confirmPassword: "newpassword123"
            ))
        }) { res in
            XCTAssertEqual(res.status, .seeOther)
            XCTAssertTrue(res.headers.first(name: .location)?.contains("error=") == true)
        }
    }
    
    func testResetPasswordWithExpiredToken() async throws {
        // Create test user with expired reset token
        let user = AuthUser(username: "<EMAIL>", passwordHash: try Bcrypt.hash("oldpassword"))
        user.resetToken = "valid-token"
        user.resetTokenExpiresAt = Calendar.current.date(byAdding: .hour, value: -1, to: Date()) // Expired 1 hour ago
        try await user.save(on: app.db)
        
        try await app.test(.POST, "/password-reset/reset", beforeRequest: { req in
            try req.content.encode(ResetPasswordRequest(
                token: "valid-token",
                newPassword: "newpassword123",
                confirmPassword: "newpassword123"
            ))
        }) { res in
            XCTAssertEqual(res.status, .seeOther)
            XCTAssertTrue(res.headers.first(name: .location)?.contains("error=") == true)
        }
    }
    
    func testResetPasswordWithMismatchedPasswords() async throws {
        let user = AuthUser(username: "<EMAIL>", passwordHash: try Bcrypt.hash("oldpassword"))
        user.setResetToken(expirationMinutes: 30)
        try await user.save(on: app.db)
        
        try await app.test(.POST, "/password-reset/reset", beforeRequest: { req in
            try req.content.encode(ResetPasswordRequest(
                token: user.resetToken!,
                newPassword: "newpassword123",
                confirmPassword: "differentpassword"
            ))
        }) { res in
            XCTAssertEqual(res.status, .seeOther)
            XCTAssertTrue(res.headers.first(name: .location)?.contains("error=Passwords%20do%20not%20match") == true)
        }
    }
    
    func testResetPasswordWithWeakPassword() async throws {
        let user = AuthUser(username: "<EMAIL>", passwordHash: try Bcrypt.hash("oldpassword"))
        user.setResetToken(expirationMinutes: 30)
        try await user.save(on: app.db)
        
        try await app.test(.POST, "/password-reset/reset", beforeRequest: { req in
            try req.content.encode(ResetPasswordRequest(
                token: user.resetToken!,
                newPassword: "weak",
                confirmPassword: "weak"
            ))
        }) { res in
            XCTAssertEqual(res.status, .seeOther)
            XCTAssertTrue(res.headers.first(name: .location)?.contains("error=") == true)
        }
    }
    
    // MARK: - AuthUser Model Tests
    
    func testAuthUserResetTokenGeneration() throws {
        let user = AuthUser(username: "<EMAIL>", passwordHash: "hash")
        
        XCTAssertNil(user.resetToken)
        XCTAssertNil(user.resetTokenExpiresAt)
        
        user.setResetToken(expirationMinutes: 30)
        
        XCTAssertNotNil(user.resetToken)
        XCTAssertNotNil(user.resetTokenExpiresAt)
        XCTAssertTrue(user.isResetTokenValid())
    }
    
    func testAuthUserResetTokenExpiration() throws {
        let user = AuthUser(username: "<EMAIL>", passwordHash: "hash")
        user.resetToken = "token"
        user.resetTokenExpiresAt = Calendar.current.date(byAdding: .hour, value: -1, to: Date()) // Expired
        
        XCTAssertFalse(user.isResetTokenValid())
    }
    
    func testAuthUserClearResetToken() throws {
        let user = AuthUser(username: "<EMAIL>", passwordHash: "hash")
        user.setResetToken(expirationMinutes: 30)
        
        XCTAssertNotNil(user.resetToken)
        XCTAssertNotNil(user.resetTokenExpiresAt)
        
        user.clearResetToken()
        
        XCTAssertNil(user.resetToken)
        XCTAssertNil(user.resetTokenExpiresAt)
    }

    // MARK: - Rate Limiting Tests

    func testRateLimitingPreventsExcessiveRequests() async throws {
        // Create test user
        let user = AuthUser(username: "<EMAIL>", passwordHash: try Bcrypt.hash("password123"))
        try await user.save(on: app.db)

        // Make multiple requests to trigger rate limiting
        for i in 1...6 { // Assuming max 5 attempts per hour
            try await app.test(.POST, "/password-reset/forgot", beforeRequest: { req in
                try req.content.encode(ForgotPasswordRequest(username: "<EMAIL>"))
            }) { res in
                if i <= 5 {
                    XCTAssertEqual(res.status, .seeOther)
                } else {
                    XCTAssertEqual(res.status, .tooManyRequests)
                }
            }
        }
    }

    // MARK: - Security Tests

    func testPasswordValidationThroughAPI() async throws {
        let user = AuthUser(username: "<EMAIL>", passwordHash: try Bcrypt.hash("password123"))
        user.setResetToken(expirationMinutes: 30)
        try await user.save(on: app.db)

        // Test weak password through API
        try await app.test(.POST, "/password-reset/reset", beforeRequest: { req in
            try req.content.encode(ResetPasswordRequest(
                token: user.resetToken!,
                newPassword: "weak",
                confirmPassword: "weak"
            ))
        }) { res in
            XCTAssertEqual(res.status, .seeOther)
            XCTAssertTrue(res.headers.first(name: .location)?.contains("error=") == true)
        }
    }

    func testTokenFormatValidation() async throws {
        let user = AuthUser(username: "<EMAIL>", passwordHash: try Bcrypt.hash("password123"))
        user.setResetToken(expirationMinutes: 30)
        try await user.save(on: app.db)

        // Test empty token
        try await app.test(.POST, "/password-reset/reset", beforeRequest: { req in
            try req.content.encode(ResetPasswordRequest(
                token: "",
                newPassword: "newpassword123",
                confirmPassword: "newpassword123"
            ))
        }) { res in
            XCTAssertEqual(res.status, .seeOther)
            XCTAssertTrue(res.headers.first(name: .location)?.contains("error=") == true)
        }

        // Test whitespace-only token
        try await app.test(.POST, "/password-reset/reset", beforeRequest: { req in
            try req.content.encode(ResetPasswordRequest(
                token: "   ",
                newPassword: "newpassword123",
                confirmPassword: "newpassword123"
            ))
        }) { res in
            XCTAssertEqual(res.status, .seeOther)
            XCTAssertTrue(res.headers.first(name: .location)?.contains("error=") == true)
        }
    }
}
