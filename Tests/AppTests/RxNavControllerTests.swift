import XCTest
import Vapor
@testable import App

final class RxNavControllerTests: XCTestCase {
    var app: Application!

    override func setUp() async throws {
        app = Application(.testing)
        try configure(app)
    }

    override func tearDown() async throws {
        app.shutdown()
    }

    func testHealthCheck() async throws {
        try app.test(.GET, "rxnav/health") { res in
            XCTAssertEqual(res.status, .ok)
            let response = try res.content.decode([String: String].self)
            XCTAssertEqual(response["service"], "RxNav API")
            XCTAssertEqual(response["status"], "healthy")
            XCTAssertNotNil(response["timestamp"])
            XCTAssertEqual(response["version"], "1.0.0")
        }
    }

    func testSearchMedications() async throws {
        let searchRequest = MedicationSearchRequest(name: "lisinopril", includeDetails: false)

        try app.test(.POST, "rxnav/search", beforeRequest: { req in
            try req.content.encode(searchRequest)
        }) { res in
            XCTAssertEqual(res.status, .ok)
            let response = try res.content.decode(MedicationSearchResponse.self)
            XCTAssertEqual(response.searchTerm, "lisinopril")
            // Since we're using a mock implementation, we expect 0 results for now
            XCTAssertEqual(response.totalResults, 0)
            XCTAssertEqual(response.medications.count, 0)
        }
    }
    
    // Tests for unimplemented endpoints - should return 501 Not Implemented
    func testGetMedicationDetailsNotImplemented() async throws {
        let detailRequest = MedicationDetailRequest(rxcui: "123456")

        try app.test(.POST, "rxnav/details", beforeRequest: { req in
            try req.content.encode(detailRequest)
        }) { res in
            XCTAssertEqual(res.status, .notImplemented)
        }
    }
    
    func testMedicationInfoParsing() {
        // Test the utility methods for parsing medication information
        let controller = RxNavController()

        // Test strength extraction
        XCTAssertEqual(controller.extractStrength(from: "lisinopril 10 MG Oral Tablet"), "10 MG")
        XCTAssertEqual(controller.extractStrength(from: "aspirin 325 MG Oral Tablet"), "325 MG")
        XCTAssertNil(controller.extractStrength(from: "lisinopril Oral Tablet"))

        // Test dose form extraction
        XCTAssertEqual(controller.extractDoseForm(from: "lisinopril 10 MG Oral Tablet"), "Oral Tablet")
        XCTAssertEqual(controller.extractDoseForm(from: "insulin injection"), "Injection")

        // Test route extraction
        XCTAssertEqual(controller.extractRoute(from: "lisinopril 10 MG Oral Tablet"), "Oral")
        XCTAssertEqual(controller.extractRoute(from: "insulin Topical Cream"), "Topical")

        // Test brand name extraction
        XCTAssertEqual(controller.extractBrandName(from: "lisinopril 10 MG Oral Tablet [Zestril]", tty: "SBD"), "Zestril")
        XCTAssertNil(controller.extractBrandName(from: "lisinopril 10 MG Oral Tablet", tty: "SCD"))

        // Test generic medication detection
        XCTAssertTrue(controller.isGenericMedication(tty: "SCD"))
        XCTAssertTrue(controller.isGenericMedication(tty: "GPCK"))
        XCTAssertFalse(controller.isGenericMedication(tty: "SBD"))
        XCTAssertFalse(controller.isGenericMedication(tty: "BPCK"))
    }

    func testBasicControllerStructure() {
        // Test that the controller can be instantiated
        let controller = RxNavController()
        XCTAssertNotNil(controller)
    }
}
