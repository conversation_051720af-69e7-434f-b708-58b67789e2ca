//
//  CarePlanTimelineTests.swift
//  
//
//  Created by Augment Agent on 6/21/25.
//

import XCTest
import Vapor
import Fluent
@testable import App

final class CarePlanTimelineTests: XCTestCase {
    var app: Application!
    
    override func setUp() async throws {
        app = Application(.testing)
        try await configure(app)
        try await app.autoMigrate()
    }
    
    override func tearDown() async throws {
        try await app.autoRevert()
        app.shutdown()
    }
    
    func testCarePlanTimelineCreation() async throws {
        // Create a test member first
        let member = Member()
        member.email = "<EMAIL>"
        member.firstName = "Test"
        member.lastName = "User"
        member.type = "member"
        member.roles = ["patient"]
        member.dob = "1990-01-01"
        member.status = "active"
        try await member.save(on: app.db)
        
        // Create a care plan
        let carePlan = CarePlan()
        carePlan.$member.id = try member.requireID()
        carePlan.startDate = Date()
        carePlan.status = "active"
        try await carePlan.save(on: app.db)
        
        // Create timeline entry using our service
        try await CarePlanTimelineService.createCarePlanTimeline(
            operation: .created,
            carePlan: carePlan,
            on: app.db,
            memberID: try member.requireID()
        )
        
        // Verify timeline entry was created
        let carePlanID = try carePlan.requireID()
        let timelineItems = try await TimelineItem.query(on: app.db)
            .filter(\.$carePlan.$id == carePlanID)
            .all()
        
        XCTAssertEqual(timelineItems.count, 1)
        
        let timelineItem = timelineItems.first!
        XCTAssertEqual(timelineItem.status, "created")
        XCTAssertEqual(timelineItem.title, "Care Plan Created")
        XCTAssertTrue(timelineItem.desc.contains("Care Plan was created"))
        XCTAssertEqual(timelineItem.visible, true)
        XCTAssertEqual(timelineItem.memberId, try member.requireID())
        
        // Verify metadata
        XCTAssertNotNil(timelineItem.meta)
        XCTAssertEqual(timelineItem.meta?.data["ref_id"], try carePlan.requireID().uuidString)
        XCTAssertEqual(timelineItem.meta?.data["entity_type"], "CarePlan")
        XCTAssertEqual(timelineItem.meta?.data["operation"], "created")
    }
    
    func testGoalTimelineCreation() async throws {
        // Create test data
        let member = Member()
        member.email = "<EMAIL>"
        member.firstName = "Test"
        member.lastName = "User"
        member.type = "member"
        member.roles = ["patient"]
        member.dob = "1990-01-01"
        member.status = "active"
        try await member.save(on: app.db)
        
        let carePlan = CarePlan()
        carePlan.$member.id = try member.requireID()
        carePlan.startDate = Date()
        carePlan.status = "active"
        try await carePlan.save(on: app.db)
        
        let goal = Goal()
        goal.$carePlan.id = try carePlan.requireID()
        goal.description = "Test Goal"
        goal.type = "health"
        goal.targetDate = Date()
        goal.status = "active"
        goal.objective = "Test objective"
        goal.measurementCriteria = "Test criteria"
        try await goal.save(on: app.db)
        
        // Create timeline entry
        try await CarePlanTimelineService.createGoalTimeline(
            operation: .created,
            goal: goal,
            on: app.db
        )
        
        // Verify timeline entry
        let carePlanID = try carePlan.requireID()
        let timelineItems = try await TimelineItem.query(on: app.db)
            .filter(\.$carePlan.$id == carePlanID)
            .all()
        
        XCTAssertEqual(timelineItems.count, 1)
        
        let timelineItem = timelineItems.first!
        XCTAssertEqual(timelineItem.status, "created")
        XCTAssertEqual(timelineItem.title, "Goal Created")
        XCTAssertTrue(timelineItem.desc.contains("Goal was created: Test Goal"))
        
        // Verify metadata
        XCTAssertEqual(timelineItem.meta?.data["ref_id"], try goal.requireID().uuidString)
        XCTAssertEqual(timelineItem.meta?.data["entity_type"], "Goal")
        XCTAssertEqual(timelineItem.meta?.data["operation"], "created")
        XCTAssertEqual(timelineItem.meta?.data["care_plan_id"], try carePlan.requireID().uuidString)
    }
    
    func testTimelineOperationTypes() async throws {
        // Test all operation types
        let operations: [TimelineOperation] = [.created, .updated, .deleted]
        
        for operation in operations {
            XCTAssertEqual(operation.status, operation.rawValue)
            
            let title = operation.title(for: "Test Entity")
            let description = operation.description(for: "Test Entity", details: "test details")
            
            switch operation {
            case .created:
                XCTAssertEqual(title, "Test Entity Created")
                XCTAssertEqual(description, "Test Entity was created: test details")
            case .updated:
                XCTAssertEqual(title, "Test Entity Updated")
                XCTAssertEqual(description, "Test Entity was updated: test details")
            case .deleted:
                XCTAssertEqual(title, "Test Entity Deleted")
                XCTAssertEqual(description, "Test Entity was deleted: test details")
            }
        }
    }
    
    func testTimelineMetadataStructure() async throws {
        // Create test data
        let member = Member()
        member.email = "<EMAIL>"
        member.firstName = "Test"
        member.lastName = "User"
        member.type = "member"
        member.roles = ["patient"]
        member.dob = "1990-01-01"
        member.status = "active"
        try await member.save(on: app.db)
        
        let carePlan = CarePlan()
        carePlan.$member.id = try member.requireID()
        carePlan.startDate = Date()
        carePlan.status = "active"
        try await carePlan.save(on: app.db)
        
        let problem = Problem()
        problem.$carePlan.id = try carePlan.requireID()
        problem.description = "Test Problem"
        problem.status = "active"
        problem.dateIdentified = Date()
        problem.source = "care team"
        try await problem.save(on: app.db)
        
        // Create timeline entry
        try await CarePlanTimelineService.createProblemTimeline(
            operation: .updated,
            problem: problem,
            on: app.db
        )
        
        // Verify metadata structure
        let timelineItems = try await TimelineItem.query(on: app.db).all()
        XCTAssertEqual(timelineItems.count, 1)

        guard let timelineItem = timelineItems.first else {
            XCTFail("No timeline item found")
            return
        }

        guard let metadata = timelineItem.meta else {
            XCTFail("Timeline item has no metadata")
            return
        }
        
        // Verify all required metadata fields are present
        XCTAssertNotNil(metadata.data["ref_id"])
        XCTAssertNotNil(metadata.data["entity_type"])
        XCTAssertNotNil(metadata.data["operation"])
        XCTAssertNotNil(metadata.data["care_plan_id"])
        
        // Verify metadata values
        XCTAssertEqual(metadata.data["ref_id"], try problem.requireID().uuidString)
        XCTAssertEqual(metadata.data["entity_type"], "Problem")
        XCTAssertEqual(metadata.data["operation"], "updated")
        XCTAssertEqual(metadata.data["care_plan_id"], try carePlan.requireID().uuidString)
    }
}
